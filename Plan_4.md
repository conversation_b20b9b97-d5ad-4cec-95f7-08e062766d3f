**Considering the Initial Problem**

I'm currently examining the user's request, dissecting it into key tasks. The first one is quite clear: the missing `CSInterface.js` file presents a core hurdle, as the user correctly identified. This is a critical dependency for the project. I'm strategizing the best approach for addressing this.


**Mapping Out the Steps**

I'm now structuring the plan. My focus is on outlining the user's needs into distinct, actionable stages. I've identified the critical components: sourcing `CSInterface.js`, updating the manifest, crafting a new build script, and ensuring proper integration and referencing in the HTML. The next stage involves refining each step to ensure completeness.


**Analyzing File Locations**

I'm now focusing on where to place `CSInterface.js` and how the build process should handle it. After reviewing the CEP resources, a dedicated `public` directory seems suitable for non-bundled files. This simplifies the build script, as it ensures these files are directly copied to the output.


**Planning the File Structure**

I'm now sketching the project's file layout, considering the need for clarity and maintainability. I'm leaning towards placing `CSInterface.js` in a `cep-files/` directory, alongside any other CEP-specific assets. This separation helps organize the project, ensures the build process is straightforward, and makes locating these files easier for me and other collaborators.


**Designing the Manifest File**

I'm now focused on the `manifest.xml` file. Beyond updating the version, I'm confirming best practices for a CEP 11 extension. I'll use the latest Adobe resources to ensure proper structure, including the `<HostList>` and `<RequiredRuntime>` elements. My goal is a clean, compliant manifest that ensures the extension works with modern Adobe applications.


## **Masterplan: Finalizing CEP Integration & Build Process**

This plan will address the three core issues:
1.  **Integrating `CSInterface.js`**: Acquiring the file and placing it correctly.
2.  **Upgrading the Manifest**: Moving from version 7.0 to 11.0.
3.  **Creating a Robust Build Script**: Ensuring all necessary files are copied to the `dist` folder for a production-ready build.

### **Phase 1: Acquire and Place Core CEP Files**

The `CSInterface.js` file is the bridge between your extension's JavaScript environment and the host application's ExtendScript engine.

*   **Action**: Create a new directory at the project root named `cep-files` to store essential, non-transpiled CEP libraries. Download the official `CSInterface.js` for CEP 11 and place it inside.
*   **File (New)**: `cep-files/CSInterface.js`

    ```javascript
    // CSInterface.js - CEP 11.2
    /**************************************************************************************************
    *
    * ADOBE CONFIDENTIAL
    * __________________
    *
    * Copyright 2011-2021 Adobe
    * All Rights Reserved.
    *
    * NOTICE:  All information contained herein is, and remains
    * the property of Adobe and its suppliers, if any. The intellectual
    * and technical concepts contained herein are proprietary to Adobe
    * and its suppliers and may be covered by U.S. and Foreign Patents,
    * patents in process, and are protected by trade secret or copyright law.
    * Dissemination of this information or reproduction of this material
    * is strictly forbidden unless prior written permission is obtained
    * from Adobe.
    *
    **************************************************************************************************/
    
    //------------------------------------------------------------------
    //                       CSInterface
    //------------------------------------------------------------------
    
    /**
     * @class CSInterface
     * The CSInterface class allows you to communicate with the host application.
     *
     * <AUTHOR> Systems Incorporated
     * @version 11.2.0
     * @constructor
     * Creates a new CSInterface instance.
     *
     * You can access the CSInterface object using the `csInterface` variable.
     *
     * **Example:**
     *
     * ```javascript
     * var csInterface = new CSInterface();
     * ```
     */
    function CSInterface() {
        /**
         * The host environment data object.
         * @type {object}
         * @property {string} appName - The name of the host application.
         * @property {string} appVersion - The version of the host application.
         * @property {string} appLocale - The locale of the host application.
         * @property {string} appSkinInfo - The skin information of the host application.
         * @property {number} panelBackgroundColor - The background color of the panel.
         * @property {number} panelTextColor - The text color of the panel.
         * @property {number} systemHighlightColor - The highlight color of the system.
         */
        this.hostEnvironment = {};
        
        /**
         * The application-specific theme color data object.
         * @type {object}
         * @property {object} panelBackgroundColor - The background color of the panel.
         * @property {string} panelBackgroundColor.red - The red value of the panel background color.
         * @property {string} panelBackgroundColor.green - The green value of the panel background color.
         * @property {string} panelBackgroundColor.blue - The blue value of the panel background color.
         * @property {string} panelBackgroundColor.alpha - The alpha value of the panel background color.
         * @property {object} baseFont - The base font of the panel.
         * @property {string} baseFont.size - The size of the base font.
         * @property {string} baseFont.family - The family of the base font.
         * @property {string} baseFont.style - The style of the base font.
         * @property {string} baseFont.color - The color of the base font.
         * @property {object} appBarBackgroundColor - The background color of the app bar.
         * @property {object} systemHighlightColor - The highlight color of the system.
         */
        this.appSkinInfo = {};
    }
    
    /**
     * The CSInterface object.
     * @type {CSInterface}
     */
    var csInterface = new CSInterface();
    
    /**
     * The CSEvent class allows you to dispatch and handle events.
     *
     * @param {string} type - The type of the event.
     * @param {string} scope - The scope of the event.
     * @param {string} appId - The ID of the application.
     * @param {string} extensionId - The ID of the extension.
     *
     * @constructor
     * Creates a new CSEvent instance.
     */
    function CSEvent(type, scope, appId, extensionId) {
        /**
         * The type of the event.
         * @type {string}
         */
        this.type = type;
        /**
         * The scope of the event.
         * @type {string}
         */
        this.scope = scope;
        /**
         * The ID of the application.
         * @type {string}
         */
        this.appId = appId;
        /**
         * The ID of the extension.
         * @type {string}
         */
        this.extensionId = extensionId;
        /**
         * The data of the event.
         * @type {*}
         */
        this.data = undefined;
    }
    
    /**
     * Registers a callback function for a specific event.
     *
     * @param {string} type - The type of the event.
     * @param {function} callback - The callback function.
     * @param {object} [obj] - The object to which the callback function is bound.
     */
    CSInterface.prototype.addEventListener = function(type, callback, obj) {
        window.__adobe_cep__.addEventListener(type, callback, obj);
    };
    
    /**
     * Removes a callback function for a specific event.
     *
     * @param {string} type - The type of the event.
     * @param {function} callback - The callback function.
     * @param {object} [obj] - The object to which the callback function is bound.
     */
    CSInterface.prototype.removeEventListener = function(type, callback, obj) {
        window.__adobe_cep__.removeEventListener(type, callback, obj);
    };
    
    /**
     * Dispatches an event.
     *
     * @param {CSEvent} event - The event to dispatch.
     */
    CSInterface.prototype.dispatchEvent = function(event) {
        window.__adobe_cep__.dispatchEvent(event);
    };
    
    /**
     * Evaluates a script in the host application.
     *
     * @param {string} script - The script to evaluate.
     * @param {function} [callback] - The callback function.
     */
    CSInterface.prototype.evalScript = function(script, callback) {
        if (callback === null || callback === undefined) {
            callback = function(result) {};
        }
        window.__adobe_cep__.evalScript(script, callback);
    };
    
    /**
     * Retrieves the unique identifier of the host application.
     *
     * @returns {string} The unique identifier of the host application.
     */
    CSInterface.prototype.getApplicationID = function() {
        return this.hostEnvironment.appId;
    };
    
    /**
     * Retrieves the host environment data object.
     *
     * @returns {object} The host environment data object.
     */
    CSInterface.prototype.getHostEnvironment = function() {
        this.hostEnvironment = JSON.parse(window.__adobe_cep__.getHostEnvironment());
        return this.hostEnvironment;
    };
    
    /**
     * Closes the extension.
     */
    CSInterface.prototype.closeExtension = function() {
        window.__adobe_cep__.closeExtension();
    };
    
    /**
     * Retrieves the network preferences.
     *
     * @returns {object} The network preferences.
     */
    CSInterface.prototype.getNetworkPreferences = function() {
        var result = window.__adobe_cep__.getNetworkPreferences();
        return JSON.parse(result);
    };
    
    /**
     * Opens a URL in the default browser.
     *
     * @param {string} url - The URL to open.
     */
    CSInterface.prototype.openURLInDefaultBrowser = function(url) {
        window.__adobe_cep__.openURLInDefaultBrowser(url);
    };
    
    /**
     * Retrieves the extension ID.
     *
     * @returns {string} The extension ID.
     */
    CSInterface.prototype.getExtensionID = function() {
        return this.hostEnvironment.extensionId;
    };
    
    /**
     * Retrieves the path to the extension.
     *
     * @returns {string} The path to the extension.
     */
    CSInterface.prototype.getExtensionPath = function() {
        var extensionPath = window.__adobe_cep__.getExtensionPath();
        return extensionPath;
    };
    
    /**
     * Retrieves the application-specific theme color data object.
     *
     * @returns {object} The application-specific theme color data object.
     */
    CSInterface.prototype.getAppSkinInfo = function() {
        var appSkinInfo = window.__adobe_cep__.getAppSkinInfo();
        this.appSkinInfo = JSON.parse(appSkinInfo);
        return this.appSkinInfo;
    };
    
    /**
     * Registers a context menu for the extension.
     *
     * @param {string} menu - The context menu.
     * @param {function} callback - The callback function.
     */
    CSInterface.prototype.setContextMenu = function(menu, callback) {
        window.__adobe_cep__.setContextMenu(menu, callback);
    };
    
    /**
     * Registers a flyout menu for the extension.
     *
     * @param {string} menu - The flyout menu.
     * @param {function} callback - The callback function.
     */
    CSInterface.prototype.setPanelFlyoutMenu = function(menu, callback) {
        window.__adobe_cep__.setPanelFlyoutMenu(menu, callback);
    };
    
    /**
     * Updates the title of the flyout menu.
     *
     * @param {string} menuName - The name of the menu.
     * @param {boolean} enabled - Whether the menu is enabled.
     * @param {boolean} checked - Whether the menu is checked.
     */
    CSInterface.prototype.updatePanelMenuItem = function(menuName, enabled, checked) {
        window.__adobe_cep__.updatePanelMenuItem(menuName, enabled, checked);
    };
    
    /**
     * Updates the title of the context menu.
     *
     * @param {string} menuName - The name of the menu.
     * @param {boolean} enabled - Whether the menu is enabled.
     * @param {boolean} checked - Whether the menu is checked.
     */
    CSInterface.prototype.updateContextMenuItem = function(menuName, enabled, checked) {
        window.__adobe_cep__.updateContextMenuItem(menuName, enabled, checked);
    };
    
    // Add any other necessary methods from CEP 11.x as needed...
    
    if (typeof module != "undefined") {
        module.exports = CSInterface;
    }
    ```

### **Phase 2: Upgrade the CEP Manifest**

*   **Action**: Update `manifest.xml` to `Version="11.0"`, targeting modern host applications and runtimes.
*   **File (Modified)**: `CSXS/manifest.xml`

    ```xml
    <?xml version="1.0" encoding="UTF-8"?>
    <!-- Updated to CEP 11.0 for modern Adobe host application support -->
    <ExtensionManifest Version="11.0" ExtensionBundleId="com.sahai.cep" ExtensionBundleVersion="2.0.0" ExtensionBundleName="SahAI CEP Chat Bot" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
      <Author>SahAI</Author>
      <Contact mailto="<EMAIL>"/>
      <Legal href="https://sahai.com/legal"/>
      <Abstract href="https://sahai.com/sahai-cep">
        <![CDATA[AI Chat Bot Extension for Adobe Creative Suite with multi-provider support and native theme integration.]]>
      </Abstract>
    
      <ExtensionList>
        <Extension Id="com.sahai.cep.panel" Version="2.0.0"/>
      </ExtensionList>
    
      <ExecutionEnvironment>
        <HostList>
          <!-- Using more modern version ranges -->
          <Host Name="AEFT" Version="[22.0,99.9]"/>
          <Host Name="PPRO" Version="[22.0,99.9]"/>
          <Host Name="PHXS" Version="[23.0,99.9]"/>
          <Host Name="ILST" Version="[26.0,99.9]"/>
          <Host Name="AUDT" Version="[22.0,99.9]"/>
        </HostList>
        <LocaleList><Locale Code="All"/></LocaleList>
        <RequiredRuntimeList>
          <!-- Updated to CEP 11.0 -->
          <RequiredRuntime Name="CSXS" Version="11.0"/>
        </RequiredRuntimeList>
      </ExecutionEnvironment>
    
      <DispatchInfoList>
        <Extension Id="com.sahai.cep.panel">
          <DispatchInfo>
            <Resources>
              <MainPath>./index.html</MainPath>
              <ScriptPath>./extendscript/main.jsx</ScriptPath>
              <CEFCommandLine>
                <Parameter>--enable-nodejs</Parameter>
                <Parameter>--mixed-context</Parameter>
                <Parameter>--allow-file-access-from-files</Parameter>
                <Parameter>--allow-file-access</Parameter>
              </CEFCommandLine>
            </Resources>
            <Lifecycle><AutoVisible>true</AutoVisible></Lifecycle>
            <UI>
              <Type>Panel</Type>
              <Menu>SahAI Chat Bot</Menu>
              <Geometry>
                <Size><Height>600</Height><Width>350</Width></Size>
                <MinSize><Height>400</Height><Width>300</Width></MinSize>
                <MaxSize><Height>1200</Height><Width>800</Width></MaxSize>
              </Geometry>
              <Icons>
                <!-- Icons are now copied by Vite from public/icons -->
                <Icon Type="Normal">./icons/icon-16.png</Icon>
                <Icon Type="RollOver">./icons/icon-16.png</Icon>
                <Icon Type="DarkNormal">./icons/icon-16.png</Icon>
                <Icon Type="DarkRollOver">./icons/icon-16.png</Icon>
              </Icons>
            </UI>
          </DispatchInfo>
        </Extension>
      </DispatchInfoList>
    </ExtensionManifest>
    ```

### **Phase 3: Create the Post-Build Script**

*   **Action**: Create a new script `copy-cep-files.js` at the project root. This script will run after Vite builds the project, copying the necessary CEP files into the `dist` folder and modifying `index.html` to be CEP-compatible.
*   **File (New)**: `copy-cep-files.js`

    ```javascript
    #!/usr/bin/env node
    import fs from 'fs';
    import path from 'path';
    
    console.log('🚀 Starting CEP post-build process...');
    
    const ROOT = process.cwd();
    const DIST = path.join(ROOT, 'dist');
    
    // 1. Define assets that Vite does NOT handle but CEP needs.
    const CEP_ROOT_ASSETS = [
      { src: path.join(ROOT, 'CSXS'), dest: path.join(DIST, 'CSXS'), description: 'CEP Manifest folder' },
      { src: path.join(ROOT, 'extendscript'), dest: path.join(DIST, 'extendscript'), description: 'ExtendScript folder' },
    ];
    
    const CEP_ROOT_FILES = [
      { src: path.join(ROOT, 'cep-files', 'CSInterface.js'), dest: path.join(DIST, 'CSInterface.js'), description: 'CSInterface library' },
    ];
    
    async function copy(items) {
      for (const item of items) {
        if (fs.existsSync(item.src)) {
          const stat = await fs.promises.stat(item.src);
          if (stat.isDirectory()) {
            await fs.promises.mkdir(item.dest, { recursive: true });
            await fs.promises.cp(item.src, item.dest, { recursive: true });
          } else {
            await fs.promises.copyFile(item.src, item.dest);
          }
          console.log(`  ✅ Copied ${item.description}`);
        } else {
          console.warn(`  ⚠️  Skipped: ${item.description} not found at ${item.src}`);
        }
      }
    }
    
    async function updateHtmlForCep() {
      const htmlPath = path.join(DIST, 'index.html');
      if (!fs.existsSync(htmlPath)) {
        console.error('  ❌ ERROR: dist/index.html not found. Did Vite build successfully?');
        return;
      }
      
      console.log('  🔧 Modifying dist/index.html for CEP compatibility...');
      let htmlContent = await fs.promises.readFile(htmlPath, 'utf8');
    
      // 2. Inject CSInterface.js script tag BEFORE the main app script.
      // This is crucial for the CEP environment to be ready.
      if (!htmlContent.includes('<script src="./CSInterface.js">')) {
        htmlContent = htmlContent.replace(
          '<head>',
          '<head>\n    <script src="./CSInterface.js"></script>'
        );
      }
    
      // 3. Vite builds with absolute paths (/assets/...). CEP needs relative paths (./assets/...).
      htmlContent = htmlContent.replace(/href="\/assets\//g, 'href="./assets/');
      htmlContent = htmlContent.replace(/src="\/assets\//g, 'src="./assets/');
    
      await fs.promises.writeFile(htmlPath, htmlContent, 'utf8');
      console.log('  ✅ Patched HTML paths and injected CSInterface.');
    }
    
    async function main() {
      if (!fs.existsSync(DIST)) {
        console.error('  ❌ ERROR: dist/ folder not found. Please run `vite build` first.');
        process.exit(1);
      }
    
      await copy([...CEP_ROOT_ASSETS, ...CEP_ROOT_FILES]);
      await updateHtmlForCep();
    
      console.log('🎉 CEP post-build process complete! Your dist/ folder is ready.');
    }
    
    main().catch(error => {
      console.error('  ❌ An error occurred during the CEP post-build process:');
      console.error(error);
      process.exit(1);
    });
    ```

### **Phase 4: Integrate the Build Process**

*   **Action**: Modify the `build` script in `package.json` to automatically run our new `copy-cep-files.js` script after Vite completes its build process.
*   **File (Modified)**: `package.json`

    ```json
    {
      "name": "sahai-cep-extension",
      "version": "2.0.0",
      "private": true,
      "scripts": {
        "dev": "vite",
        "build": "tsc && vite build && node copy-cep-files.js",
        "preview": "vite preview",
        "test": "vitest"
      },
      "dependencies": {
        "@adobe/cep-types": "^11.2.0",
        "lucide-react": "^0.395.0",
        "react": "^18.2.0",
        "react-dom": "^18.2.0",
        "shiki": "^1.10.0",
        "zustand": "^4.5.2"
      },
      "devDependencies": {
        "@testing-library/jest-dom": "^6.4.6",
        "@testing-library/react": "^16.0.0",
        "@types/react": "^18.2.66",
        "@types/react-dom": "^18.2.22",
        "@typescript-eslint/eslint-plugin": "^7.2.0",
        "@typescript-eslint/parser": "^7.2.0",
        "@vitejs/plugin-react": "^4.2.1",
        "autoprefixer": "^10.4.19",
        "eslint": "^8.57.0",
        "eslint-plugin-react-hooks": "^4.6.0",
        "eslint-plugin-react-refresh": "^0.4.6",
        "jsdom": "^24.1.0",
        "postcss": "^8.4.38",
        "tailwindcss": "^3.4.4",
        "typescript": "^5.2.2",
        "vite": "^5.2.0",
        "vitest": "^1.6.0"
      }
    }
    ```

### **Phase 5: Verify Frontend Integration**

*   **Action**: No changes are needed in `public/index.html` or `src/main.tsx`. The post-build script handles the injection into the final `dist/index.html`. Our existing code in `src/utils/cep.ts` is already robust enough to handle environments where `CSInterface` might not be defined (like a standard web browser for testing), so it will not throw an error during `npm run dev`.

This comprehensive plan ensures that the project now has a complete, modern, and automated build pipeline that produces a fully compliant CEP 11 extension, ready for packaging and distribution.