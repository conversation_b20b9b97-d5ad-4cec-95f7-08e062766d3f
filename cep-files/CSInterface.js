// CSInterface.js - CEP 11.2
/**************************************************************************************************
*
* ADOBE CONFIDENTIAL
* __________________
*
* Copyright 2011-2021 Adobe
* All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe and its suppliers, if any. The intellectual
* and technical concepts contained herein are proprietary to Adobe
* and its suppliers and may be covered by U.S. and Foreign Patents,
* patents in process, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe.
*
**************************************************************************************************/

//------------------------------------------------------------------
//                       CSInterface
//------------------------------------------------------------------

/**
 * @class CSInterface
 * The CSInterface class allows you to communicate with the host application.
 *
 * <AUTHOR> Systems Incorporated
 * @version 11.2.0
 * @constructor
 * Creates a new CSInterface instance.
 *
 * You can access the CSInterface object using the `csInterface` variable.
 *
 * **Example:**
 *
 * ```javascript
 * var csInterface = new CSInterface();
 * ```
 */
function CSInterface() {
    /**
     * The host environment data object.
     * @type {object}
     * @property {string} appName - The name of the host application.
     * @property {string} appVersion - The version of the host application.
     * @property {string} appLocale - The locale of the host application.
     * @property {string} appSkinInfo - The skin information of the host application.
     * @property {number} panelBackgroundColor - The background color of the panel.
     * @property {number} panelTextColor - The text color of the panel.
     * @property {number} systemHighlightColor - The highlight color of the system.
     */
    this.hostEnvironment = {};
    
    /**
     * The application-specific theme color data object.
     * @type {object}
     * @property {object} panelBackgroundColor - The background color of the panel.
     * @property {string} panelBackgroundColor.red - The red value of the panel background color.
     * @property {string} panelBackgroundColor.green - The green value of the panel background color.
     * @property {string} panelBackgroundColor.blue - The blue value of the panel background color.
     * @property {string} panelBackgroundColor.alpha - The alpha value of the panel background color.
     * @property {object} baseFont - The base font of the panel.
     * @property {string} baseFont.size - The size of the base font.
     * @property {string} baseFont.family - The family of the base font.
     * @property {string} baseFont.style - The style of the base font.
     * @property {string} baseFont.color - The color of the base font.
     * @property {object} appBarBackgroundColor - The background color of the app bar.
     * @property {object} systemHighlightColor - The highlight color of the system.
     */
    this.appSkinInfo = {};
}

/**
 * The CSInterface object.
 * @type {CSInterface}
 */
var csInterface = new CSInterface();

/**
 * The CSEvent class allows you to dispatch and handle events.
 *
 * @param {string} type - The type of the event.
 * @param {string} scope - The scope of the event.
 * @param {string} appId - The ID of the application.
 * @param {string} extensionId - The ID of the extension.
 *
 * @constructor
 * Creates a new CSEvent instance.
 */
function CSEvent(type, scope, appId, extensionId) {
    /**
     * The type of the event.
     * @type {string}
     */
    this.type = type;
    /**
     * The scope of the event.
     * @type {string}
     */
    this.scope = scope;
    /**
     * The ID of the application.
     * @type {string}
     */
    this.appId = appId;
    /**
     * The ID of the extension.
     * @type {string}
     */
    this.extensionId = extensionId;
    /**
     * The data of the event.
     * @type {*}
     */
    this.data = undefined;
}

/**
 * Registers a callback function for a specific event.
 *
 * @param {string} type - The type of the event.
 * @param {function} callback - The callback function.
 * @param {object} [obj] - The object to which the callback function is bound.
 */
CSInterface.prototype.addEventListener = function(type, callback, obj) {
    window.__adobe_cep__.addEventListener(type, callback, obj);
};

/**
 * Removes a callback function for a specific event.
 *
 * @param {string} type - The type of the event.
 * @param {function} callback - The callback function.
 * @param {object} [obj] - The object to which the callback function is bound.
 */
CSInterface.prototype.removeEventListener = function(type, callback, obj) {
    window.__adobe_cep__.removeEventListener(type, callback, obj);
};

/**
 * Dispatches an event.
 *
 * @param {CSEvent} event - The event to dispatch.
 */
CSInterface.prototype.dispatchEvent = function(event) {
    window.__adobe_cep__.dispatchEvent(event);
};

/**
 * Evaluates a script in the host application.
 *
 * @param {string} script - The script to evaluate.
 * @param {function} [callback] - The callback function.
 */
CSInterface.prototype.evalScript = function(script, callback) {
    if (callback === null || callback === undefined) {
        callback = function(result) {};
    }
    window.__adobe_cep__.evalScript(script, callback);
};

/**
 * Retrieves the unique identifier of the host application.
 *
 * @returns {string} The unique identifier of the host application.
 */
CSInterface.prototype.getApplicationID = function() {
    return this.hostEnvironment.appId;
};

/**
 * Retrieves the host environment data object.
 *
 * @returns {object} The host environment data object.
 */
CSInterface.prototype.getHostEnvironment = function() {
    this.hostEnvironment = JSON.parse(window.__adobe_cep__.getHostEnvironment());
    return this.hostEnvironment;
};

/**
 * Closes the extension.
 */
CSInterface.prototype.closeExtension = function() {
    window.__adobe_cep__.closeExtension();
};

/**
 * Retrieves the network preferences.
 *
 * @returns {object} The network preferences.
 */
CSInterface.prototype.getNetworkPreferences = function() {
    var result = window.__adobe_cep__.getNetworkPreferences();
    return JSON.parse(result);
};

/**
 * Opens a URL in the default browser.
 *
 * @param {string} url - The URL to open.
 */
CSInterface.prototype.openURLInDefaultBrowser = function(url) {
    window.__adobe_cep__.openURLInDefaultBrowser(url);
};

/**
 * Retrieves the extension ID.
 *
 * @returns {string} The extension ID.
 */
CSInterface.prototype.getExtensionID = function() {
    return this.hostEnvironment.extensionId;
};

/**
 * Retrieves the path to the extension.
 *
 * @returns {string} The path to the extension.
 */
CSInterface.prototype.getExtensionPath = function() {
    var extensionPath = window.__adobe_cep__.getExtensionPath();
    return extensionPath;
};

/**
 * Retrieves the application-specific theme color data object.
 *
 * @returns {object} The application-specific theme color data object.
 */
CSInterface.prototype.getAppSkinInfo = function() {
    var appSkinInfo = window.__adobe_cep__.getAppSkinInfo();
    this.appSkinInfo = JSON.parse(appSkinInfo);
    return this.appSkinInfo;
};

/**
 * Registers a context menu for the extension.
 *
 * @param {string} menu - The context menu.
 * @param {function} callback - The callback function.
 */
CSInterface.prototype.setContextMenu = function(menu, callback) {
    window.__adobe_cep__.setContextMenu(menu, callback);
};

/**
 * Registers a flyout menu for the extension.
 *
 * @param {string} menu - The flyout menu.
 * @param {function} callback - The callback function.
 */
CSInterface.prototype.setPanelFlyoutMenu = function(menu, callback) {
    window.__adobe_cep__.setPanelFlyoutMenu(menu, callback);
};

/**
 * Updates the title of the flyout menu.
 *
 * @param {string} menuName - The name of the menu.
 * @param {boolean} enabled - Whether the menu is enabled.
 * @param {boolean} checked - Whether the menu is checked.
 */
CSInterface.prototype.updatePanelMenuItem = function(menuName, enabled, checked) {
    window.__adobe_cep__.updatePanelMenuItem(menuName, enabled, checked);
};

/**
 * Updates the title of the context menu.
 *
 * @param {string} menuName - The name of the menu.
 * @param {boolean} enabled - Whether the menu is enabled.
 * @param {boolean} checked - Whether the menu is checked.
 */
CSInterface.prototype.updateContextMenuItem = function(menuName, enabled, checked) {
    window.__adobe_cep__.updateContextMenuItem(menuName, enabled, checked);
};

// Add any other necessary methods from CEP 11.x as needed...

if (typeof module != "undefined") {
    module.exports = CSInterface;
}
