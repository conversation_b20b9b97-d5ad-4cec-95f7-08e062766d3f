#!/usr/bin/env node
import fs from 'fs';
import path from 'path';

console.log('🚀 Starting CEP post-build process...');

const ROOT = process.cwd();
const DIST = path.join(ROOT, 'dist');

// 1. Define assets that Vite does NOT handle but CEP needs.
const CEP_ROOT_ASSETS = [
  { src: path.join(ROOT, 'CSXS'), dest: path.join(DIST, 'CSXS'), description: 'CEP Manifest folder' },
  { src: path.join(ROOT, 'extendscript'), dest: path.join(DIST, 'extendscript'), description: 'ExtendScript folder' },
];

const CEP_ROOT_FILES = [
  { src: path.join(ROOT, 'cep-files', 'CSInterface.js'), dest: path.join(DIST, 'CSInterface.js'), description: 'CSInterface library' },
];

async function copy(items) {
  for (const item of items) {
    if (fs.existsSync(item.src)) {
      const stat = await fs.promises.stat(item.src);
      if (stat.isDirectory()) {
        await fs.promises.mkdir(item.dest, { recursive: true });
        await fs.promises.cp(item.src, item.dest, { recursive: true });
      } else {
        await fs.promises.copyFile(item.src, item.dest);
      }
      console.log(`  ✅ Copied ${item.description}`);
    } else {
      console.warn(`  ⚠️  Skipped: ${item.description} not found at ${item.src}`);
    }
  }
}

async function updateHtmlForCep() {
  const htmlPath = path.join(DIST, 'index.html');
  if (!fs.existsSync(htmlPath)) {
    console.error('  ❌ ERROR: dist/index.html not found. Did Vite build successfully?');
    return;
  }

  console.log('  🔧 Modifying dist/index.html for CEP compatibility...');
  let htmlContent = await fs.promises.readFile(htmlPath, 'utf8');

  // 2. Inject CSInterface.js script tag BEFORE the main app script.
  // This is crucial for the CEP environment to be ready.
  if (!htmlContent.includes('<script src="./CSInterface.js">')) {
    htmlContent = htmlContent.replace(
      '<head>',
      '<head>\n    <script src="./CSInterface.js"></script>'
    );
  }

  // 3. Vite builds with absolute paths (/assets/...). CEP needs relative paths (./assets/...).
  htmlContent = htmlContent.replace(/href="\/assets\//g, 'href="./assets/');
  htmlContent = htmlContent.replace(/src="\/assets\//g, 'src="./assets/');

  await fs.promises.writeFile(htmlPath, htmlContent, 'utf8');
  console.log('  ✅ Patched HTML paths and injected CSInterface.');
}

async function main() {
  if (!fs.existsSync(DIST)) {
    console.error('  ❌ ERROR: dist/ folder not found. Please run `vite build` first.');
    process.exit(1);
  }

  await copy([...CEP_ROOT_ASSETS, ...CEP_ROOT_FILES]);
  await updateHtmlForCep();

  console.log('🎉 CEP post-build process complete! Your dist/ folder is ready.');
}

main().catch(error => {
  console.error('  ❌ An error occurred during the CEP post-build process:');
  console.error(error);
  process.exit(1);
});
