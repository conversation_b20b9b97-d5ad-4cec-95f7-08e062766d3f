/// <reference types="vitest" />
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  build: {
    outDir: 'dist',
    // Acknowledge that the Shiki chunk will be large.
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        // Group all shiki-related files (languages, themes, wasm) into a single chunk.
        // This prevents the dozens of small files seen in the build log.
        manualChunks: (id) => {
          if (id.includes('node_modules/shiki/')) {
            return 'shiki';
          }
        },
      },
    },
  },
  publicDir: 'public',
  base: './',
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: './src/test/setup.ts',
  },
})
