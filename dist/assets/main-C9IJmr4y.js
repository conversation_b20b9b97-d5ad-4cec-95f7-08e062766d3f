const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./angular-html-LfdN0zeE.js","./html-C2L_23MC.js","./javascript-ySlJ1b_l.js","./css-BPhBrDlE.js","./angular-ts-CKsD7JZE.js","./scss-C31hgJw-.js","./apl-BBq3IX1j.js","./xml-e3z08dGr.js","./java-xI-RfyKK.js","./json-BQoSv7ci.js","./astro-CqkE3fuf.js","./typescript-Dj6nwHGl.js","./postcss-B3ZDOciz.js","./blade-a8OxSdnT.js","./sql-COK4E0Yg.js","./bsl-Dgyn0ogV.js","./sdbl-BLhTXw86.js","./cairo--RitsXJZ.js","./python-DhUJRlN_.js","./cobol-PTqiYgYu.js","./coffee-dyiR41kL.js","./cpp-BksuvNSY.js","./regexp-DWJ3fJO_.js","./glsl-DBO2IWDn.js","./c-C3t2pwGQ.js","./crystal-DtDmRg-F.js","./shellscript-atvbtKCR.js","./edge-D5gP-w-T.js","./html-derivative-CSfWNPLT.js","./elixir-CLiX3zqd.js","./elm-CmHSxxaM.js","./erb-BYTLMnw6.js","./ruby-DeZ3UC14.js","./haml-B2EZWmdv.js","./graphql-cDcHW_If.js","./jsx-BAng5TT0.js","./tsx-B6W0miNI.js","./lua-CvWAzNxB.js","./yaml-CVw76BM1.js","./fortran-fixed-form-TqA4NnZg.js","./fortran-free-form-DKXYxT9g.js","./fsharp-XplgxFYe.js","./markdown-UIAJJxZW.js","./gdresource-BHYsBjWJ.js","./gdshader-SKMF96pI.js","./gdscript-DfxzS6Rs.js","./git-commit-i4q6IMui.js","./diff-BgYniUM_.js","./git-rebase-B-v9cOL2.js","./glimmer-js-D-cwc0-E.js","./glimmer-ts-pgjy16dm.js","./hack-D1yCygmZ.js","./handlebars-BQGss363.js","./http-FRrOvY1W.js","./hxml-TIA70rKU.js","./haxe-C5wWYbrZ.js","./imba-bv_oIlVt.js","./jinja-DGy0s7-h.js","./jison-BqZprYcd.js","./julia-BBuGR-5E.js","./r-CwjWoCRV.js","./latex-C-cWTeAZ.js","./tex-rYs2v40G.js","./liquid-D3W5UaiH.js","./marko-z0MBrx5-.js","./less-BfCpw3nA.js","./mdc-DB_EDNY_.js","./nginx-D_VnBJ67.js","./nim-ZlGxZxc3.js","./perl-CHQXSrWU.js","./php-B5ebYQev.js","./pug-CM9l7STV.js","./qml-D8XfuvdV.js","./razor-CNLDkMZG.js","./csharp-D9R-vmeu.js","./rst-4NLicBqY.js","./cmake-DbXoA79R.js","./sas-BmTFh92c.js","./shaderlab-B7qAK45m.js","./hlsl-ifBTmRxC.js","./shellsession-C_rIy8kc.js","./soy-C-lX7w71.js","./sparql-bYkjHRlG.js","./turtle-BMR_PYu6.js","./stata-DorPZHa4.js","./svelte-MSaWC3Je.js","./templ-dwX3ZSMB.js","./go-B1SYOhNW.js","./ts-tags-CipyTH0X.js","./twig-NC5TFiHP.js","./vue-BuYVFjOK.js","./vue-html-xdeiXROB.js","./xsl-Dd0NUgwM.js"])))=>i.map(i=>d[i]);
var bp=Object.defineProperty;var Vp=(e,t,n)=>t in e?bp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var k=(e,t,n)=>Vp(e,typeof t!="symbol"?t+"":t,n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&r(l)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();function vc(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ec={exports:{}},wi={},Sc={exports:{}},b={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hr=Symbol.for("react.element"),zp=Symbol.for("react.portal"),Up=Symbol.for("react.fragment"),$p=Symbol.for("react.strict_mode"),Bp=Symbol.for("react.profiler"),Fp=Symbol.for("react.provider"),Hp=Symbol.for("react.context"),Gp=Symbol.for("react.forward_ref"),Wp=Symbol.for("react.suspense"),Kp=Symbol.for("react.memo"),Qp=Symbol.for("react.lazy"),ja=Symbol.iterator;function qp(e){return e===null||typeof e!="object"?null:(e=ja&&e[ja]||e["@@iterator"],typeof e=="function"?e:null)}var wc={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},kc=Object.assign,xc={};function Wn(e,t,n){this.props=e,this.context=t,this.refs=xc,this.updater=n||wc}Wn.prototype.isReactComponent={};Wn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Wn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Cc(){}Cc.prototype=Wn.prototype;function As(e,t,n){this.props=e,this.context=t,this.refs=xc,this.updater=n||wc}var Is=As.prototype=new Cc;Is.constructor=As;kc(Is,Wn.prototype);Is.isPureReactComponent=!0;var Ma=Array.isArray,Rc=Object.prototype.hasOwnProperty,Ns={current:null},Pc={key:!0,ref:!0,__self:!0,__source:!0};function Tc(e,t,n){var r,o={},i=null,l=null;if(t!=null)for(r in t.ref!==void 0&&(l=t.ref),t.key!==void 0&&(i=""+t.key),t)Rc.call(t,r)&&!Pc.hasOwnProperty(r)&&(o[r]=t[r]);var s=arguments.length-2;if(s===1)o.children=n;else if(1<s){for(var a=Array(s),u=0;u<s;u++)a[u]=arguments[u+2];o.children=a}if(e&&e.defaultProps)for(r in s=e.defaultProps,s)o[r]===void 0&&(o[r]=s[r]);return{$$typeof:Hr,type:e,key:i,ref:l,props:o,_owner:Ns.current}}function Xp(e,t){return{$$typeof:Hr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Ds(e){return typeof e=="object"&&e!==null&&e.$$typeof===Hr}function Yp(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var ba=/\/+/g;function Wi(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Yp(""+e.key):t.toString(36)}function Po(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var l=!1;if(e===null)l=!0;else switch(i){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case Hr:case zp:l=!0}}if(l)return l=e,o=o(l),e=r===""?"."+Wi(l,0):r,Ma(o)?(n="",e!=null&&(n=e.replace(ba,"$&/")+"/"),Po(o,t,n,"",function(u){return u})):o!=null&&(Ds(o)&&(o=Xp(o,n+(!o.key||l&&l.key===o.key?"":(""+o.key).replace(ba,"$&/")+"/")+e)),t.push(o)),1;if(l=0,r=r===""?".":r+":",Ma(e))for(var s=0;s<e.length;s++){i=e[s];var a=r+Wi(i,s);l+=Po(i,t,n,a,o)}else if(a=qp(e),typeof a=="function")for(e=a.call(e),s=0;!(i=e.next()).done;)i=i.value,a=r+Wi(i,s++),l+=Po(i,t,n,a,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function ro(e,t,n){if(e==null)return e;var r=[],o=0;return Po(e,r,"","",function(i){return t.call(n,i,o++)}),r}function Jp(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ke={current:null},To={transition:null},Zp={ReactCurrentDispatcher:ke,ReactCurrentBatchConfig:To,ReactCurrentOwner:Ns};function Lc(){throw Error("act(...) is not supported in production builds of React.")}b.Children={map:ro,forEach:function(e,t,n){ro(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ro(e,function(){t++}),t},toArray:function(e){return ro(e,function(t){return t})||[]},only:function(e){if(!Ds(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};b.Component=Wn;b.Fragment=Up;b.Profiler=Bp;b.PureComponent=As;b.StrictMode=$p;b.Suspense=Wp;b.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Zp;b.act=Lc;b.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=kc({},e.props),o=e.key,i=e.ref,l=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,l=Ns.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(a in t)Rc.call(t,a)&&!Pc.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&s!==void 0?s[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){s=Array(a);for(var u=0;u<a;u++)s[u]=arguments[u+2];r.children=s}return{$$typeof:Hr,type:e.type,key:o,ref:i,props:r,_owner:l}};b.createContext=function(e){return e={$$typeof:Hp,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Fp,_context:e},e.Consumer=e};b.createElement=Tc;b.createFactory=function(e){var t=Tc.bind(null,e);return t.type=e,t};b.createRef=function(){return{current:null}};b.forwardRef=function(e){return{$$typeof:Gp,render:e}};b.isValidElement=Ds;b.lazy=function(e){return{$$typeof:Qp,_payload:{_status:-1,_result:e},_init:Jp}};b.memo=function(e,t){return{$$typeof:Kp,type:e,compare:t===void 0?null:t}};b.startTransition=function(e){var t=To.transition;To.transition={};try{e()}finally{To.transition=t}};b.unstable_act=Lc;b.useCallback=function(e,t){return ke.current.useCallback(e,t)};b.useContext=function(e){return ke.current.useContext(e)};b.useDebugValue=function(){};b.useDeferredValue=function(e){return ke.current.useDeferredValue(e)};b.useEffect=function(e,t){return ke.current.useEffect(e,t)};b.useId=function(){return ke.current.useId()};b.useImperativeHandle=function(e,t,n){return ke.current.useImperativeHandle(e,t,n)};b.useInsertionEffect=function(e,t){return ke.current.useInsertionEffect(e,t)};b.useLayoutEffect=function(e,t){return ke.current.useLayoutEffect(e,t)};b.useMemo=function(e,t){return ke.current.useMemo(e,t)};b.useReducer=function(e,t,n){return ke.current.useReducer(e,t,n)};b.useRef=function(e){return ke.current.useRef(e)};b.useState=function(e){return ke.current.useState(e)};b.useSyncExternalStore=function(e,t,n){return ke.current.useSyncExternalStore(e,t,n)};b.useTransition=function(){return ke.current.useTransition()};b.version="18.3.1";Sc.exports=b;var I=Sc.exports;const Oc=vc(I);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var em=I,tm=Symbol.for("react.element"),nm=Symbol.for("react.fragment"),rm=Object.prototype.hasOwnProperty,om=em.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,im={key:!0,ref:!0,__self:!0,__source:!0};function Ac(e,t,n){var r,o={},i=null,l=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(l=t.ref);for(r in t)rm.call(t,r)&&!im.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:tm,type:e,key:i,ref:l,props:o,_owner:om.current}}wi.Fragment=nm;wi.jsx=Ac;wi.jsxs=Ac;Ec.exports=wi;var v=Ec.exports,Pl={},Ic={exports:{}},Me={},Nc={exports:{}},Dc={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(O,D){var M=O.length;O.push(D);e:for(;0<M;){var Y=M-1>>>1,ie=O[Y];if(0<o(ie,D))O[Y]=D,O[M]=ie,M=Y;else break e}}function n(O){return O.length===0?null:O[0]}function r(O){if(O.length===0)return null;var D=O[0],M=O.pop();if(M!==D){O[0]=M;e:for(var Y=0,ie=O.length,to=ie>>>1;Y<to;){var Kt=2*(Y+1)-1,Gi=O[Kt],Qt=Kt+1,no=O[Qt];if(0>o(Gi,M))Qt<ie&&0>o(no,Gi)?(O[Y]=no,O[Qt]=M,Y=Qt):(O[Y]=Gi,O[Kt]=M,Y=Kt);else if(Qt<ie&&0>o(no,M))O[Y]=no,O[Qt]=M,Y=Qt;else break e}}return D}function o(O,D){var M=O.sortIndex-D.sortIndex;return M!==0?M:O.id-D.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var l=Date,s=l.now();e.unstable_now=function(){return l.now()-s}}var a=[],u=[],d=1,f=null,m=3,g=!1,_=!1,E=!1,S=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,c=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(O){for(var D=n(u);D!==null;){if(D.callback===null)r(u);else if(D.startTime<=O)r(u),D.sortIndex=D.expirationTime,t(a,D);else break;D=n(u)}}function w(O){if(E=!1,y(O),!_)if(n(a)!==null)_=!0,Wt(x);else{var D=n(u);D!==null&&Hi(w,D.startTime-O)}}function x(O,D){_=!1,E&&(E=!1,p(A),A=-1),g=!0;var M=m;try{for(y(D),f=n(a);f!==null&&(!(f.expirationTime>D)||O&&!ye());){var Y=f.callback;if(typeof Y=="function"){f.callback=null,m=f.priorityLevel;var ie=Y(f.expirationTime<=D);D=e.unstable_now(),typeof ie=="function"?f.callback=ie:f===n(a)&&r(a),y(D)}else r(a);f=n(a)}if(f!==null)var to=!0;else{var Kt=n(u);Kt!==null&&Hi(w,Kt.startTime-D),to=!1}return to}finally{f=null,m=M,g=!1}}var T=!1,P=null,A=-1,$=5,N=-1;function ye(){return!(e.unstable_now()-N<$)}function St(){if(P!==null){var O=e.unstable_now();N=O;var D=!0;try{D=P(!0,O)}finally{D?wt():(T=!1,P=null)}}else T=!1}var wt;if(typeof c=="function")wt=function(){c(St)};else if(typeof MessageChannel<"u"){var Ve=new MessageChannel,kt=Ve.port2;Ve.port1.onmessage=St,wt=function(){kt.postMessage(null)}}else wt=function(){S(St,0)};function Wt(O){P=O,T||(T=!0,wt())}function Hi(O,D){A=S(function(){O(e.unstable_now())},D)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(O){O.callback=null},e.unstable_continueExecution=function(){_||g||(_=!0,Wt(x))},e.unstable_forceFrameRate=function(O){0>O||125<O?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):$=0<O?Math.floor(1e3/O):5},e.unstable_getCurrentPriorityLevel=function(){return m},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(O){switch(m){case 1:case 2:case 3:var D=3;break;default:D=m}var M=m;m=D;try{return O()}finally{m=M}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(O,D){switch(O){case 1:case 2:case 3:case 4:case 5:break;default:O=3}var M=m;m=O;try{return D()}finally{m=M}},e.unstable_scheduleCallback=function(O,D,M){var Y=e.unstable_now();switch(typeof M=="object"&&M!==null?(M=M.delay,M=typeof M=="number"&&0<M?Y+M:Y):M=Y,O){case 1:var ie=-1;break;case 2:ie=250;break;case 5:ie=**********;break;case 4:ie=1e4;break;default:ie=5e3}return ie=M+ie,O={id:d++,callback:D,priorityLevel:O,startTime:M,expirationTime:ie,sortIndex:-1},M>Y?(O.sortIndex=M,t(u,O),n(a)===null&&O===n(u)&&(E?(p(A),A=-1):E=!0,Hi(w,M-Y))):(O.sortIndex=ie,t(a,O),_||g||(_=!0,Wt(x))),O},e.unstable_shouldYield=ye,e.unstable_wrapCallback=function(O){var D=m;return function(){var M=m;m=D;try{return O.apply(this,arguments)}finally{m=M}}}})(Dc);Nc.exports=Dc;var lm=Nc.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sm=I,je=lm;function C(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var jc=new Set,wr={};function dn(e,t){bn(e,t),bn(e+"Capture",t)}function bn(e,t){for(wr[e]=t,e=0;e<t.length;e++)jc.add(t[e])}var gt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Tl=Object.prototype.hasOwnProperty,am=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Va={},za={};function um(e){return Tl.call(za,e)?!0:Tl.call(Va,e)?!1:am.test(e)?za[e]=!0:(Va[e]=!0,!1)}function cm(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function dm(e,t,n,r){if(t===null||typeof t>"u"||cm(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function xe(e,t,n,r,o,i,l){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=l}var fe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){fe[e]=new xe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];fe[t]=new xe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){fe[e]=new xe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){fe[e]=new xe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){fe[e]=new xe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){fe[e]=new xe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){fe[e]=new xe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){fe[e]=new xe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){fe[e]=new xe(e,5,!1,e.toLowerCase(),null,!1,!1)});var js=/[\-:]([a-z])/g;function Ms(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(js,Ms);fe[t]=new xe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(js,Ms);fe[t]=new xe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(js,Ms);fe[t]=new xe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){fe[e]=new xe(e,1,!1,e.toLowerCase(),null,!1,!1)});fe.xlinkHref=new xe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){fe[e]=new xe(e,1,!1,e.toLowerCase(),null,!0,!0)});function bs(e,t,n,r){var o=fe.hasOwnProperty(t)?fe[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(dm(t,n,o,r)&&(n=null),r||o===null?um(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Et=sm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,oo=Symbol.for("react.element"),gn=Symbol.for("react.portal"),_n=Symbol.for("react.fragment"),Vs=Symbol.for("react.strict_mode"),Ll=Symbol.for("react.profiler"),Mc=Symbol.for("react.provider"),bc=Symbol.for("react.context"),zs=Symbol.for("react.forward_ref"),Ol=Symbol.for("react.suspense"),Al=Symbol.for("react.suspense_list"),Us=Symbol.for("react.memo"),Ct=Symbol.for("react.lazy"),Vc=Symbol.for("react.offscreen"),Ua=Symbol.iterator;function Jn(e){return e===null||typeof e!="object"?null:(e=Ua&&e[Ua]||e["@@iterator"],typeof e=="function"?e:null)}var q=Object.assign,Ki;function sr(e){if(Ki===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ki=t&&t[1]||""}return`
`+Ki+e}var Qi=!1;function qi(e,t){if(!e||Qi)return"";Qi=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),l=o.length-1,s=i.length-1;1<=l&&0<=s&&o[l]!==i[s];)s--;for(;1<=l&&0<=s;l--,s--)if(o[l]!==i[s]){if(l!==1||s!==1)do if(l--,s--,0>s||o[l]!==i[s]){var a=`
`+o[l].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=l&&0<=s);break}}}finally{Qi=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?sr(e):""}function fm(e){switch(e.tag){case 5:return sr(e.type);case 16:return sr("Lazy");case 13:return sr("Suspense");case 19:return sr("SuspenseList");case 0:case 2:case 15:return e=qi(e.type,!1),e;case 11:return e=qi(e.type.render,!1),e;case 1:return e=qi(e.type,!0),e;default:return""}}function Il(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case _n:return"Fragment";case gn:return"Portal";case Ll:return"Profiler";case Vs:return"StrictMode";case Ol:return"Suspense";case Al:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case bc:return(e.displayName||"Context")+".Consumer";case Mc:return(e._context.displayName||"Context")+".Provider";case zs:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Us:return t=e.displayName||null,t!==null?t:Il(e.type)||"Memo";case Ct:t=e._payload,e=e._init;try{return Il(e(t))}catch{}}return null}function pm(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Il(t);case 8:return t===Vs?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function zt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function zc(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function mm(e){var t=zc(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(l){r=""+l,i.call(this,l)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(l){r=""+l},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function io(e){e._valueTracker||(e._valueTracker=mm(e))}function Uc(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=zc(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Bo(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Nl(e,t){var n=t.checked;return q({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function $a(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=zt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function $c(e,t){t=t.checked,t!=null&&bs(e,"checked",t,!1)}function Dl(e,t){$c(e,t);var n=zt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?jl(e,t.type,n):t.hasOwnProperty("defaultValue")&&jl(e,t.type,zt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ba(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function jl(e,t,n){(t!=="number"||Bo(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var ar=Array.isArray;function Tn(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+zt(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Ml(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(C(91));return q({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Fa(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(C(92));if(ar(n)){if(1<n.length)throw Error(C(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:zt(n)}}function Bc(e,t){var n=zt(t.value),r=zt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Ha(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Fc(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function bl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Fc(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var lo,Hc=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(lo=lo||document.createElement("div"),lo.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=lo.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function kr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var fr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},hm=["Webkit","ms","Moz","O"];Object.keys(fr).forEach(function(e){hm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),fr[t]=fr[e]})});function Gc(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||fr.hasOwnProperty(e)&&fr[e]?(""+t).trim():t+"px"}function Wc(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Gc(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var gm=q({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Vl(e,t){if(t){if(gm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(C(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(C(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(C(61))}if(t.style!=null&&typeof t.style!="object")throw Error(C(62))}}function zl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ul=null;function $s(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var $l=null,Ln=null,On=null;function Ga(e){if(e=Kr(e)){if(typeof $l!="function")throw Error(C(280));var t=e.stateNode;t&&(t=Pi(t),$l(e.stateNode,e.type,t))}}function Kc(e){Ln?On?On.push(e):On=[e]:Ln=e}function Qc(){if(Ln){var e=Ln,t=On;if(On=Ln=null,Ga(e),t)for(e=0;e<t.length;e++)Ga(t[e])}}function qc(e,t){return e(t)}function Xc(){}var Xi=!1;function Yc(e,t,n){if(Xi)return e(t,n);Xi=!0;try{return qc(e,t,n)}finally{Xi=!1,(Ln!==null||On!==null)&&(Xc(),Qc())}}function xr(e,t){var n=e.stateNode;if(n===null)return null;var r=Pi(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(C(231,t,typeof n));return n}var Bl=!1;if(gt)try{var Zn={};Object.defineProperty(Zn,"passive",{get:function(){Bl=!0}}),window.addEventListener("test",Zn,Zn),window.removeEventListener("test",Zn,Zn)}catch{Bl=!1}function _m(e,t,n,r,o,i,l,s,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(d){this.onError(d)}}var pr=!1,Fo=null,Ho=!1,Fl=null,ym={onError:function(e){pr=!0,Fo=e}};function vm(e,t,n,r,o,i,l,s,a){pr=!1,Fo=null,_m.apply(ym,arguments)}function Em(e,t,n,r,o,i,l,s,a){if(vm.apply(this,arguments),pr){if(pr){var u=Fo;pr=!1,Fo=null}else throw Error(C(198));Ho||(Ho=!0,Fl=u)}}function fn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Jc(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Wa(e){if(fn(e)!==e)throw Error(C(188))}function Sm(e){var t=e.alternate;if(!t){if(t=fn(e),t===null)throw Error(C(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Wa(o),e;if(i===r)return Wa(o),t;i=i.sibling}throw Error(C(188))}if(n.return!==r.return)n=o,r=i;else{for(var l=!1,s=o.child;s;){if(s===n){l=!0,n=o,r=i;break}if(s===r){l=!0,r=o,n=i;break}s=s.sibling}if(!l){for(s=i.child;s;){if(s===n){l=!0,n=i,r=o;break}if(s===r){l=!0,r=i,n=o;break}s=s.sibling}if(!l)throw Error(C(189))}}if(n.alternate!==r)throw Error(C(190))}if(n.tag!==3)throw Error(C(188));return n.stateNode.current===n?e:t}function Zc(e){return e=Sm(e),e!==null?ed(e):null}function ed(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=ed(e);if(t!==null)return t;e=e.sibling}return null}var td=je.unstable_scheduleCallback,Ka=je.unstable_cancelCallback,wm=je.unstable_shouldYield,km=je.unstable_requestPaint,J=je.unstable_now,xm=je.unstable_getCurrentPriorityLevel,Bs=je.unstable_ImmediatePriority,nd=je.unstable_UserBlockingPriority,Go=je.unstable_NormalPriority,Cm=je.unstable_LowPriority,rd=je.unstable_IdlePriority,ki=null,it=null;function Rm(e){if(it&&typeof it.onCommitFiberRoot=="function")try{it.onCommitFiberRoot(ki,e,void 0,(e.current.flags&128)===128)}catch{}}var Ye=Math.clz32?Math.clz32:Lm,Pm=Math.log,Tm=Math.LN2;function Lm(e){return e>>>=0,e===0?32:31-(Pm(e)/Tm|0)|0}var so=64,ao=4194304;function ur(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Wo(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,l=n&268435455;if(l!==0){var s=l&~o;s!==0?r=ur(s):(i&=l,i!==0&&(r=ur(i)))}else l=n&~o,l!==0?r=ur(l):i!==0&&(r=ur(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ye(t),o=1<<n,r|=e[n],t&=~o;return r}function Om(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Am(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var l=31-Ye(i),s=1<<l,a=o[l];a===-1?(!(s&n)||s&r)&&(o[l]=Om(s,t)):a<=t&&(e.expiredLanes|=s),i&=~s}}function Hl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function od(){var e=so;return so<<=1,!(so&4194240)&&(so=64),e}function Yi(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Gr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ye(t),e[t]=n}function Im(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-Ye(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function Fs(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ye(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var z=0;function id(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var ld,Hs,sd,ad,ud,Gl=!1,uo=[],At=null,It=null,Nt=null,Cr=new Map,Rr=new Map,Pt=[],Nm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Qa(e,t){switch(e){case"focusin":case"focusout":At=null;break;case"dragenter":case"dragleave":It=null;break;case"mouseover":case"mouseout":Nt=null;break;case"pointerover":case"pointerout":Cr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Rr.delete(t.pointerId)}}function er(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=Kr(t),t!==null&&Hs(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function Dm(e,t,n,r,o){switch(t){case"focusin":return At=er(At,e,t,n,r,o),!0;case"dragenter":return It=er(It,e,t,n,r,o),!0;case"mouseover":return Nt=er(Nt,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return Cr.set(i,er(Cr.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,Rr.set(i,er(Rr.get(i)||null,e,t,n,r,o)),!0}return!1}function cd(e){var t=Yt(e.target);if(t!==null){var n=fn(t);if(n!==null){if(t=n.tag,t===13){if(t=Jc(n),t!==null){e.blockedOn=t,ud(e.priority,function(){sd(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Lo(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Wl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Ul=r,n.target.dispatchEvent(r),Ul=null}else return t=Kr(n),t!==null&&Hs(t),e.blockedOn=n,!1;t.shift()}return!0}function qa(e,t,n){Lo(e)&&n.delete(t)}function jm(){Gl=!1,At!==null&&Lo(At)&&(At=null),It!==null&&Lo(It)&&(It=null),Nt!==null&&Lo(Nt)&&(Nt=null),Cr.forEach(qa),Rr.forEach(qa)}function tr(e,t){e.blockedOn===t&&(e.blockedOn=null,Gl||(Gl=!0,je.unstable_scheduleCallback(je.unstable_NormalPriority,jm)))}function Pr(e){function t(o){return tr(o,e)}if(0<uo.length){tr(uo[0],e);for(var n=1;n<uo.length;n++){var r=uo[n];r.blockedOn===e&&(r.blockedOn=null)}}for(At!==null&&tr(At,e),It!==null&&tr(It,e),Nt!==null&&tr(Nt,e),Cr.forEach(t),Rr.forEach(t),n=0;n<Pt.length;n++)r=Pt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Pt.length&&(n=Pt[0],n.blockedOn===null);)cd(n),n.blockedOn===null&&Pt.shift()}var An=Et.ReactCurrentBatchConfig,Ko=!0;function Mm(e,t,n,r){var o=z,i=An.transition;An.transition=null;try{z=1,Gs(e,t,n,r)}finally{z=o,An.transition=i}}function bm(e,t,n,r){var o=z,i=An.transition;An.transition=null;try{z=4,Gs(e,t,n,r)}finally{z=o,An.transition=i}}function Gs(e,t,n,r){if(Ko){var o=Wl(e,t,n,r);if(o===null)sl(e,t,r,Qo,n),Qa(e,r);else if(Dm(o,e,t,n,r))r.stopPropagation();else if(Qa(e,r),t&4&&-1<Nm.indexOf(e)){for(;o!==null;){var i=Kr(o);if(i!==null&&ld(i),i=Wl(e,t,n,r),i===null&&sl(e,t,r,Qo,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else sl(e,t,r,null,n)}}var Qo=null;function Wl(e,t,n,r){if(Qo=null,e=$s(r),e=Yt(e),e!==null)if(t=fn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Jc(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Qo=e,null}function dd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(xm()){case Bs:return 1;case nd:return 4;case Go:case Cm:return 16;case rd:return 536870912;default:return 16}default:return 16}}var Lt=null,Ws=null,Oo=null;function fd(){if(Oo)return Oo;var e,t=Ws,n=t.length,r,o="value"in Lt?Lt.value:Lt.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var l=n-e;for(r=1;r<=l&&t[n-r]===o[i-r];r++);return Oo=o.slice(e,1<r?1-r:void 0)}function Ao(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function co(){return!0}function Xa(){return!1}function be(e){function t(n,r,o,i,l){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=l,this.currentTarget=null;for(var s in e)e.hasOwnProperty(s)&&(n=e[s],this[s]=n?n(i):i[s]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?co:Xa,this.isPropagationStopped=Xa,this}return q(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=co)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=co)},persist:function(){},isPersistent:co}),t}var Kn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ks=be(Kn),Wr=q({},Kn,{view:0,detail:0}),Vm=be(Wr),Ji,Zi,nr,xi=q({},Wr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Qs,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==nr&&(nr&&e.type==="mousemove"?(Ji=e.screenX-nr.screenX,Zi=e.screenY-nr.screenY):Zi=Ji=0,nr=e),Ji)},movementY:function(e){return"movementY"in e?e.movementY:Zi}}),Ya=be(xi),zm=q({},xi,{dataTransfer:0}),Um=be(zm),$m=q({},Wr,{relatedTarget:0}),el=be($m),Bm=q({},Kn,{animationName:0,elapsedTime:0,pseudoElement:0}),Fm=be(Bm),Hm=q({},Kn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Gm=be(Hm),Wm=q({},Kn,{data:0}),Ja=be(Wm),Km={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Qm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},qm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Xm(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=qm[e])?!!t[e]:!1}function Qs(){return Xm}var Ym=q({},Wr,{key:function(e){if(e.key){var t=Km[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ao(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Qm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Qs,charCode:function(e){return e.type==="keypress"?Ao(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ao(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Jm=be(Ym),Zm=q({},xi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Za=be(Zm),eh=q({},Wr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Qs}),th=be(eh),nh=q({},Kn,{propertyName:0,elapsedTime:0,pseudoElement:0}),rh=be(nh),oh=q({},xi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ih=be(oh),lh=[9,13,27,32],qs=gt&&"CompositionEvent"in window,mr=null;gt&&"documentMode"in document&&(mr=document.documentMode);var sh=gt&&"TextEvent"in window&&!mr,pd=gt&&(!qs||mr&&8<mr&&11>=mr),eu=" ",tu=!1;function md(e,t){switch(e){case"keyup":return lh.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function hd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var yn=!1;function ah(e,t){switch(e){case"compositionend":return hd(t);case"keypress":return t.which!==32?null:(tu=!0,eu);case"textInput":return e=t.data,e===eu&&tu?null:e;default:return null}}function uh(e,t){if(yn)return e==="compositionend"||!qs&&md(e,t)?(e=fd(),Oo=Ws=Lt=null,yn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return pd&&t.locale!=="ko"?null:t.data;default:return null}}var ch={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function nu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!ch[e.type]:t==="textarea"}function gd(e,t,n,r){Kc(r),t=qo(t,"onChange"),0<t.length&&(n=new Ks("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var hr=null,Tr=null;function dh(e){Pd(e,0)}function Ci(e){var t=Sn(e);if(Uc(t))return e}function fh(e,t){if(e==="change")return t}var _d=!1;if(gt){var tl;if(gt){var nl="oninput"in document;if(!nl){var ru=document.createElement("div");ru.setAttribute("oninput","return;"),nl=typeof ru.oninput=="function"}tl=nl}else tl=!1;_d=tl&&(!document.documentMode||9<document.documentMode)}function ou(){hr&&(hr.detachEvent("onpropertychange",yd),Tr=hr=null)}function yd(e){if(e.propertyName==="value"&&Ci(Tr)){var t=[];gd(t,Tr,e,$s(e)),Yc(dh,t)}}function ph(e,t,n){e==="focusin"?(ou(),hr=t,Tr=n,hr.attachEvent("onpropertychange",yd)):e==="focusout"&&ou()}function mh(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ci(Tr)}function hh(e,t){if(e==="click")return Ci(t)}function gh(e,t){if(e==="input"||e==="change")return Ci(t)}function _h(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ze=typeof Object.is=="function"?Object.is:_h;function Lr(e,t){if(Ze(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Tl.call(t,o)||!Ze(e[o],t[o]))return!1}return!0}function iu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function lu(e,t){var n=iu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=iu(n)}}function vd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?vd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Ed(){for(var e=window,t=Bo();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Bo(e.document)}return t}function Xs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function yh(e){var t=Ed(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&vd(n.ownerDocument.documentElement,n)){if(r!==null&&Xs(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=lu(n,i);var l=lu(n,r);o&&l&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var vh=gt&&"documentMode"in document&&11>=document.documentMode,vn=null,Kl=null,gr=null,Ql=!1;function su(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ql||vn==null||vn!==Bo(r)||(r=vn,"selectionStart"in r&&Xs(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),gr&&Lr(gr,r)||(gr=r,r=qo(Kl,"onSelect"),0<r.length&&(t=new Ks("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=vn)))}function fo(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var En={animationend:fo("Animation","AnimationEnd"),animationiteration:fo("Animation","AnimationIteration"),animationstart:fo("Animation","AnimationStart"),transitionend:fo("Transition","TransitionEnd")},rl={},Sd={};gt&&(Sd=document.createElement("div").style,"AnimationEvent"in window||(delete En.animationend.animation,delete En.animationiteration.animation,delete En.animationstart.animation),"TransitionEvent"in window||delete En.transitionend.transition);function Ri(e){if(rl[e])return rl[e];if(!En[e])return e;var t=En[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Sd)return rl[e]=t[n];return e}var wd=Ri("animationend"),kd=Ri("animationiteration"),xd=Ri("animationstart"),Cd=Ri("transitionend"),Rd=new Map,au="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function $t(e,t){Rd.set(e,t),dn(t,[e])}for(var ol=0;ol<au.length;ol++){var il=au[ol],Eh=il.toLowerCase(),Sh=il[0].toUpperCase()+il.slice(1);$t(Eh,"on"+Sh)}$t(wd,"onAnimationEnd");$t(kd,"onAnimationIteration");$t(xd,"onAnimationStart");$t("dblclick","onDoubleClick");$t("focusin","onFocus");$t("focusout","onBlur");$t(Cd,"onTransitionEnd");bn("onMouseEnter",["mouseout","mouseover"]);bn("onMouseLeave",["mouseout","mouseover"]);bn("onPointerEnter",["pointerout","pointerover"]);bn("onPointerLeave",["pointerout","pointerover"]);dn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));dn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));dn("onBeforeInput",["compositionend","keypress","textInput","paste"]);dn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));dn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));dn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var cr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),wh=new Set("cancel close invalid load scroll toggle".split(" ").concat(cr));function uu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Em(r,t,void 0,e),e.currentTarget=null}function Pd(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var l=r.length-1;0<=l;l--){var s=r[l],a=s.instance,u=s.currentTarget;if(s=s.listener,a!==i&&o.isPropagationStopped())break e;uu(o,s,u),i=a}else for(l=0;l<r.length;l++){if(s=r[l],a=s.instance,u=s.currentTarget,s=s.listener,a!==i&&o.isPropagationStopped())break e;uu(o,s,u),i=a}}}if(Ho)throw e=Fl,Ho=!1,Fl=null,e}function H(e,t){var n=t[Zl];n===void 0&&(n=t[Zl]=new Set);var r=e+"__bubble";n.has(r)||(Td(t,e,2,!1),n.add(r))}function ll(e,t,n){var r=0;t&&(r|=4),Td(n,e,r,t)}var po="_reactListening"+Math.random().toString(36).slice(2);function Or(e){if(!e[po]){e[po]=!0,jc.forEach(function(n){n!=="selectionchange"&&(wh.has(n)||ll(n,!1,e),ll(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[po]||(t[po]=!0,ll("selectionchange",!1,t))}}function Td(e,t,n,r){switch(dd(t)){case 1:var o=Mm;break;case 4:o=bm;break;default:o=Gs}n=o.bind(null,t,n,e),o=void 0,!Bl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function sl(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var l=r.tag;if(l===3||l===4){var s=r.stateNode.containerInfo;if(s===o||s.nodeType===8&&s.parentNode===o)break;if(l===4)for(l=r.return;l!==null;){var a=l.tag;if((a===3||a===4)&&(a=l.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;l=l.return}for(;s!==null;){if(l=Yt(s),l===null)return;if(a=l.tag,a===5||a===6){r=i=l;continue e}s=s.parentNode}}r=r.return}Yc(function(){var u=i,d=$s(n),f=[];e:{var m=Rd.get(e);if(m!==void 0){var g=Ks,_=e;switch(e){case"keypress":if(Ao(n)===0)break e;case"keydown":case"keyup":g=Jm;break;case"focusin":_="focus",g=el;break;case"focusout":_="blur",g=el;break;case"beforeblur":case"afterblur":g=el;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=Ya;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=Um;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=th;break;case wd:case kd:case xd:g=Fm;break;case Cd:g=rh;break;case"scroll":g=Vm;break;case"wheel":g=ih;break;case"copy":case"cut":case"paste":g=Gm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=Za}var E=(t&4)!==0,S=!E&&e==="scroll",p=E?m!==null?m+"Capture":null:m;E=[];for(var c=u,y;c!==null;){y=c;var w=y.stateNode;if(y.tag===5&&w!==null&&(y=w,p!==null&&(w=xr(c,p),w!=null&&E.push(Ar(c,w,y)))),S)break;c=c.return}0<E.length&&(m=new g(m,_,null,n,d),f.push({event:m,listeners:E}))}}if(!(t&7)){e:{if(m=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",m&&n!==Ul&&(_=n.relatedTarget||n.fromElement)&&(Yt(_)||_[_t]))break e;if((g||m)&&(m=d.window===d?d:(m=d.ownerDocument)?m.defaultView||m.parentWindow:window,g?(_=n.relatedTarget||n.toElement,g=u,_=_?Yt(_):null,_!==null&&(S=fn(_),_!==S||_.tag!==5&&_.tag!==6)&&(_=null)):(g=null,_=u),g!==_)){if(E=Ya,w="onMouseLeave",p="onMouseEnter",c="mouse",(e==="pointerout"||e==="pointerover")&&(E=Za,w="onPointerLeave",p="onPointerEnter",c="pointer"),S=g==null?m:Sn(g),y=_==null?m:Sn(_),m=new E(w,c+"leave",g,n,d),m.target=S,m.relatedTarget=y,w=null,Yt(d)===u&&(E=new E(p,c+"enter",_,n,d),E.target=y,E.relatedTarget=S,w=E),S=w,g&&_)t:{for(E=g,p=_,c=0,y=E;y;y=mn(y))c++;for(y=0,w=p;w;w=mn(w))y++;for(;0<c-y;)E=mn(E),c--;for(;0<y-c;)p=mn(p),y--;for(;c--;){if(E===p||p!==null&&E===p.alternate)break t;E=mn(E),p=mn(p)}E=null}else E=null;g!==null&&cu(f,m,g,E,!1),_!==null&&S!==null&&cu(f,S,_,E,!0)}}e:{if(m=u?Sn(u):window,g=m.nodeName&&m.nodeName.toLowerCase(),g==="select"||g==="input"&&m.type==="file")var x=fh;else if(nu(m))if(_d)x=gh;else{x=mh;var T=ph}else(g=m.nodeName)&&g.toLowerCase()==="input"&&(m.type==="checkbox"||m.type==="radio")&&(x=hh);if(x&&(x=x(e,u))){gd(f,x,n,d);break e}T&&T(e,m,u),e==="focusout"&&(T=m._wrapperState)&&T.controlled&&m.type==="number"&&jl(m,"number",m.value)}switch(T=u?Sn(u):window,e){case"focusin":(nu(T)||T.contentEditable==="true")&&(vn=T,Kl=u,gr=null);break;case"focusout":gr=Kl=vn=null;break;case"mousedown":Ql=!0;break;case"contextmenu":case"mouseup":case"dragend":Ql=!1,su(f,n,d);break;case"selectionchange":if(vh)break;case"keydown":case"keyup":su(f,n,d)}var P;if(qs)e:{switch(e){case"compositionstart":var A="onCompositionStart";break e;case"compositionend":A="onCompositionEnd";break e;case"compositionupdate":A="onCompositionUpdate";break e}A=void 0}else yn?md(e,n)&&(A="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(A="onCompositionStart");A&&(pd&&n.locale!=="ko"&&(yn||A!=="onCompositionStart"?A==="onCompositionEnd"&&yn&&(P=fd()):(Lt=d,Ws="value"in Lt?Lt.value:Lt.textContent,yn=!0)),T=qo(u,A),0<T.length&&(A=new Ja(A,e,null,n,d),f.push({event:A,listeners:T}),P?A.data=P:(P=hd(n),P!==null&&(A.data=P)))),(P=sh?ah(e,n):uh(e,n))&&(u=qo(u,"onBeforeInput"),0<u.length&&(d=new Ja("onBeforeInput","beforeinput",null,n,d),f.push({event:d,listeners:u}),d.data=P))}Pd(f,t)})}function Ar(e,t,n){return{instance:e,listener:t,currentTarget:n}}function qo(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=xr(e,n),i!=null&&r.unshift(Ar(e,i,o)),i=xr(e,t),i!=null&&r.push(Ar(e,i,o))),e=e.return}return r}function mn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function cu(e,t,n,r,o){for(var i=t._reactName,l=[];n!==null&&n!==r;){var s=n,a=s.alternate,u=s.stateNode;if(a!==null&&a===r)break;s.tag===5&&u!==null&&(s=u,o?(a=xr(n,i),a!=null&&l.unshift(Ar(n,a,s))):o||(a=xr(n,i),a!=null&&l.push(Ar(n,a,s)))),n=n.return}l.length!==0&&e.push({event:t,listeners:l})}var kh=/\r\n?/g,xh=/\u0000|\uFFFD/g;function du(e){return(typeof e=="string"?e:""+e).replace(kh,`
`).replace(xh,"")}function mo(e,t,n){if(t=du(t),du(e)!==t&&n)throw Error(C(425))}function Xo(){}var ql=null,Xl=null;function Yl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Jl=typeof setTimeout=="function"?setTimeout:void 0,Ch=typeof clearTimeout=="function"?clearTimeout:void 0,fu=typeof Promise=="function"?Promise:void 0,Rh=typeof queueMicrotask=="function"?queueMicrotask:typeof fu<"u"?function(e){return fu.resolve(null).then(e).catch(Ph)}:Jl;function Ph(e){setTimeout(function(){throw e})}function al(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Pr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Pr(t)}function Dt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function pu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Qn=Math.random().toString(36).slice(2),ot="__reactFiber$"+Qn,Ir="__reactProps$"+Qn,_t="__reactContainer$"+Qn,Zl="__reactEvents$"+Qn,Th="__reactListeners$"+Qn,Lh="__reactHandles$"+Qn;function Yt(e){var t=e[ot];if(t)return t;for(var n=e.parentNode;n;){if(t=n[_t]||n[ot]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=pu(e);e!==null;){if(n=e[ot])return n;e=pu(e)}return t}e=n,n=e.parentNode}return null}function Kr(e){return e=e[ot]||e[_t],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Sn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(C(33))}function Pi(e){return e[Ir]||null}var es=[],wn=-1;function Bt(e){return{current:e}}function G(e){0>wn||(e.current=es[wn],es[wn]=null,wn--)}function F(e,t){wn++,es[wn]=e.current,e.current=t}var Ut={},_e=Bt(Ut),Pe=Bt(!1),on=Ut;function Vn(e,t){var n=e.type.contextTypes;if(!n)return Ut;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Te(e){return e=e.childContextTypes,e!=null}function Yo(){G(Pe),G(_e)}function mu(e,t,n){if(_e.current!==Ut)throw Error(C(168));F(_e,t),F(Pe,n)}function Ld(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(C(108,pm(e)||"Unknown",o));return q({},n,r)}function Jo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ut,on=_e.current,F(_e,e),F(Pe,Pe.current),!0}function hu(e,t,n){var r=e.stateNode;if(!r)throw Error(C(169));n?(e=Ld(e,t,on),r.__reactInternalMemoizedMergedChildContext=e,G(Pe),G(_e),F(_e,e)):G(Pe),F(Pe,n)}var at=null,Ti=!1,ul=!1;function Od(e){at===null?at=[e]:at.push(e)}function Oh(e){Ti=!0,Od(e)}function Ft(){if(!ul&&at!==null){ul=!0;var e=0,t=z;try{var n=at;for(z=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}at=null,Ti=!1}catch(o){throw at!==null&&(at=at.slice(e+1)),td(Bs,Ft),o}finally{z=t,ul=!1}}return null}var kn=[],xn=0,Zo=null,ei=0,Ue=[],$e=0,ln=null,ct=1,dt="";function qt(e,t){kn[xn++]=ei,kn[xn++]=Zo,Zo=e,ei=t}function Ad(e,t,n){Ue[$e++]=ct,Ue[$e++]=dt,Ue[$e++]=ln,ln=e;var r=ct;e=dt;var o=32-Ye(r)-1;r&=~(1<<o),n+=1;var i=32-Ye(t)+o;if(30<i){var l=o-o%5;i=(r&(1<<l)-1).toString(32),r>>=l,o-=l,ct=1<<32-Ye(t)+o|n<<o|r,dt=i+e}else ct=1<<i|n<<o|r,dt=e}function Ys(e){e.return!==null&&(qt(e,1),Ad(e,1,0))}function Js(e){for(;e===Zo;)Zo=kn[--xn],kn[xn]=null,ei=kn[--xn],kn[xn]=null;for(;e===ln;)ln=Ue[--$e],Ue[$e]=null,dt=Ue[--$e],Ue[$e]=null,ct=Ue[--$e],Ue[$e]=null}var De=null,Ne=null,W=!1,Xe=null;function Id(e,t){var n=Fe(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function gu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,De=e,Ne=Dt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,De=e,Ne=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=ln!==null?{id:ct,overflow:dt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Fe(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,De=e,Ne=null,!0):!1;default:return!1}}function ts(e){return(e.mode&1)!==0&&(e.flags&128)===0}function ns(e){if(W){var t=Ne;if(t){var n=t;if(!gu(e,t)){if(ts(e))throw Error(C(418));t=Dt(n.nextSibling);var r=De;t&&gu(e,t)?Id(r,n):(e.flags=e.flags&-4097|2,W=!1,De=e)}}else{if(ts(e))throw Error(C(418));e.flags=e.flags&-4097|2,W=!1,De=e}}}function _u(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;De=e}function ho(e){if(e!==De)return!1;if(!W)return _u(e),W=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Yl(e.type,e.memoizedProps)),t&&(t=Ne)){if(ts(e))throw Nd(),Error(C(418));for(;t;)Id(e,t),t=Dt(t.nextSibling)}if(_u(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(C(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ne=Dt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ne=null}}else Ne=De?Dt(e.stateNode.nextSibling):null;return!0}function Nd(){for(var e=Ne;e;)e=Dt(e.nextSibling)}function zn(){Ne=De=null,W=!1}function Zs(e){Xe===null?Xe=[e]:Xe.push(e)}var Ah=Et.ReactCurrentBatchConfig;function rr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(C(309));var r=n.stateNode}if(!r)throw Error(C(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(l){var s=o.refs;l===null?delete s[i]:s[i]=l},t._stringRef=i,t)}if(typeof e!="string")throw Error(C(284));if(!n._owner)throw Error(C(290,e))}return e}function go(e,t){throw e=Object.prototype.toString.call(t),Error(C(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function yu(e){var t=e._init;return t(e._payload)}function Dd(e){function t(p,c){if(e){var y=p.deletions;y===null?(p.deletions=[c],p.flags|=16):y.push(c)}}function n(p,c){if(!e)return null;for(;c!==null;)t(p,c),c=c.sibling;return null}function r(p,c){for(p=new Map;c!==null;)c.key!==null?p.set(c.key,c):p.set(c.index,c),c=c.sibling;return p}function o(p,c){return p=Vt(p,c),p.index=0,p.sibling=null,p}function i(p,c,y){return p.index=y,e?(y=p.alternate,y!==null?(y=y.index,y<c?(p.flags|=2,c):y):(p.flags|=2,c)):(p.flags|=1048576,c)}function l(p){return e&&p.alternate===null&&(p.flags|=2),p}function s(p,c,y,w){return c===null||c.tag!==6?(c=gl(y,p.mode,w),c.return=p,c):(c=o(c,y),c.return=p,c)}function a(p,c,y,w){var x=y.type;return x===_n?d(p,c,y.props.children,w,y.key):c!==null&&(c.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===Ct&&yu(x)===c.type)?(w=o(c,y.props),w.ref=rr(p,c,y),w.return=p,w):(w=Vo(y.type,y.key,y.props,null,p.mode,w),w.ref=rr(p,c,y),w.return=p,w)}function u(p,c,y,w){return c===null||c.tag!==4||c.stateNode.containerInfo!==y.containerInfo||c.stateNode.implementation!==y.implementation?(c=_l(y,p.mode,w),c.return=p,c):(c=o(c,y.children||[]),c.return=p,c)}function d(p,c,y,w,x){return c===null||c.tag!==7?(c=rn(y,p.mode,w,x),c.return=p,c):(c=o(c,y),c.return=p,c)}function f(p,c,y){if(typeof c=="string"&&c!==""||typeof c=="number")return c=gl(""+c,p.mode,y),c.return=p,c;if(typeof c=="object"&&c!==null){switch(c.$$typeof){case oo:return y=Vo(c.type,c.key,c.props,null,p.mode,y),y.ref=rr(p,null,c),y.return=p,y;case gn:return c=_l(c,p.mode,y),c.return=p,c;case Ct:var w=c._init;return f(p,w(c._payload),y)}if(ar(c)||Jn(c))return c=rn(c,p.mode,y,null),c.return=p,c;go(p,c)}return null}function m(p,c,y,w){var x=c!==null?c.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return x!==null?null:s(p,c,""+y,w);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case oo:return y.key===x?a(p,c,y,w):null;case gn:return y.key===x?u(p,c,y,w):null;case Ct:return x=y._init,m(p,c,x(y._payload),w)}if(ar(y)||Jn(y))return x!==null?null:d(p,c,y,w,null);go(p,y)}return null}function g(p,c,y,w,x){if(typeof w=="string"&&w!==""||typeof w=="number")return p=p.get(y)||null,s(c,p,""+w,x);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case oo:return p=p.get(w.key===null?y:w.key)||null,a(c,p,w,x);case gn:return p=p.get(w.key===null?y:w.key)||null,u(c,p,w,x);case Ct:var T=w._init;return g(p,c,y,T(w._payload),x)}if(ar(w)||Jn(w))return p=p.get(y)||null,d(c,p,w,x,null);go(c,w)}return null}function _(p,c,y,w){for(var x=null,T=null,P=c,A=c=0,$=null;P!==null&&A<y.length;A++){P.index>A?($=P,P=null):$=P.sibling;var N=m(p,P,y[A],w);if(N===null){P===null&&(P=$);break}e&&P&&N.alternate===null&&t(p,P),c=i(N,c,A),T===null?x=N:T.sibling=N,T=N,P=$}if(A===y.length)return n(p,P),W&&qt(p,A),x;if(P===null){for(;A<y.length;A++)P=f(p,y[A],w),P!==null&&(c=i(P,c,A),T===null?x=P:T.sibling=P,T=P);return W&&qt(p,A),x}for(P=r(p,P);A<y.length;A++)$=g(P,p,A,y[A],w),$!==null&&(e&&$.alternate!==null&&P.delete($.key===null?A:$.key),c=i($,c,A),T===null?x=$:T.sibling=$,T=$);return e&&P.forEach(function(ye){return t(p,ye)}),W&&qt(p,A),x}function E(p,c,y,w){var x=Jn(y);if(typeof x!="function")throw Error(C(150));if(y=x.call(y),y==null)throw Error(C(151));for(var T=x=null,P=c,A=c=0,$=null,N=y.next();P!==null&&!N.done;A++,N=y.next()){P.index>A?($=P,P=null):$=P.sibling;var ye=m(p,P,N.value,w);if(ye===null){P===null&&(P=$);break}e&&P&&ye.alternate===null&&t(p,P),c=i(ye,c,A),T===null?x=ye:T.sibling=ye,T=ye,P=$}if(N.done)return n(p,P),W&&qt(p,A),x;if(P===null){for(;!N.done;A++,N=y.next())N=f(p,N.value,w),N!==null&&(c=i(N,c,A),T===null?x=N:T.sibling=N,T=N);return W&&qt(p,A),x}for(P=r(p,P);!N.done;A++,N=y.next())N=g(P,p,A,N.value,w),N!==null&&(e&&N.alternate!==null&&P.delete(N.key===null?A:N.key),c=i(N,c,A),T===null?x=N:T.sibling=N,T=N);return e&&P.forEach(function(St){return t(p,St)}),W&&qt(p,A),x}function S(p,c,y,w){if(typeof y=="object"&&y!==null&&y.type===_n&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case oo:e:{for(var x=y.key,T=c;T!==null;){if(T.key===x){if(x=y.type,x===_n){if(T.tag===7){n(p,T.sibling),c=o(T,y.props.children),c.return=p,p=c;break e}}else if(T.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===Ct&&yu(x)===T.type){n(p,T.sibling),c=o(T,y.props),c.ref=rr(p,T,y),c.return=p,p=c;break e}n(p,T);break}else t(p,T);T=T.sibling}y.type===_n?(c=rn(y.props.children,p.mode,w,y.key),c.return=p,p=c):(w=Vo(y.type,y.key,y.props,null,p.mode,w),w.ref=rr(p,c,y),w.return=p,p=w)}return l(p);case gn:e:{for(T=y.key;c!==null;){if(c.key===T)if(c.tag===4&&c.stateNode.containerInfo===y.containerInfo&&c.stateNode.implementation===y.implementation){n(p,c.sibling),c=o(c,y.children||[]),c.return=p,p=c;break e}else{n(p,c);break}else t(p,c);c=c.sibling}c=_l(y,p.mode,w),c.return=p,p=c}return l(p);case Ct:return T=y._init,S(p,c,T(y._payload),w)}if(ar(y))return _(p,c,y,w);if(Jn(y))return E(p,c,y,w);go(p,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,c!==null&&c.tag===6?(n(p,c.sibling),c=o(c,y),c.return=p,p=c):(n(p,c),c=gl(y,p.mode,w),c.return=p,p=c),l(p)):n(p,c)}return S}var Un=Dd(!0),jd=Dd(!1),ti=Bt(null),ni=null,Cn=null,ea=null;function ta(){ea=Cn=ni=null}function na(e){var t=ti.current;G(ti),e._currentValue=t}function rs(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function In(e,t){ni=e,ea=Cn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Re=!0),e.firstContext=null)}function Ge(e){var t=e._currentValue;if(ea!==e)if(e={context:e,memoizedValue:t,next:null},Cn===null){if(ni===null)throw Error(C(308));Cn=e,ni.dependencies={lanes:0,firstContext:e}}else Cn=Cn.next=e;return t}var Jt=null;function ra(e){Jt===null?Jt=[e]:Jt.push(e)}function Md(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,ra(t)):(n.next=o.next,o.next=n),t.interleaved=n,yt(e,r)}function yt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Rt=!1;function oa(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function bd(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function pt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function jt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,V&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,yt(e,n)}return o=r.interleaved,o===null?(t.next=t,ra(r)):(t.next=o.next,o.next=t),r.interleaved=t,yt(e,n)}function Io(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Fs(e,n)}}function vu(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=l:i=i.next=l,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ri(e,t,n,r){var o=e.updateQueue;Rt=!1;var i=o.firstBaseUpdate,l=o.lastBaseUpdate,s=o.shared.pending;if(s!==null){o.shared.pending=null;var a=s,u=a.next;a.next=null,l===null?i=u:l.next=u,l=a;var d=e.alternate;d!==null&&(d=d.updateQueue,s=d.lastBaseUpdate,s!==l&&(s===null?d.firstBaseUpdate=u:s.next=u,d.lastBaseUpdate=a))}if(i!==null){var f=o.baseState;l=0,d=u=a=null,s=i;do{var m=s.lane,g=s.eventTime;if((r&m)===m){d!==null&&(d=d.next={eventTime:g,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var _=e,E=s;switch(m=t,g=n,E.tag){case 1:if(_=E.payload,typeof _=="function"){f=_.call(g,f,m);break e}f=_;break e;case 3:_.flags=_.flags&-65537|128;case 0:if(_=E.payload,m=typeof _=="function"?_.call(g,f,m):_,m==null)break e;f=q({},f,m);break e;case 2:Rt=!0}}s.callback!==null&&s.lane!==0&&(e.flags|=64,m=o.effects,m===null?o.effects=[s]:m.push(s))}else g={eventTime:g,lane:m,tag:s.tag,payload:s.payload,callback:s.callback,next:null},d===null?(u=d=g,a=f):d=d.next=g,l|=m;if(s=s.next,s===null){if(s=o.shared.pending,s===null)break;m=s,s=m.next,m.next=null,o.lastBaseUpdate=m,o.shared.pending=null}}while(!0);if(d===null&&(a=f),o.baseState=a,o.firstBaseUpdate=u,o.lastBaseUpdate=d,t=o.shared.interleaved,t!==null){o=t;do l|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);an|=l,e.lanes=l,e.memoizedState=f}}function Eu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(C(191,o));o.call(r)}}}var Qr={},lt=Bt(Qr),Nr=Bt(Qr),Dr=Bt(Qr);function Zt(e){if(e===Qr)throw Error(C(174));return e}function ia(e,t){switch(F(Dr,t),F(Nr,e),F(lt,Qr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:bl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=bl(t,e)}G(lt),F(lt,t)}function $n(){G(lt),G(Nr),G(Dr)}function Vd(e){Zt(Dr.current);var t=Zt(lt.current),n=bl(t,e.type);t!==n&&(F(Nr,e),F(lt,n))}function la(e){Nr.current===e&&(G(lt),G(Nr))}var K=Bt(0);function oi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var cl=[];function sa(){for(var e=0;e<cl.length;e++)cl[e]._workInProgressVersionPrimary=null;cl.length=0}var No=Et.ReactCurrentDispatcher,dl=Et.ReactCurrentBatchConfig,sn=0,Q=null,te=null,se=null,ii=!1,_r=!1,jr=0,Ih=0;function pe(){throw Error(C(321))}function aa(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ze(e[n],t[n]))return!1;return!0}function ua(e,t,n,r,o,i){if(sn=i,Q=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,No.current=e===null||e.memoizedState===null?Mh:bh,e=n(r,o),_r){i=0;do{if(_r=!1,jr=0,25<=i)throw Error(C(301));i+=1,se=te=null,t.updateQueue=null,No.current=Vh,e=n(r,o)}while(_r)}if(No.current=li,t=te!==null&&te.next!==null,sn=0,se=te=Q=null,ii=!1,t)throw Error(C(300));return e}function ca(){var e=jr!==0;return jr=0,e}function rt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return se===null?Q.memoizedState=se=e:se=se.next=e,se}function We(){if(te===null){var e=Q.alternate;e=e!==null?e.memoizedState:null}else e=te.next;var t=se===null?Q.memoizedState:se.next;if(t!==null)se=t,te=e;else{if(e===null)throw Error(C(310));te=e,e={memoizedState:te.memoizedState,baseState:te.baseState,baseQueue:te.baseQueue,queue:te.queue,next:null},se===null?Q.memoizedState=se=e:se=se.next=e}return se}function Mr(e,t){return typeof t=="function"?t(e):t}function fl(e){var t=We(),n=t.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=e;var r=te,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var l=o.next;o.next=i.next,i.next=l}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var s=l=null,a=null,u=i;do{var d=u.lane;if((sn&d)===d)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(s=a=f,l=r):a=a.next=f,Q.lanes|=d,an|=d}u=u.next}while(u!==null&&u!==i);a===null?l=r:a.next=s,Ze(r,t.memoizedState)||(Re=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,Q.lanes|=i,an|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function pl(e){var t=We(),n=t.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var l=o=o.next;do i=e(i,l.action),l=l.next;while(l!==o);Ze(i,t.memoizedState)||(Re=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function zd(){}function Ud(e,t){var n=Q,r=We(),o=t(),i=!Ze(r.memoizedState,o);if(i&&(r.memoizedState=o,Re=!0),r=r.queue,da(Fd.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||se!==null&&se.memoizedState.tag&1){if(n.flags|=2048,br(9,Bd.bind(null,n,r,o,t),void 0,null),ae===null)throw Error(C(349));sn&30||$d(n,t,o)}return o}function $d(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Bd(e,t,n,r){t.value=n,t.getSnapshot=r,Hd(t)&&Gd(e)}function Fd(e,t,n){return n(function(){Hd(t)&&Gd(e)})}function Hd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ze(e,n)}catch{return!0}}function Gd(e){var t=yt(e,1);t!==null&&Je(t,e,1,-1)}function Su(e){var t=rt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Mr,lastRenderedState:e},t.queue=e,e=e.dispatch=jh.bind(null,Q,e),[t.memoizedState,e]}function br(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Wd(){return We().memoizedState}function Do(e,t,n,r){var o=rt();Q.flags|=e,o.memoizedState=br(1|t,n,void 0,r===void 0?null:r)}function Li(e,t,n,r){var o=We();r=r===void 0?null:r;var i=void 0;if(te!==null){var l=te.memoizedState;if(i=l.destroy,r!==null&&aa(r,l.deps)){o.memoizedState=br(t,n,i,r);return}}Q.flags|=e,o.memoizedState=br(1|t,n,i,r)}function wu(e,t){return Do(8390656,8,e,t)}function da(e,t){return Li(2048,8,e,t)}function Kd(e,t){return Li(4,2,e,t)}function Qd(e,t){return Li(4,4,e,t)}function qd(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Xd(e,t,n){return n=n!=null?n.concat([e]):null,Li(4,4,qd.bind(null,t,e),n)}function fa(){}function Yd(e,t){var n=We();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&aa(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Jd(e,t){var n=We();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&aa(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Zd(e,t,n){return sn&21?(Ze(n,t)||(n=od(),Q.lanes|=n,an|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Re=!0),e.memoizedState=n)}function Nh(e,t){var n=z;z=n!==0&&4>n?n:4,e(!0);var r=dl.transition;dl.transition={};try{e(!1),t()}finally{z=n,dl.transition=r}}function ef(){return We().memoizedState}function Dh(e,t,n){var r=bt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},tf(e))nf(t,n);else if(n=Md(e,t,n,r),n!==null){var o=we();Je(n,e,r,o),rf(n,t,r)}}function jh(e,t,n){var r=bt(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(tf(e))nf(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var l=t.lastRenderedState,s=i(l,n);if(o.hasEagerState=!0,o.eagerState=s,Ze(s,l)){var a=t.interleaved;a===null?(o.next=o,ra(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=Md(e,t,o,r),n!==null&&(o=we(),Je(n,e,r,o),rf(n,t,r))}}function tf(e){var t=e.alternate;return e===Q||t!==null&&t===Q}function nf(e,t){_r=ii=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function rf(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Fs(e,n)}}var li={readContext:Ge,useCallback:pe,useContext:pe,useEffect:pe,useImperativeHandle:pe,useInsertionEffect:pe,useLayoutEffect:pe,useMemo:pe,useReducer:pe,useRef:pe,useState:pe,useDebugValue:pe,useDeferredValue:pe,useTransition:pe,useMutableSource:pe,useSyncExternalStore:pe,useId:pe,unstable_isNewReconciler:!1},Mh={readContext:Ge,useCallback:function(e,t){return rt().memoizedState=[e,t===void 0?null:t],e},useContext:Ge,useEffect:wu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Do(4194308,4,qd.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Do(4194308,4,e,t)},useInsertionEffect:function(e,t){return Do(4,2,e,t)},useMemo:function(e,t){var n=rt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=rt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Dh.bind(null,Q,e),[r.memoizedState,e]},useRef:function(e){var t=rt();return e={current:e},t.memoizedState=e},useState:Su,useDebugValue:fa,useDeferredValue:function(e){return rt().memoizedState=e},useTransition:function(){var e=Su(!1),t=e[0];return e=Nh.bind(null,e[1]),rt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Q,o=rt();if(W){if(n===void 0)throw Error(C(407));n=n()}else{if(n=t(),ae===null)throw Error(C(349));sn&30||$d(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,wu(Fd.bind(null,r,i,e),[e]),r.flags|=2048,br(9,Bd.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=rt(),t=ae.identifierPrefix;if(W){var n=dt,r=ct;n=(r&~(1<<32-Ye(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=jr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Ih++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},bh={readContext:Ge,useCallback:Yd,useContext:Ge,useEffect:da,useImperativeHandle:Xd,useInsertionEffect:Kd,useLayoutEffect:Qd,useMemo:Jd,useReducer:fl,useRef:Wd,useState:function(){return fl(Mr)},useDebugValue:fa,useDeferredValue:function(e){var t=We();return Zd(t,te.memoizedState,e)},useTransition:function(){var e=fl(Mr)[0],t=We().memoizedState;return[e,t]},useMutableSource:zd,useSyncExternalStore:Ud,useId:ef,unstable_isNewReconciler:!1},Vh={readContext:Ge,useCallback:Yd,useContext:Ge,useEffect:da,useImperativeHandle:Xd,useInsertionEffect:Kd,useLayoutEffect:Qd,useMemo:Jd,useReducer:pl,useRef:Wd,useState:function(){return pl(Mr)},useDebugValue:fa,useDeferredValue:function(e){var t=We();return te===null?t.memoizedState=e:Zd(t,te.memoizedState,e)},useTransition:function(){var e=pl(Mr)[0],t=We().memoizedState;return[e,t]},useMutableSource:zd,useSyncExternalStore:Ud,useId:ef,unstable_isNewReconciler:!1};function Qe(e,t){if(e&&e.defaultProps){t=q({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function os(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:q({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Oi={isMounted:function(e){return(e=e._reactInternals)?fn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=we(),o=bt(e),i=pt(r,o);i.payload=t,n!=null&&(i.callback=n),t=jt(e,i,o),t!==null&&(Je(t,e,o,r),Io(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=we(),o=bt(e),i=pt(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=jt(e,i,o),t!==null&&(Je(t,e,o,r),Io(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=we(),r=bt(e),o=pt(n,r);o.tag=2,t!=null&&(o.callback=t),t=jt(e,o,r),t!==null&&(Je(t,e,r,n),Io(t,e,r))}};function ku(e,t,n,r,o,i,l){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,l):t.prototype&&t.prototype.isPureReactComponent?!Lr(n,r)||!Lr(o,i):!0}function of(e,t,n){var r=!1,o=Ut,i=t.contextType;return typeof i=="object"&&i!==null?i=Ge(i):(o=Te(t)?on:_e.current,r=t.contextTypes,i=(r=r!=null)?Vn(e,o):Ut),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Oi,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function xu(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Oi.enqueueReplaceState(t,t.state,null)}function is(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},oa(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=Ge(i):(i=Te(t)?on:_e.current,o.context=Vn(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(os(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Oi.enqueueReplaceState(o,o.state,null),ri(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Bn(e,t){try{var n="",r=t;do n+=fm(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function ml(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function ls(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var zh=typeof WeakMap=="function"?WeakMap:Map;function lf(e,t,n){n=pt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ai||(ai=!0,gs=r),ls(e,t)},n}function sf(e,t,n){n=pt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){ls(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){ls(e,t),typeof r!="function"&&(Mt===null?Mt=new Set([this]):Mt.add(this));var l=t.stack;this.componentDidCatch(t.value,{componentStack:l!==null?l:""})}),n}function Cu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new zh;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Zh.bind(null,e,t,n),t.then(e,e))}function Ru(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Pu(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=pt(-1,1),t.tag=2,jt(n,t,1))),n.lanes|=1),e)}var Uh=Et.ReactCurrentOwner,Re=!1;function ve(e,t,n,r){t.child=e===null?jd(t,null,n,r):Un(t,e.child,n,r)}function Tu(e,t,n,r,o){n=n.render;var i=t.ref;return In(t,o),r=ua(e,t,n,r,i,o),n=ca(),e!==null&&!Re?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,vt(e,t,o)):(W&&n&&Ys(t),t.flags|=1,ve(e,t,r,o),t.child)}function Lu(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!Ea(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,af(e,t,i,r,o)):(e=Vo(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var l=i.memoizedProps;if(n=n.compare,n=n!==null?n:Lr,n(l,r)&&e.ref===t.ref)return vt(e,t,o)}return t.flags|=1,e=Vt(i,r),e.ref=t.ref,e.return=t,t.child=e}function af(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(Lr(i,r)&&e.ref===t.ref)if(Re=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(Re=!0);else return t.lanes=e.lanes,vt(e,t,o)}return ss(e,t,n,r,o)}function uf(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},F(Pn,Ie),Ie|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,F(Pn,Ie),Ie|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,F(Pn,Ie),Ie|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,F(Pn,Ie),Ie|=r;return ve(e,t,o,n),t.child}function cf(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ss(e,t,n,r,o){var i=Te(n)?on:_e.current;return i=Vn(t,i),In(t,o),n=ua(e,t,n,r,i,o),r=ca(),e!==null&&!Re?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,vt(e,t,o)):(W&&r&&Ys(t),t.flags|=1,ve(e,t,n,o),t.child)}function Ou(e,t,n,r,o){if(Te(n)){var i=!0;Jo(t)}else i=!1;if(In(t,o),t.stateNode===null)jo(e,t),of(t,n,r),is(t,n,r,o),r=!0;else if(e===null){var l=t.stateNode,s=t.memoizedProps;l.props=s;var a=l.context,u=n.contextType;typeof u=="object"&&u!==null?u=Ge(u):(u=Te(n)?on:_e.current,u=Vn(t,u));var d=n.getDerivedStateFromProps,f=typeof d=="function"||typeof l.getSnapshotBeforeUpdate=="function";f||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==r||a!==u)&&xu(t,l,r,u),Rt=!1;var m=t.memoizedState;l.state=m,ri(t,r,l,o),a=t.memoizedState,s!==r||m!==a||Pe.current||Rt?(typeof d=="function"&&(os(t,n,d,r),a=t.memoizedState),(s=Rt||ku(t,n,s,r,m,a,u))?(f||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount()),typeof l.componentDidMount=="function"&&(t.flags|=4194308)):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),l.props=r,l.state=a,l.context=u,r=s):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,bd(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:Qe(t.type,s),l.props=u,f=t.pendingProps,m=l.context,a=n.contextType,typeof a=="object"&&a!==null?a=Ge(a):(a=Te(n)?on:_e.current,a=Vn(t,a));var g=n.getDerivedStateFromProps;(d=typeof g=="function"||typeof l.getSnapshotBeforeUpdate=="function")||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==f||m!==a)&&xu(t,l,r,a),Rt=!1,m=t.memoizedState,l.state=m,ri(t,r,l,o);var _=t.memoizedState;s!==f||m!==_||Pe.current||Rt?(typeof g=="function"&&(os(t,n,g,r),_=t.memoizedState),(u=Rt||ku(t,n,u,r,m,_,a)||!1)?(d||typeof l.UNSAFE_componentWillUpdate!="function"&&typeof l.componentWillUpdate!="function"||(typeof l.componentWillUpdate=="function"&&l.componentWillUpdate(r,_,a),typeof l.UNSAFE_componentWillUpdate=="function"&&l.UNSAFE_componentWillUpdate(r,_,a)),typeof l.componentDidUpdate=="function"&&(t.flags|=4),typeof l.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=_),l.props=r,l.state=_,l.context=a,r=u):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),r=!1)}return as(e,t,n,r,i,o)}function as(e,t,n,r,o,i){cf(e,t);var l=(t.flags&128)!==0;if(!r&&!l)return o&&hu(t,n,!1),vt(e,t,i);r=t.stateNode,Uh.current=t;var s=l&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&l?(t.child=Un(t,e.child,null,i),t.child=Un(t,null,s,i)):ve(e,t,s,i),t.memoizedState=r.state,o&&hu(t,n,!0),t.child}function df(e){var t=e.stateNode;t.pendingContext?mu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&mu(e,t.context,!1),ia(e,t.containerInfo)}function Au(e,t,n,r,o){return zn(),Zs(o),t.flags|=256,ve(e,t,n,r),t.child}var us={dehydrated:null,treeContext:null,retryLane:0};function cs(e){return{baseLanes:e,cachePool:null,transitions:null}}function ff(e,t,n){var r=t.pendingProps,o=K.current,i=!1,l=(t.flags&128)!==0,s;if((s=l)||(s=e!==null&&e.memoizedState===null?!1:(o&2)!==0),s?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),F(K,o&1),e===null)return ns(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(l=r.children,e=r.fallback,i?(r=t.mode,i=t.child,l={mode:"hidden",children:l},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=l):i=Ni(l,r,0,null),e=rn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=cs(n),t.memoizedState=us,e):pa(t,l));if(o=e.memoizedState,o!==null&&(s=o.dehydrated,s!==null))return $h(e,t,l,r,s,o,n);if(i){i=r.fallback,l=t.mode,o=e.child,s=o.sibling;var a={mode:"hidden",children:r.children};return!(l&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Vt(o,a),r.subtreeFlags=o.subtreeFlags&14680064),s!==null?i=Vt(s,i):(i=rn(i,l,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,l=e.child.memoizedState,l=l===null?cs(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},i.memoizedState=l,i.childLanes=e.childLanes&~n,t.memoizedState=us,r}return i=e.child,e=i.sibling,r=Vt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function pa(e,t){return t=Ni({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function _o(e,t,n,r){return r!==null&&Zs(r),Un(t,e.child,null,n),e=pa(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function $h(e,t,n,r,o,i,l){if(n)return t.flags&256?(t.flags&=-257,r=ml(Error(C(422))),_o(e,t,l,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=Ni({mode:"visible",children:r.children},o,0,null),i=rn(i,o,l,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Un(t,e.child,null,l),t.child.memoizedState=cs(l),t.memoizedState=us,i);if(!(t.mode&1))return _o(e,t,l,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var s=r.dgst;return r=s,i=Error(C(419)),r=ml(i,r,void 0),_o(e,t,l,r)}if(s=(l&e.childLanes)!==0,Re||s){if(r=ae,r!==null){switch(l&-l){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|l)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,yt(e,o),Je(r,e,o,-1))}return va(),r=ml(Error(C(421))),_o(e,t,l,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=eg.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,Ne=Dt(o.nextSibling),De=t,W=!0,Xe=null,e!==null&&(Ue[$e++]=ct,Ue[$e++]=dt,Ue[$e++]=ln,ct=e.id,dt=e.overflow,ln=t),t=pa(t,r.children),t.flags|=4096,t)}function Iu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),rs(e.return,t,n)}function hl(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function pf(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(ve(e,t,r.children,n),r=K.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Iu(e,n,t);else if(e.tag===19)Iu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(F(K,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&oi(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),hl(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&oi(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}hl(t,!0,n,null,i);break;case"together":hl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function jo(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function vt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),an|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(C(153));if(t.child!==null){for(e=t.child,n=Vt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Vt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Bh(e,t,n){switch(t.tag){case 3:df(t),zn();break;case 5:Vd(t);break;case 1:Te(t.type)&&Jo(t);break;case 4:ia(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;F(ti,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(F(K,K.current&1),t.flags|=128,null):n&t.child.childLanes?ff(e,t,n):(F(K,K.current&1),e=vt(e,t,n),e!==null?e.sibling:null);F(K,K.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return pf(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),F(K,K.current),r)break;return null;case 22:case 23:return t.lanes=0,uf(e,t,n)}return vt(e,t,n)}var mf,ds,hf,gf;mf=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ds=function(){};hf=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Zt(lt.current);var i=null;switch(n){case"input":o=Nl(e,o),r=Nl(e,r),i=[];break;case"select":o=q({},o,{value:void 0}),r=q({},r,{value:void 0}),i=[];break;case"textarea":o=Ml(e,o),r=Ml(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Xo)}Vl(n,r);var l;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var s=o[u];for(l in s)s.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(wr.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var a=r[u];if(s=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&a!==s&&(a!=null||s!=null))if(u==="style")if(s){for(l in s)!s.hasOwnProperty(l)||a&&a.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in a)a.hasOwnProperty(l)&&s[l]!==a[l]&&(n||(n={}),n[l]=a[l])}else n||(i||(i=[]),i.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,s=s?s.__html:void 0,a!=null&&s!==a&&(i=i||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(wr.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&H("scroll",e),i||s===a||(i=[])):(i=i||[]).push(u,a))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};gf=function(e,t,n,r){n!==r&&(t.flags|=4)};function or(e,t){if(!W)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function me(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Fh(e,t,n){var r=t.pendingProps;switch(Js(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return me(t),null;case 1:return Te(t.type)&&Yo(),me(t),null;case 3:return r=t.stateNode,$n(),G(Pe),G(_e),sa(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(ho(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Xe!==null&&(vs(Xe),Xe=null))),ds(e,t),me(t),null;case 5:la(t);var o=Zt(Dr.current);if(n=t.type,e!==null&&t.stateNode!=null)hf(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(C(166));return me(t),null}if(e=Zt(lt.current),ho(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[ot]=t,r[Ir]=i,e=(t.mode&1)!==0,n){case"dialog":H("cancel",r),H("close",r);break;case"iframe":case"object":case"embed":H("load",r);break;case"video":case"audio":for(o=0;o<cr.length;o++)H(cr[o],r);break;case"source":H("error",r);break;case"img":case"image":case"link":H("error",r),H("load",r);break;case"details":H("toggle",r);break;case"input":$a(r,i),H("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},H("invalid",r);break;case"textarea":Fa(r,i),H("invalid",r)}Vl(n,i),o=null;for(var l in i)if(i.hasOwnProperty(l)){var s=i[l];l==="children"?typeof s=="string"?r.textContent!==s&&(i.suppressHydrationWarning!==!0&&mo(r.textContent,s,e),o=["children",s]):typeof s=="number"&&r.textContent!==""+s&&(i.suppressHydrationWarning!==!0&&mo(r.textContent,s,e),o=["children",""+s]):wr.hasOwnProperty(l)&&s!=null&&l==="onScroll"&&H("scroll",r)}switch(n){case"input":io(r),Ba(r,i,!0);break;case"textarea":io(r),Ha(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Xo)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{l=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Fc(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),n==="select"&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[ot]=t,e[Ir]=r,mf(e,t,!1,!1),t.stateNode=e;e:{switch(l=zl(n,r),n){case"dialog":H("cancel",e),H("close",e),o=r;break;case"iframe":case"object":case"embed":H("load",e),o=r;break;case"video":case"audio":for(o=0;o<cr.length;o++)H(cr[o],e);o=r;break;case"source":H("error",e),o=r;break;case"img":case"image":case"link":H("error",e),H("load",e),o=r;break;case"details":H("toggle",e),o=r;break;case"input":$a(e,r),o=Nl(e,r),H("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=q({},r,{value:void 0}),H("invalid",e);break;case"textarea":Fa(e,r),o=Ml(e,r),H("invalid",e);break;default:o=r}Vl(n,o),s=o;for(i in s)if(s.hasOwnProperty(i)){var a=s[i];i==="style"?Wc(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Hc(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&kr(e,a):typeof a=="number"&&kr(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(wr.hasOwnProperty(i)?a!=null&&i==="onScroll"&&H("scroll",e):a!=null&&bs(e,i,a,l))}switch(n){case"input":io(e),Ba(e,r,!1);break;case"textarea":io(e),Ha(e);break;case"option":r.value!=null&&e.setAttribute("value",""+zt(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Tn(e,!!r.multiple,i,!1):r.defaultValue!=null&&Tn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Xo)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return me(t),null;case 6:if(e&&t.stateNode!=null)gf(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(C(166));if(n=Zt(Dr.current),Zt(lt.current),ho(t)){if(r=t.stateNode,n=t.memoizedProps,r[ot]=t,(i=r.nodeValue!==n)&&(e=De,e!==null))switch(e.tag){case 3:mo(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&mo(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[ot]=t,t.stateNode=r}return me(t),null;case 13:if(G(K),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(W&&Ne!==null&&t.mode&1&&!(t.flags&128))Nd(),zn(),t.flags|=98560,i=!1;else if(i=ho(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(C(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(C(317));i[ot]=t}else zn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;me(t),i=!1}else Xe!==null&&(vs(Xe),Xe=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||K.current&1?re===0&&(re=3):va())),t.updateQueue!==null&&(t.flags|=4),me(t),null);case 4:return $n(),ds(e,t),e===null&&Or(t.stateNode.containerInfo),me(t),null;case 10:return na(t.type._context),me(t),null;case 17:return Te(t.type)&&Yo(),me(t),null;case 19:if(G(K),i=t.memoizedState,i===null)return me(t),null;if(r=(t.flags&128)!==0,l=i.rendering,l===null)if(r)or(i,!1);else{if(re!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(l=oi(e),l!==null){for(t.flags|=128,or(i,!1),r=l.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,l=i.alternate,l===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=l.childLanes,i.lanes=l.lanes,i.child=l.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=l.memoizedProps,i.memoizedState=l.memoizedState,i.updateQueue=l.updateQueue,i.type=l.type,e=l.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return F(K,K.current&1|2),t.child}e=e.sibling}i.tail!==null&&J()>Fn&&(t.flags|=128,r=!0,or(i,!1),t.lanes=4194304)}else{if(!r)if(e=oi(l),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),or(i,!0),i.tail===null&&i.tailMode==="hidden"&&!l.alternate&&!W)return me(t),null}else 2*J()-i.renderingStartTime>Fn&&n!==1073741824&&(t.flags|=128,r=!0,or(i,!1),t.lanes=4194304);i.isBackwards?(l.sibling=t.child,t.child=l):(n=i.last,n!==null?n.sibling=l:t.child=l,i.last=l)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=J(),t.sibling=null,n=K.current,F(K,r?n&1|2:n&1),t):(me(t),null);case 22:case 23:return ya(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ie&1073741824&&(me(t),t.subtreeFlags&6&&(t.flags|=8192)):me(t),null;case 24:return null;case 25:return null}throw Error(C(156,t.tag))}function Hh(e,t){switch(Js(t),t.tag){case 1:return Te(t.type)&&Yo(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return $n(),G(Pe),G(_e),sa(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return la(t),null;case 13:if(G(K),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(C(340));zn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return G(K),null;case 4:return $n(),null;case 10:return na(t.type._context),null;case 22:case 23:return ya(),null;case 24:return null;default:return null}}var yo=!1,ge=!1,Gh=typeof WeakSet=="function"?WeakSet:Set,L=null;function Rn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){X(e,t,r)}else n.current=null}function fs(e,t,n){try{n()}catch(r){X(e,t,r)}}var Nu=!1;function Wh(e,t){if(ql=Ko,e=Ed(),Xs(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var l=0,s=-1,a=-1,u=0,d=0,f=e,m=null;t:for(;;){for(var g;f!==n||o!==0&&f.nodeType!==3||(s=l+o),f!==i||r!==0&&f.nodeType!==3||(a=l+r),f.nodeType===3&&(l+=f.nodeValue.length),(g=f.firstChild)!==null;)m=f,f=g;for(;;){if(f===e)break t;if(m===n&&++u===o&&(s=l),m===i&&++d===r&&(a=l),(g=f.nextSibling)!==null)break;f=m,m=f.parentNode}f=g}n=s===-1||a===-1?null:{start:s,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Xl={focusedElem:e,selectionRange:n},Ko=!1,L=t;L!==null;)if(t=L,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,L=e;else for(;L!==null;){t=L;try{var _=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(_!==null){var E=_.memoizedProps,S=_.memoizedState,p=t.stateNode,c=p.getSnapshotBeforeUpdate(t.elementType===t.type?E:Qe(t.type,E),S);p.__reactInternalSnapshotBeforeUpdate=c}break;case 3:var y=t.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(C(163))}}catch(w){X(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,L=e;break}L=t.return}return _=Nu,Nu=!1,_}function yr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&fs(t,n,i)}o=o.next}while(o!==r)}}function Ai(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ps(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function _f(e){var t=e.alternate;t!==null&&(e.alternate=null,_f(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[ot],delete t[Ir],delete t[Zl],delete t[Th],delete t[Lh])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function yf(e){return e.tag===5||e.tag===3||e.tag===4}function Du(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||yf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ms(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Xo));else if(r!==4&&(e=e.child,e!==null))for(ms(e,t,n),e=e.sibling;e!==null;)ms(e,t,n),e=e.sibling}function hs(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(hs(e,t,n),e=e.sibling;e!==null;)hs(e,t,n),e=e.sibling}var ce=null,qe=!1;function xt(e,t,n){for(n=n.child;n!==null;)vf(e,t,n),n=n.sibling}function vf(e,t,n){if(it&&typeof it.onCommitFiberUnmount=="function")try{it.onCommitFiberUnmount(ki,n)}catch{}switch(n.tag){case 5:ge||Rn(n,t);case 6:var r=ce,o=qe;ce=null,xt(e,t,n),ce=r,qe=o,ce!==null&&(qe?(e=ce,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ce.removeChild(n.stateNode));break;case 18:ce!==null&&(qe?(e=ce,n=n.stateNode,e.nodeType===8?al(e.parentNode,n):e.nodeType===1&&al(e,n),Pr(e)):al(ce,n.stateNode));break;case 4:r=ce,o=qe,ce=n.stateNode.containerInfo,qe=!0,xt(e,t,n),ce=r,qe=o;break;case 0:case 11:case 14:case 15:if(!ge&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,l=i.destroy;i=i.tag,l!==void 0&&(i&2||i&4)&&fs(n,t,l),o=o.next}while(o!==r)}xt(e,t,n);break;case 1:if(!ge&&(Rn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){X(n,t,s)}xt(e,t,n);break;case 21:xt(e,t,n);break;case 22:n.mode&1?(ge=(r=ge)||n.memoizedState!==null,xt(e,t,n),ge=r):xt(e,t,n);break;default:xt(e,t,n)}}function ju(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Gh),t.forEach(function(r){var o=tg.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function Ke(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,l=t,s=l;e:for(;s!==null;){switch(s.tag){case 5:ce=s.stateNode,qe=!1;break e;case 3:ce=s.stateNode.containerInfo,qe=!0;break e;case 4:ce=s.stateNode.containerInfo,qe=!0;break e}s=s.return}if(ce===null)throw Error(C(160));vf(i,l,o),ce=null,qe=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(u){X(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Ef(t,e),t=t.sibling}function Ef(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ke(t,e),et(e),r&4){try{yr(3,e,e.return),Ai(3,e)}catch(E){X(e,e.return,E)}try{yr(5,e,e.return)}catch(E){X(e,e.return,E)}}break;case 1:Ke(t,e),et(e),r&512&&n!==null&&Rn(n,n.return);break;case 5:if(Ke(t,e),et(e),r&512&&n!==null&&Rn(n,n.return),e.flags&32){var o=e.stateNode;try{kr(o,"")}catch(E){X(e,e.return,E)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,l=n!==null?n.memoizedProps:i,s=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{s==="input"&&i.type==="radio"&&i.name!=null&&$c(o,i),zl(s,l);var u=zl(s,i);for(l=0;l<a.length;l+=2){var d=a[l],f=a[l+1];d==="style"?Wc(o,f):d==="dangerouslySetInnerHTML"?Hc(o,f):d==="children"?kr(o,f):bs(o,d,f,u)}switch(s){case"input":Dl(o,i);break;case"textarea":Bc(o,i);break;case"select":var m=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var g=i.value;g!=null?Tn(o,!!i.multiple,g,!1):m!==!!i.multiple&&(i.defaultValue!=null?Tn(o,!!i.multiple,i.defaultValue,!0):Tn(o,!!i.multiple,i.multiple?[]:"",!1))}o[Ir]=i}catch(E){X(e,e.return,E)}}break;case 6:if(Ke(t,e),et(e),r&4){if(e.stateNode===null)throw Error(C(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(E){X(e,e.return,E)}}break;case 3:if(Ke(t,e),et(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Pr(t.containerInfo)}catch(E){X(e,e.return,E)}break;case 4:Ke(t,e),et(e);break;case 13:Ke(t,e),et(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(ga=J())),r&4&&ju(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(ge=(u=ge)||d,Ke(t,e),ge=u):Ke(t,e),et(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&e.mode&1)for(L=e,d=e.child;d!==null;){for(f=L=d;L!==null;){switch(m=L,g=m.child,m.tag){case 0:case 11:case 14:case 15:yr(4,m,m.return);break;case 1:Rn(m,m.return);var _=m.stateNode;if(typeof _.componentWillUnmount=="function"){r=m,n=m.return;try{t=r,_.props=t.memoizedProps,_.state=t.memoizedState,_.componentWillUnmount()}catch(E){X(r,n,E)}}break;case 5:Rn(m,m.return);break;case 22:if(m.memoizedState!==null){bu(f);continue}}g!==null?(g.return=m,L=g):bu(f)}d=d.sibling}e:for(d=null,f=e;;){if(f.tag===5){if(d===null){d=f;try{o=f.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(s=f.stateNode,a=f.memoizedProps.style,l=a!=null&&a.hasOwnProperty("display")?a.display:null,s.style.display=Gc("display",l))}catch(E){X(e,e.return,E)}}}else if(f.tag===6){if(d===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(E){X(e,e.return,E)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:Ke(t,e),et(e),r&4&&ju(e);break;case 21:break;default:Ke(t,e),et(e)}}function et(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(yf(n)){var r=n;break e}n=n.return}throw Error(C(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(kr(o,""),r.flags&=-33);var i=Du(e);hs(e,i,o);break;case 3:case 4:var l=r.stateNode.containerInfo,s=Du(e);ms(e,s,l);break;default:throw Error(C(161))}}catch(a){X(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Kh(e,t,n){L=e,Sf(e)}function Sf(e,t,n){for(var r=(e.mode&1)!==0;L!==null;){var o=L,i=o.child;if(o.tag===22&&r){var l=o.memoizedState!==null||yo;if(!l){var s=o.alternate,a=s!==null&&s.memoizedState!==null||ge;s=yo;var u=ge;if(yo=l,(ge=a)&&!u)for(L=o;L!==null;)l=L,a=l.child,l.tag===22&&l.memoizedState!==null?Vu(o):a!==null?(a.return=l,L=a):Vu(o);for(;i!==null;)L=i,Sf(i),i=i.sibling;L=o,yo=s,ge=u}Mu(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,L=i):Mu(e)}}function Mu(e){for(;L!==null;){var t=L;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ge||Ai(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ge)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Qe(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Eu(t,i,r);break;case 3:var l=t.updateQueue;if(l!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Eu(t,l,n)}break;case 5:var s=t.stateNode;if(n===null&&t.flags&4){n=s;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var f=d.dehydrated;f!==null&&Pr(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(C(163))}ge||t.flags&512&&ps(t)}catch(m){X(t,t.return,m)}}if(t===e){L=null;break}if(n=t.sibling,n!==null){n.return=t.return,L=n;break}L=t.return}}function bu(e){for(;L!==null;){var t=L;if(t===e){L=null;break}var n=t.sibling;if(n!==null){n.return=t.return,L=n;break}L=t.return}}function Vu(e){for(;L!==null;){var t=L;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ai(4,t)}catch(a){X(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){X(t,o,a)}}var i=t.return;try{ps(t)}catch(a){X(t,i,a)}break;case 5:var l=t.return;try{ps(t)}catch(a){X(t,l,a)}}}catch(a){X(t,t.return,a)}if(t===e){L=null;break}var s=t.sibling;if(s!==null){s.return=t.return,L=s;break}L=t.return}}var Qh=Math.ceil,si=Et.ReactCurrentDispatcher,ma=Et.ReactCurrentOwner,He=Et.ReactCurrentBatchConfig,V=0,ae=null,ee=null,de=0,Ie=0,Pn=Bt(0),re=0,Vr=null,an=0,Ii=0,ha=0,vr=null,Ce=null,ga=0,Fn=1/0,st=null,ai=!1,gs=null,Mt=null,vo=!1,Ot=null,ui=0,Er=0,_s=null,Mo=-1,bo=0;function we(){return V&6?J():Mo!==-1?Mo:Mo=J()}function bt(e){return e.mode&1?V&2&&de!==0?de&-de:Ah.transition!==null?(bo===0&&(bo=od()),bo):(e=z,e!==0||(e=window.event,e=e===void 0?16:dd(e.type)),e):1}function Je(e,t,n,r){if(50<Er)throw Er=0,_s=null,Error(C(185));Gr(e,n,r),(!(V&2)||e!==ae)&&(e===ae&&(!(V&2)&&(Ii|=n),re===4&&Tt(e,de)),Le(e,r),n===1&&V===0&&!(t.mode&1)&&(Fn=J()+500,Ti&&Ft()))}function Le(e,t){var n=e.callbackNode;Am(e,t);var r=Wo(e,e===ae?de:0);if(r===0)n!==null&&Ka(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Ka(n),t===1)e.tag===0?Oh(zu.bind(null,e)):Od(zu.bind(null,e)),Rh(function(){!(V&6)&&Ft()}),n=null;else{switch(id(r)){case 1:n=Bs;break;case 4:n=nd;break;case 16:n=Go;break;case 536870912:n=rd;break;default:n=Go}n=Lf(n,wf.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function wf(e,t){if(Mo=-1,bo=0,V&6)throw Error(C(327));var n=e.callbackNode;if(Nn()&&e.callbackNode!==n)return null;var r=Wo(e,e===ae?de:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=ci(e,r);else{t=r;var o=V;V|=2;var i=xf();(ae!==e||de!==t)&&(st=null,Fn=J()+500,nn(e,t));do try{Yh();break}catch(s){kf(e,s)}while(!0);ta(),si.current=i,V=o,ee!==null?t=0:(ae=null,de=0,t=re)}if(t!==0){if(t===2&&(o=Hl(e),o!==0&&(r=o,t=ys(e,o))),t===1)throw n=Vr,nn(e,0),Tt(e,r),Le(e,J()),n;if(t===6)Tt(e,r);else{if(o=e.current.alternate,!(r&30)&&!qh(o)&&(t=ci(e,r),t===2&&(i=Hl(e),i!==0&&(r=i,t=ys(e,i))),t===1))throw n=Vr,nn(e,0),Tt(e,r),Le(e,J()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(C(345));case 2:Xt(e,Ce,st);break;case 3:if(Tt(e,r),(r&130023424)===r&&(t=ga+500-J(),10<t)){if(Wo(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){we(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Jl(Xt.bind(null,e,Ce,st),t);break}Xt(e,Ce,st);break;case 4:if(Tt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var l=31-Ye(r);i=1<<l,l=t[l],l>o&&(o=l),r&=~i}if(r=o,r=J()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Qh(r/1960))-r,10<r){e.timeoutHandle=Jl(Xt.bind(null,e,Ce,st),r);break}Xt(e,Ce,st);break;case 5:Xt(e,Ce,st);break;default:throw Error(C(329))}}}return Le(e,J()),e.callbackNode===n?wf.bind(null,e):null}function ys(e,t){var n=vr;return e.current.memoizedState.isDehydrated&&(nn(e,t).flags|=256),e=ci(e,t),e!==2&&(t=Ce,Ce=n,t!==null&&vs(t)),e}function vs(e){Ce===null?Ce=e:Ce.push.apply(Ce,e)}function qh(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!Ze(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Tt(e,t){for(t&=~ha,t&=~Ii,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ye(t),r=1<<n;e[n]=-1,t&=~r}}function zu(e){if(V&6)throw Error(C(327));Nn();var t=Wo(e,0);if(!(t&1))return Le(e,J()),null;var n=ci(e,t);if(e.tag!==0&&n===2){var r=Hl(e);r!==0&&(t=r,n=ys(e,r))}if(n===1)throw n=Vr,nn(e,0),Tt(e,t),Le(e,J()),n;if(n===6)throw Error(C(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Xt(e,Ce,st),Le(e,J()),null}function _a(e,t){var n=V;V|=1;try{return e(t)}finally{V=n,V===0&&(Fn=J()+500,Ti&&Ft())}}function un(e){Ot!==null&&Ot.tag===0&&!(V&6)&&Nn();var t=V;V|=1;var n=He.transition,r=z;try{if(He.transition=null,z=1,e)return e()}finally{z=r,He.transition=n,V=t,!(V&6)&&Ft()}}function ya(){Ie=Pn.current,G(Pn)}function nn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Ch(n)),ee!==null)for(n=ee.return;n!==null;){var r=n;switch(Js(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Yo();break;case 3:$n(),G(Pe),G(_e),sa();break;case 5:la(r);break;case 4:$n();break;case 13:G(K);break;case 19:G(K);break;case 10:na(r.type._context);break;case 22:case 23:ya()}n=n.return}if(ae=e,ee=e=Vt(e.current,null),de=Ie=t,re=0,Vr=null,ha=Ii=an=0,Ce=vr=null,Jt!==null){for(t=0;t<Jt.length;t++)if(n=Jt[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var l=i.next;i.next=o,r.next=l}n.pending=r}Jt=null}return e}function kf(e,t){do{var n=ee;try{if(ta(),No.current=li,ii){for(var r=Q.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}ii=!1}if(sn=0,se=te=Q=null,_r=!1,jr=0,ma.current=null,n===null||n.return===null){re=1,Vr=t,ee=null;break}e:{var i=e,l=n.return,s=n,a=t;if(t=de,s.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,d=s,f=d.tag;if(!(d.mode&1)&&(f===0||f===11||f===15)){var m=d.alternate;m?(d.updateQueue=m.updateQueue,d.memoizedState=m.memoizedState,d.lanes=m.lanes):(d.updateQueue=null,d.memoizedState=null)}var g=Ru(l);if(g!==null){g.flags&=-257,Pu(g,l,s,i,t),g.mode&1&&Cu(i,u,t),t=g,a=u;var _=t.updateQueue;if(_===null){var E=new Set;E.add(a),t.updateQueue=E}else _.add(a);break e}else{if(!(t&1)){Cu(i,u,t),va();break e}a=Error(C(426))}}else if(W&&s.mode&1){var S=Ru(l);if(S!==null){!(S.flags&65536)&&(S.flags|=256),Pu(S,l,s,i,t),Zs(Bn(a,s));break e}}i=a=Bn(a,s),re!==4&&(re=2),vr===null?vr=[i]:vr.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var p=lf(i,a,t);vu(i,p);break e;case 1:s=a;var c=i.type,y=i.stateNode;if(!(i.flags&128)&&(typeof c.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(Mt===null||!Mt.has(y)))){i.flags|=65536,t&=-t,i.lanes|=t;var w=sf(i,s,t);vu(i,w);break e}}i=i.return}while(i!==null)}Rf(n)}catch(x){t=x,ee===n&&n!==null&&(ee=n=n.return);continue}break}while(!0)}function xf(){var e=si.current;return si.current=li,e===null?li:e}function va(){(re===0||re===3||re===2)&&(re=4),ae===null||!(an&268435455)&&!(Ii&268435455)||Tt(ae,de)}function ci(e,t){var n=V;V|=2;var r=xf();(ae!==e||de!==t)&&(st=null,nn(e,t));do try{Xh();break}catch(o){kf(e,o)}while(!0);if(ta(),V=n,si.current=r,ee!==null)throw Error(C(261));return ae=null,de=0,re}function Xh(){for(;ee!==null;)Cf(ee)}function Yh(){for(;ee!==null&&!wm();)Cf(ee)}function Cf(e){var t=Tf(e.alternate,e,Ie);e.memoizedProps=e.pendingProps,t===null?Rf(e):ee=t,ma.current=null}function Rf(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Hh(n,t),n!==null){n.flags&=32767,ee=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{re=6,ee=null;return}}else if(n=Fh(n,t,Ie),n!==null){ee=n;return}if(t=t.sibling,t!==null){ee=t;return}ee=t=e}while(t!==null);re===0&&(re=5)}function Xt(e,t,n){var r=z,o=He.transition;try{He.transition=null,z=1,Jh(e,t,n,r)}finally{He.transition=o,z=r}return null}function Jh(e,t,n,r){do Nn();while(Ot!==null);if(V&6)throw Error(C(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(C(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Im(e,i),e===ae&&(ee=ae=null,de=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||vo||(vo=!0,Lf(Go,function(){return Nn(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=He.transition,He.transition=null;var l=z;z=1;var s=V;V|=4,ma.current=null,Wh(e,n),Ef(n,e),yh(Xl),Ko=!!ql,Xl=ql=null,e.current=n,Kh(n),km(),V=s,z=l,He.transition=i}else e.current=n;if(vo&&(vo=!1,Ot=e,ui=o),i=e.pendingLanes,i===0&&(Mt=null),Rm(n.stateNode),Le(e,J()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(ai)throw ai=!1,e=gs,gs=null,e;return ui&1&&e.tag!==0&&Nn(),i=e.pendingLanes,i&1?e===_s?Er++:(Er=0,_s=e):Er=0,Ft(),null}function Nn(){if(Ot!==null){var e=id(ui),t=He.transition,n=z;try{if(He.transition=null,z=16>e?16:e,Ot===null)var r=!1;else{if(e=Ot,Ot=null,ui=0,V&6)throw Error(C(331));var o=V;for(V|=4,L=e.current;L!==null;){var i=L,l=i.child;if(L.flags&16){var s=i.deletions;if(s!==null){for(var a=0;a<s.length;a++){var u=s[a];for(L=u;L!==null;){var d=L;switch(d.tag){case 0:case 11:case 15:yr(8,d,i)}var f=d.child;if(f!==null)f.return=d,L=f;else for(;L!==null;){d=L;var m=d.sibling,g=d.return;if(_f(d),d===u){L=null;break}if(m!==null){m.return=g,L=m;break}L=g}}}var _=i.alternate;if(_!==null){var E=_.child;if(E!==null){_.child=null;do{var S=E.sibling;E.sibling=null,E=S}while(E!==null)}}L=i}}if(i.subtreeFlags&2064&&l!==null)l.return=i,L=l;else e:for(;L!==null;){if(i=L,i.flags&2048)switch(i.tag){case 0:case 11:case 15:yr(9,i,i.return)}var p=i.sibling;if(p!==null){p.return=i.return,L=p;break e}L=i.return}}var c=e.current;for(L=c;L!==null;){l=L;var y=l.child;if(l.subtreeFlags&2064&&y!==null)y.return=l,L=y;else e:for(l=c;L!==null;){if(s=L,s.flags&2048)try{switch(s.tag){case 0:case 11:case 15:Ai(9,s)}}catch(x){X(s,s.return,x)}if(s===l){L=null;break e}var w=s.sibling;if(w!==null){w.return=s.return,L=w;break e}L=s.return}}if(V=o,Ft(),it&&typeof it.onPostCommitFiberRoot=="function")try{it.onPostCommitFiberRoot(ki,e)}catch{}r=!0}return r}finally{z=n,He.transition=t}}return!1}function Uu(e,t,n){t=Bn(n,t),t=lf(e,t,1),e=jt(e,t,1),t=we(),e!==null&&(Gr(e,1,t),Le(e,t))}function X(e,t,n){if(e.tag===3)Uu(e,e,n);else for(;t!==null;){if(t.tag===3){Uu(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Mt===null||!Mt.has(r))){e=Bn(n,e),e=sf(t,e,1),t=jt(t,e,1),e=we(),t!==null&&(Gr(t,1,e),Le(t,e));break}}t=t.return}}function Zh(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=we(),e.pingedLanes|=e.suspendedLanes&n,ae===e&&(de&n)===n&&(re===4||re===3&&(de&130023424)===de&&500>J()-ga?nn(e,0):ha|=n),Le(e,t)}function Pf(e,t){t===0&&(e.mode&1?(t=ao,ao<<=1,!(ao&130023424)&&(ao=4194304)):t=1);var n=we();e=yt(e,t),e!==null&&(Gr(e,t,n),Le(e,n))}function eg(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Pf(e,n)}function tg(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(C(314))}r!==null&&r.delete(t),Pf(e,n)}var Tf;Tf=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Pe.current)Re=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Re=!1,Bh(e,t,n);Re=!!(e.flags&131072)}else Re=!1,W&&t.flags&1048576&&Ad(t,ei,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;jo(e,t),e=t.pendingProps;var o=Vn(t,_e.current);In(t,n),o=ua(null,t,r,e,o,n);var i=ca();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Te(r)?(i=!0,Jo(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,oa(t),o.updater=Oi,t.stateNode=o,o._reactInternals=t,is(t,r,e,n),t=as(null,t,r,!0,i,n)):(t.tag=0,W&&i&&Ys(t),ve(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(jo(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=rg(r),e=Qe(r,e),o){case 0:t=ss(null,t,r,e,n);break e;case 1:t=Ou(null,t,r,e,n);break e;case 11:t=Tu(null,t,r,e,n);break e;case 14:t=Lu(null,t,r,Qe(r.type,e),n);break e}throw Error(C(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Qe(r,o),ss(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Qe(r,o),Ou(e,t,r,o,n);case 3:e:{if(df(t),e===null)throw Error(C(387));r=t.pendingProps,i=t.memoizedState,o=i.element,bd(e,t),ri(t,r,null,n);var l=t.memoizedState;if(r=l.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=Bn(Error(C(423)),t),t=Au(e,t,r,n,o);break e}else if(r!==o){o=Bn(Error(C(424)),t),t=Au(e,t,r,n,o);break e}else for(Ne=Dt(t.stateNode.containerInfo.firstChild),De=t,W=!0,Xe=null,n=jd(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(zn(),r===o){t=vt(e,t,n);break e}ve(e,t,r,n)}t=t.child}return t;case 5:return Vd(t),e===null&&ns(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,l=o.children,Yl(r,o)?l=null:i!==null&&Yl(r,i)&&(t.flags|=32),cf(e,t),ve(e,t,l,n),t.child;case 6:return e===null&&ns(t),null;case 13:return ff(e,t,n);case 4:return ia(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Un(t,null,r,n):ve(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Qe(r,o),Tu(e,t,r,o,n);case 7:return ve(e,t,t.pendingProps,n),t.child;case 8:return ve(e,t,t.pendingProps.children,n),t.child;case 12:return ve(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,l=o.value,F(ti,r._currentValue),r._currentValue=l,i!==null)if(Ze(i.value,l)){if(i.children===o.children&&!Pe.current){t=vt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var s=i.dependencies;if(s!==null){l=i.child;for(var a=s.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=pt(-1,n&-n),a.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?a.next=a:(a.next=d.next,d.next=a),u.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),rs(i.return,n,t),s.lanes|=n;break}a=a.next}}else if(i.tag===10)l=i.type===t.type?null:i.child;else if(i.tag===18){if(l=i.return,l===null)throw Error(C(341));l.lanes|=n,s=l.alternate,s!==null&&(s.lanes|=n),rs(l,n,t),l=i.sibling}else l=i.child;if(l!==null)l.return=i;else for(l=i;l!==null;){if(l===t){l=null;break}if(i=l.sibling,i!==null){i.return=l.return,l=i;break}l=l.return}i=l}ve(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,In(t,n),o=Ge(o),r=r(o),t.flags|=1,ve(e,t,r,n),t.child;case 14:return r=t.type,o=Qe(r,t.pendingProps),o=Qe(r.type,o),Lu(e,t,r,o,n);case 15:return af(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Qe(r,o),jo(e,t),t.tag=1,Te(r)?(e=!0,Jo(t)):e=!1,In(t,n),of(t,r,o),is(t,r,o,n),as(null,t,r,!0,e,n);case 19:return pf(e,t,n);case 22:return uf(e,t,n)}throw Error(C(156,t.tag))};function Lf(e,t){return td(e,t)}function ng(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Fe(e,t,n,r){return new ng(e,t,n,r)}function Ea(e){return e=e.prototype,!(!e||!e.isReactComponent)}function rg(e){if(typeof e=="function")return Ea(e)?1:0;if(e!=null){if(e=e.$$typeof,e===zs)return 11;if(e===Us)return 14}return 2}function Vt(e,t){var n=e.alternate;return n===null?(n=Fe(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Vo(e,t,n,r,o,i){var l=2;if(r=e,typeof e=="function")Ea(e)&&(l=1);else if(typeof e=="string")l=5;else e:switch(e){case _n:return rn(n.children,o,i,t);case Vs:l=8,o|=8;break;case Ll:return e=Fe(12,n,t,o|2),e.elementType=Ll,e.lanes=i,e;case Ol:return e=Fe(13,n,t,o),e.elementType=Ol,e.lanes=i,e;case Al:return e=Fe(19,n,t,o),e.elementType=Al,e.lanes=i,e;case Vc:return Ni(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Mc:l=10;break e;case bc:l=9;break e;case zs:l=11;break e;case Us:l=14;break e;case Ct:l=16,r=null;break e}throw Error(C(130,e==null?e:typeof e,""))}return t=Fe(l,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function rn(e,t,n,r){return e=Fe(7,e,r,t),e.lanes=n,e}function Ni(e,t,n,r){return e=Fe(22,e,r,t),e.elementType=Vc,e.lanes=n,e.stateNode={isHidden:!1},e}function gl(e,t,n){return e=Fe(6,e,null,t),e.lanes=n,e}function _l(e,t,n){return t=Fe(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function og(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Yi(0),this.expirationTimes=Yi(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Yi(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Sa(e,t,n,r,o,i,l,s,a){return e=new og(e,t,n,s,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Fe(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},oa(i),e}function ig(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:gn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Of(e){if(!e)return Ut;e=e._reactInternals;e:{if(fn(e)!==e||e.tag!==1)throw Error(C(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Te(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(C(171))}if(e.tag===1){var n=e.type;if(Te(n))return Ld(e,n,t)}return t}function Af(e,t,n,r,o,i,l,s,a){return e=Sa(n,r,!0,e,o,i,l,s,a),e.context=Of(null),n=e.current,r=we(),o=bt(n),i=pt(r,o),i.callback=t??null,jt(n,i,o),e.current.lanes=o,Gr(e,o,r),Le(e,r),e}function Di(e,t,n,r){var o=t.current,i=we(),l=bt(o);return n=Of(n),t.context===null?t.context=n:t.pendingContext=n,t=pt(i,l),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=jt(o,t,l),e!==null&&(Je(e,o,l,i),Io(e,o,l)),l}function di(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function $u(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function wa(e,t){$u(e,t),(e=e.alternate)&&$u(e,t)}function lg(){return null}var If=typeof reportError=="function"?reportError:function(e){console.error(e)};function ka(e){this._internalRoot=e}ji.prototype.render=ka.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(C(409));Di(e,t,null,null)};ji.prototype.unmount=ka.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;un(function(){Di(null,e,null,null)}),t[_t]=null}};function ji(e){this._internalRoot=e}ji.prototype.unstable_scheduleHydration=function(e){if(e){var t=ad();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Pt.length&&t!==0&&t<Pt[n].priority;n++);Pt.splice(n,0,e),n===0&&cd(e)}};function xa(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Mi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Bu(){}function sg(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=di(l);i.call(u)}}var l=Af(t,r,e,0,null,!1,!1,"",Bu);return e._reactRootContainer=l,e[_t]=l.current,Or(e.nodeType===8?e.parentNode:e),un(),l}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var s=r;r=function(){var u=di(a);s.call(u)}}var a=Sa(e,0,!1,null,null,!1,!1,"",Bu);return e._reactRootContainer=a,e[_t]=a.current,Or(e.nodeType===8?e.parentNode:e),un(function(){Di(t,a,n,r)}),a}function bi(e,t,n,r,o){var i=n._reactRootContainer;if(i){var l=i;if(typeof o=="function"){var s=o;o=function(){var a=di(l);s.call(a)}}Di(t,l,e,o)}else l=sg(n,t,e,o,r);return di(l)}ld=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=ur(t.pendingLanes);n!==0&&(Fs(t,n|1),Le(t,J()),!(V&6)&&(Fn=J()+500,Ft()))}break;case 13:un(function(){var r=yt(e,1);if(r!==null){var o=we();Je(r,e,1,o)}}),wa(e,1)}};Hs=function(e){if(e.tag===13){var t=yt(e,134217728);if(t!==null){var n=we();Je(t,e,134217728,n)}wa(e,134217728)}};sd=function(e){if(e.tag===13){var t=bt(e),n=yt(e,t);if(n!==null){var r=we();Je(n,e,t,r)}wa(e,t)}};ad=function(){return z};ud=function(e,t){var n=z;try{return z=e,t()}finally{z=n}};$l=function(e,t,n){switch(t){case"input":if(Dl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Pi(r);if(!o)throw Error(C(90));Uc(r),Dl(r,o)}}}break;case"textarea":Bc(e,n);break;case"select":t=n.value,t!=null&&Tn(e,!!n.multiple,t,!1)}};qc=_a;Xc=un;var ag={usingClientEntryPoint:!1,Events:[Kr,Sn,Pi,Kc,Qc,_a]},ir={findFiberByHostInstance:Yt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},ug={bundleType:ir.bundleType,version:ir.version,rendererPackageName:ir.rendererPackageName,rendererConfig:ir.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Et.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Zc(e),e===null?null:e.stateNode},findFiberByHostInstance:ir.findFiberByHostInstance||lg,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Eo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Eo.isDisabled&&Eo.supportsFiber)try{ki=Eo.inject(ug),it=Eo}catch{}}Me.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ag;Me.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!xa(t))throw Error(C(200));return ig(e,t,null,n)};Me.createRoot=function(e,t){if(!xa(e))throw Error(C(299));var n=!1,r="",o=If;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Sa(e,1,!1,null,null,n,!1,r,o),e[_t]=t.current,Or(e.nodeType===8?e.parentNode:e),new ka(t)};Me.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(C(188)):(e=Object.keys(e).join(","),Error(C(268,e)));return e=Zc(t),e=e===null?null:e.stateNode,e};Me.flushSync=function(e){return un(e)};Me.hydrate=function(e,t,n){if(!Mi(t))throw Error(C(200));return bi(null,e,t,!0,n)};Me.hydrateRoot=function(e,t,n){if(!xa(e))throw Error(C(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",l=If;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(l=n.onRecoverableError)),t=Af(t,null,e,1,n??null,o,!1,i,l),e[_t]=t.current,Or(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new ji(t)};Me.render=function(e,t,n){if(!Mi(t))throw Error(C(200));return bi(null,e,t,!1,n)};Me.unmountComponentAtNode=function(e){if(!Mi(e))throw Error(C(40));return e._reactRootContainer?(un(function(){bi(null,null,e,!1,function(){e._reactRootContainer=null,e[_t]=null})}),!0):!1};Me.unstable_batchedUpdates=_a;Me.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Mi(n))throw Error(C(200));if(e==null||e._reactInternals===void 0)throw Error(C(38));return bi(e,t,n,!1,r)};Me.version="18.3.1-next-f1338f8080-20240426";function Nf(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Nf)}catch(e){console.error(e)}}Nf(),Ic.exports=Me;var cg=Ic.exports,Fu=cg;Pl.createRoot=Fu.createRoot,Pl.hydrateRoot=Fu.hydrateRoot;/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dg=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Df=(...e)=>e.filter((t,n,r)=>!!t&&r.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var fg={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pg=I.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:i,iconNode:l,...s},a)=>I.createElement("svg",{ref:a,...fg,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Df("lucide",o),...s},[...l.map(([u,d])=>I.createElement(u,d)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const U=(e,t)=>{const n=I.forwardRef(({className:r,...o},i)=>I.createElement(pg,{ref:i,iconNode:t,className:Df(`lucide-${dg(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mg=U("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jf=U("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hg=U("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gg=U("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ca=U("CircleCheckBig",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mf=U("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _g=U("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yg=U("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vg=U("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Eg=U("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sg=U("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wg=U("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hn=U("KeyRound",[["path",{d:"M2 18v3c0 .6.4 1 1 1h4v-3h3v-3h2l1.4-1.4a6.5 6.5 0 1 0-4-4Z",key:"167ctg"}],["circle",{cx:"16.5",cy:"7.5",r:".5",fill:"currentColor",key:"w0ekpg"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qr=U("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kg=U("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xg=U("Paperclip",[["path",{d:"m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48",key:"1u3ebp"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cg=U("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bf=U("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vf=U("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rg=U("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pg=U("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tg=U("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lg=U("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Og=U("Terminal",[["polyline",{points:"4 17 10 11 4 5",key:"akl6gq"}],["line",{x1:"12",x2:"20",y1:"19",y2:"19",key:"q2wloq"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zf=U("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uf=U("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ag=U("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $f=U("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Ig={},Hu=e=>{let t;const n=new Set,r=(d,f)=>{const m=typeof d=="function"?d(t):d;if(!Object.is(m,t)){const g=t;t=f??(typeof m!="object"||m===null)?m:Object.assign({},t,m),n.forEach(_=>_(t,g))}},o=()=>t,a={setState:r,getState:o,getInitialState:()=>u,subscribe:d=>(n.add(d),()=>n.delete(d)),destroy:()=>{(Ig?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},u=t=e(r,o,a);return a},Ng=e=>e?Hu(e):Hu;var Bf={exports:{}},Ff={},Hf={exports:{}},Gf={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hn=I;function Dg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var jg=typeof Object.is=="function"?Object.is:Dg,Mg=Hn.useState,bg=Hn.useEffect,Vg=Hn.useLayoutEffect,zg=Hn.useDebugValue;function Ug(e,t){var n=t(),r=Mg({inst:{value:n,getSnapshot:t}}),o=r[0].inst,i=r[1];return Vg(function(){o.value=n,o.getSnapshot=t,yl(o)&&i({inst:o})},[e,n,t]),bg(function(){return yl(o)&&i({inst:o}),e(function(){yl(o)&&i({inst:o})})},[e]),zg(n),n}function yl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!jg(e,n)}catch{return!0}}function $g(e,t){return t()}var Bg=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?$g:Ug;Gf.useSyncExternalStore=Hn.useSyncExternalStore!==void 0?Hn.useSyncExternalStore:Bg;Hf.exports=Gf;var Fg=Hf.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vi=I,Hg=Fg;function Gg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Wg=typeof Object.is=="function"?Object.is:Gg,Kg=Hg.useSyncExternalStore,Qg=Vi.useRef,qg=Vi.useEffect,Xg=Vi.useMemo,Yg=Vi.useDebugValue;Ff.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var i=Qg(null);if(i.current===null){var l={hasValue:!1,value:null};i.current=l}else l=i.current;i=Xg(function(){function a(g){if(!u){if(u=!0,d=g,g=r(g),o!==void 0&&l.hasValue){var _=l.value;if(o(_,g))return f=_}return f=g}if(_=f,Wg(d,g))return _;var E=r(g);return o!==void 0&&o(_,E)?(d=g,_):(d=g,f=E)}var u=!1,d,f,m=n===void 0?null:n;return[function(){return a(t())},m===null?void 0:function(){return a(m())}]},[t,n,r,o]);var s=Kg(e,i[0],i[1]);return qg(function(){l.hasValue=!0,l.value=s},[s]),Yg(s),s};Bf.exports=Ff;var Jg=Bf.exports;const Zg=vc(Jg),Wf={},{useDebugValue:e_}=Oc,{useSyncExternalStoreWithSelector:t_}=Zg;let Gu=!1;const n_=e=>e;function r_(e,t=n_,n){(Wf?"production":void 0)!=="production"&&n&&!Gu&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),Gu=!0);const r=t_(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return e_(r),r}const Wu=e=>{(Wf?"production":void 0)!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof e=="function"?Ng(e):e,n=(r,o)=>r_(t,r,o);return Object.assign(n,t),n},zi=e=>e?Wu(e):Wu,Xr=zi(e=>({activeModal:null,modalData:null,openModal:(t,n=null)=>e({activeModal:t,modalData:n}),closeModal:()=>e({activeModal:null,modalData:null})})),o_="modulepreload",i_=function(e,t){return new URL(e,t).href},Ku={},h=function(t,n,r){let o=Promise.resolve();if(n&&n.length>0){const l=document.getElementsByTagName("link"),s=document.querySelector("meta[property=csp-nonce]"),a=(s==null?void 0:s.nonce)||(s==null?void 0:s.getAttribute("nonce"));o=Promise.allSettled(n.map(u=>{if(u=i_(u,r),u in Ku)return;Ku[u]=!0;const d=u.endsWith(".css"),f=d?'[rel="stylesheet"]':"";if(!!r)for(let _=l.length-1;_>=0;_--){const E=l[_];if(E.href===u&&(!d||E.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${u}"]${f}`))return;const g=document.createElement("link");if(g.rel=d?"stylesheet":o_,d||(g.as="script"),g.crossOrigin="",g.href=u,a&&g.setAttribute("nonce",a),document.head.appendChild(g),d)return new Promise((_,E)=>{g.addEventListener("load",_),g.addEventListener("error",()=>E(new Error(`Unable to preload CSS for ${u}`)))})}))}function i(l){const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=l,window.dispatchEvent(s),!s.defaultPrevented)throw l}return o.then(l=>{for(const s of l||[])s.status==="rejected"&&i(s.reason);return t().catch(i)})},l_={};function s_(e,t){let n;try{n=e()}catch{return}return{getItem:o=>{var i;const l=a=>a===null?null:JSON.parse(a,void 0),s=(i=n.getItem(o))!=null?i:null;return s instanceof Promise?s.then(l):l(s)},setItem:(o,i)=>n.setItem(o,JSON.stringify(i,void 0)),removeItem:o=>n.removeItem(o)}}const zr=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then(r){return zr(r)(n)},catch(r){return this}}}catch(n){return{then(r){return this},catch(r){return zr(r)(n)}}}},a_=(e,t)=>(n,r,o)=>{let i={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:S=>S,version:0,merge:(S,p)=>({...p,...S}),...t},l=!1;const s=new Set,a=new Set;let u;try{u=i.getStorage()}catch{}if(!u)return e((...S)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),n(...S)},r,o);const d=zr(i.serialize),f=()=>{const S=i.partialize({...r()});let p;const c=d({state:S,version:i.version}).then(y=>u.setItem(i.name,y)).catch(y=>{p=y});if(p)throw p;return c},m=o.setState;o.setState=(S,p)=>{m(S,p),f()};const g=e((...S)=>{n(...S),f()},r,o);let _;const E=()=>{var S;if(!u)return;l=!1,s.forEach(c=>c(r()));const p=((S=i.onRehydrateStorage)==null?void 0:S.call(i,r()))||void 0;return zr(u.getItem.bind(u))(i.name).then(c=>{if(c)return i.deserialize(c)}).then(c=>{if(c)if(typeof c.version=="number"&&c.version!==i.version){if(i.migrate)return i.migrate(c.state,c.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return c.state}).then(c=>{var y;return _=i.merge(c,(y=r())!=null?y:g),n(_,!0),f()}).then(()=>{p==null||p(_,void 0),l=!0,a.forEach(c=>c(_))}).catch(c=>{p==null||p(void 0,c)})};return o.persist={setOptions:S=>{i={...i,...S},S.getStorage&&(u=S.getStorage())},clearStorage:()=>{u==null||u.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>E(),hasHydrated:()=>l,onHydrate:S=>(s.add(S),()=>{s.delete(S)}),onFinishHydration:S=>(a.add(S),()=>{a.delete(S)})},E(),_||g},u_=(e,t)=>(n,r,o)=>{let i={storage:s_(()=>localStorage),partialize:E=>E,version:0,merge:(E,S)=>({...S,...E}),...t},l=!1;const s=new Set,a=new Set;let u=i.storage;if(!u)return e((...E)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),n(...E)},r,o);const d=()=>{const E=i.partialize({...r()});return u.setItem(i.name,{state:E,version:i.version})},f=o.setState;o.setState=(E,S)=>{f(E,S),d()};const m=e((...E)=>{n(...E),d()},r,o);o.getInitialState=()=>m;let g;const _=()=>{var E,S;if(!u)return;l=!1,s.forEach(c=>{var y;return c((y=r())!=null?y:m)});const p=((S=i.onRehydrateStorage)==null?void 0:S.call(i,(E=r())!=null?E:m))||void 0;return zr(u.getItem.bind(u))(i.name).then(c=>{if(c)if(typeof c.version=="number"&&c.version!==i.version){if(i.migrate)return[!0,i.migrate(c.state,c.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,c.state];return[!1,void 0]}).then(c=>{var y;const[w,x]=c;if(g=i.merge(x,(y=r())!=null?y:m),n(g,!0),w)return d()}).then(()=>{p==null||p(g,void 0),g=r(),l=!0,a.forEach(c=>c(g))}).catch(c=>{p==null||p(void 0,c)})};return o.persist={setOptions:E=>{i={...i,...E},E.storage&&(u=E.storage)},clearStorage:()=>{u==null||u.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>_(),hasHydrated:()=>l,onHydrate:E=>(s.add(E),()=>{s.delete(E)}),onFinishHydration:E=>(a.add(E),()=>{a.delete(E)})},i.skipHydration||_(),g||m},c_=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?((l_?"production":void 0)!=="production"&&console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),a_(e,t)):u_(e,t),Kf=c_;let So;const d_=new Uint8Array(16);function f_(){if(!So&&(So=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!So))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return So(d_)}const ue=[];for(let e=0;e<256;++e)ue.push((e+256).toString(16).slice(1));function p_(e,t=0){return ue[e[t+0]]+ue[e[t+1]]+ue[e[t+2]]+ue[e[t+3]]+"-"+ue[e[t+4]]+ue[e[t+5]]+"-"+ue[e[t+6]]+ue[e[t+7]]+"-"+ue[e[t+8]]+ue[e[t+9]]+"-"+ue[e[t+10]]+ue[e[t+11]]+ue[e[t+12]]+ue[e[t+13]]+ue[e[t+14]]+ue[e[t+15]]}const m_=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),Qu={randomUUID:m_};function Qf(e,t,n){if(Qu.randomUUID&&!e)return Qu.randomUUID();e=e||{};const r=e.random||(e.rng||f_)();return r[6]=r[6]&15|64,r[8]=r[8]&63|128,p_(r)}const Ht=zi(e=>({toasts:[],addToast:t=>{const n={...t,id:Date.now()};e(r=>({toasts:[...r.toasts,n]})),setTimeout(()=>{e(r=>({toasts:r.toasts.filter(o=>o.id!==n.id)}))},t.duration||5e3)},removeToast:t=>{e(n=>({toasts:n.toasts.filter(r=>r.id!==t)}))}})),Se={THEME_BRIGHTNESS_THRESHOLD:8421504,DEFAULT_OLLAMA_URL:"http://localhost:11434",OPENROUTER_REFERRER_URL:"https://sahai.com/cep",OPENROUTER_APP_TITLE:"SahAI CEP Extension",HEALTH_CHECK_INTERVAL_MS:3e4,MODEL_CACHE_DURATION_MS:36e5,DARK_THEME:{BG_COLOR:"#323232",TEXT_COLOR:"#F0F0F0",SECONDARY_BG_COLOR:"#3C3C3C",BORDER_COLOR:"#4A4A4A",SCROLLBAR_THUMB_COLOR:"#555555",SCROLLBAR_TRACK_COLOR:"#323232"},LIGHT_THEME:{BG_COLOR:"#F5F5F5",TEXT_COLOR:"#1a1a1a",SECONDARY_BG_COLOR:"#EAEAEA",BORDER_COLOR:"#D3D3D3",SCROLLBAR_THUMB_COLOR:"#C1C1C1",SCROLLBAR_TRACK_COLOR:"#F5F5F5"},ENDPOINTS:{OPENAI:"https://api.openai.com/v1",GROQ:"https://api.groq.com/openai/v1",DEEPSEEK:"https://api.deepseek.com",ANTHROPIC:"https://api.anthropic.com/v1",GOOGLE_GEMINI:"https://generativelanguage.googleapis.com/v1beta/models",OPENROUTER:"https://openrouter.ai/api/v1"},STORAGE_KEYS:{SETTINGS:"sahai-settings-storage"}},cn={log:(...e)=>{},warn:(...e)=>{},error:(...e)=>{console.error(...e)},debug:(...e)=>{},info:(...e)=>{}},qf={openai:{models:[]},anthropic:{models:[]},google:{models:[]},groq:{models:[]},deepseek:{models:[]},openrouter:{models:[]},ollama:{baseURL:"http://localhost:11434",models:[]}},Gt=zi()(Kf((e,t)=>({providers:qf,selectedProvider:"openai",selectedModel:"",theme:"auto",adobeTheme:null,modelCache:new Map,setProviderApiKey:(n,r)=>{Es(n,r),t().refreshProviderModels(n)},setOllamaBaseUrl:n=>{e(r=>({providers:{...r.providers,ollama:{...r.providers.ollama,baseURL:n}}}))},setSelectedProvider:n=>{e({selectedProvider:n,selectedModel:""}),t().providers[n].models.length===0&&t().refreshProviderModels(n)},setSelectedModel:n=>e({selectedModel:n}),setTheme:(n,r)=>e({theme:n,...r&&{adobeTheme:r}}),applyTheme:()=>{const{theme:n,adobeTheme:r}=t(),o=document.documentElement;if(n==="auto"&&r||n!=="auto"){const l=n==="dark"||r&&parseInt(r.backgroundColor.substring(1),16)<Se.THEME_BRIGHTNESS_THRESHOLD?Se.DARK_THEME:Se.LIGHT_THEME;o.style.setProperty("--adobe-bg-color",l.BG_COLOR),o.style.setProperty("--adobe-text-color",l.TEXT_COLOR),o.style.setProperty("--adobe-secondary-bg-color",l.SECONDARY_BG_COLOR),o.style.setProperty("--adobe-border-color",l.BORDER_COLOR),o.style.setProperty("--adobe-scrollbar-thumb-color",l.SCROLLBAR_THUMB_COLOR),o.style.setProperty("--adobe-scrollbar-track-color",l.SCROLLBAR_TRACK_COLOR)}},refreshProviderModels:async n=>{const{modelCache:r}=t(),o=r.get(n);if(o&&Date.now()-o.timestamp<Se.MODEL_CACHE_DURATION_MS){e(i=>({providers:{...i.providers,[n]:{...i.providers[n],models:o.models}}}));return}try{cn.log(`Fetching fresh models for ${n}`);const l=await Yr(n).getModels();e(s=>({providers:{...s.providers,[n]:{...s.providers[n],models:l}},modelCache:r.set(n,{models:l,timestamp:Date.now()})})),!t().selectedModel&&l.length>0&&t().setSelectedModel(l[0].id)}catch(i){cn.error(`Failed to fetch models for ${n}:`,i),Ht.getState().addToast({message:`Could not fetch models for ${n}. Check API key and connection.`,type:"error"}),e(l=>({providers:{...l.providers,[n]:{...l.providers[n],models:[]}}}))}}}),{name:Se.STORAGE_KEYS.SETTINGS,partialize:e=>({selectedProvider:e.selectedProvider,selectedModel:e.selectedModel,theme:e.theme,providers:{ollama:e.providers.ollama}})}));Object.keys(qf).forEach(e=>{mt(e)});const h_=Object.freeze(Object.defineProperty({__proto__:null,useSettingsStore:Gt},Symbol.toStringTag,{value:"Module"}));let Dn;const g_=()=>{typeof CSInterface<"u"&&(Dn=new CSInterface,Xf())},__=e=>new Promise((t,n)=>{if(!Dn){n("Not in a CEP environment.");return}Dn.evalScript(e,r=>{try{t(JSON.parse(r))}catch{t(r)}})}),Xf=()=>{if(!Dn)return;const e=Dn.getThemeInformation(),t={baseFontFamily:e.baseFontFamily,baseFontSize:e.baseFontSize,baseFontColor:`#${e.baseFontColor.color.hex}`,backgroundColor:`#${e.panelBackgroundColor.color.hex}`},n=y_(t.backgroundColor);Gt.getState().setTheme(n,t),Dn.addEventListener("com.adobe.csxs.events.ThemeColorChanged",()=>Xf())},y_=e=>{const t=e.substring(1),n=parseInt(t.substring(0,2),16),r=parseInt(t.substring(2,4),16),o=parseInt(t.substring(4,6),16);return(n*299+r*587+o*114)/1e3>125?"light":"dark"},Es=(e,t)=>{try{const n=btoa(t);localStorage.setItem(`sahai_api_key_${e}`,n)}catch(n){console.error("Failed to store credential:",n)}},mt=e=>{try{const t=localStorage.getItem(`sahai_api_key_${e}`);return t?atob(t):null}catch(t){return console.error("Failed to retrieve credential:",t),null}};class zo{constructor(t,n){this.providerId=t,this.baseUrl=n}async getModels(){if(!mt(this.providerId))return[];const n=await this.makeRequest("/models");return(n.data||n).map(o=>({id:o.id,name:o.id,description:`Owned by ${o.owned_by||"unknown"}`,contextLength:o.context_window||o.context_length})).sort((o,i)=>o.name.localeCompare(i.name))}async*chat(t,n){if(!mt(this.providerId))throw new Error(`API key for ${this.providerId} not found.`);const o=JSON.stringify({model:n,messages:t.map(({role:l,content:s})=>({role:l,content:s})),stream:!0}),i=await this.makeRequest("/chat/completions",{method:"POST",body:o},!0);yield*this.processStream(i)}async makeRequest(t,n={},r=!1){var s;const o=mt(this.providerId),i=new Headers(n.headers||{});i.set("Content-Type","application/json"),i.set("Authorization",`Bearer ${o}`);const l=await fetch(`${this.baseUrl}${t}`,{...n,headers:i});if(!l.ok){const a=await l.json().catch(()=>({}));throw new Error(`API Error (${l.status}): ${((s=a.error)==null?void 0:s.message)||l.statusText}`)}return r?l:l.json()}async*processStream(t){var o,i,l;const n=(o=t.body)==null?void 0:o.getReader();if(!n)throw new Error("Failed to read stream.");const r=new TextDecoder;for(;;){const{done:s,value:a}=await n.read();if(s)break;const d=r.decode(a).split(`

`);for(const f of d)if(f.startsWith("data: ")){const m=f.substring(6);if(m.trim()==="[DONE]")return;try{const _=(l=(i=JSON.parse(m).choices[0])==null?void 0:i.delta)==null?void 0:l.content;_&&(yield _)}catch{cn.error("Error parsing stream data chunk:",m)}}}}}class v_{constructor(){k(this,"BASE_URL",Se.ENDPOINTS.ANTHROPIC)}async getModels(){return Promise.resolve([{id:"claude-3-opus-20240229",name:"Claude 3 Opus",contextLength:2e5},{id:"claude-3-sonnet-20240229",name:"Claude 3 Sonnet",contextLength:2e5},{id:"claude-3-haiku-20240307",name:"Claude 3 Haiku",contextLength:2e5},{id:"claude-2.1",name:"Claude 2.1",contextLength:2e5},{id:"claude-2.0",name:"Claude 2.0",contextLength:1e5}])}async*chat(t,n){var d,f,m;const r=mt("anthropic");if(!r)throw new Error("API key for Anthropic not found.");const o=(d=t.find(g=>g.role==="system"))==null?void 0:d.content,i=t.filter(g=>g.role!=="system"),l=JSON.stringify({model:n,messages:i.map(({role:g,content:_})=>({role:g,content:_})),...o&&{system:o},stream:!0,max_tokens:4096}),s=await fetch(`${this.BASE_URL}/messages`,{method:"POST",headers:{"Content-Type":"application/json","x-api-key":r,"anthropic-version":"2023-06-01"},body:l});if(!s.ok){const g=await s.json().catch(()=>({}));throw new Error(`API Error (${s.status}): ${((f=g.error)==null?void 0:f.message)||s.statusText}`)}const a=(m=s.body)==null?void 0:m.getReader();if(!a)throw new Error("Failed to read stream.");const u=new TextDecoder;for(;;){const{done:g,value:_}=await a.read();if(g)break;const S=u.decode(_).split(`
`);for(const p of S)if(p.startsWith("data: "))try{const c=JSON.parse(p.substring(6));c.type==="content_block_delta"&&c.delta.type==="text_delta"&&(yield c.delta.text)}catch{cn.error("Error parsing Anthropic stream chunk:",p)}}}}class E_{constructor(){k(this,"BASE_URL",Se.ENDPOINTS.GOOGLE_GEMINI)}async getModels(){const t=mt("google");return t?(await(await fetch(`${this.BASE_URL}?key=${t}`)).json()).models.filter(o=>o.supportedGenerationMethods.includes("generateContent")).map(o=>({id:o.name,name:o.displayName,description:o.description,contextLength:o.inputTokenLimit})).sort((o,i)=>o.name.localeCompare(i.name)):[]}async*chat(t,n){var d,f,m,g,_,E,S;const r=mt("google");if(!r)throw new Error("API key for Google Gemini not found.");const o=t.map(p=>({role:p.role==="assistant"?"model":"user",parts:[{text:p.content}]})),i=JSON.stringify({contents:o}),l=n.split("/").pop(),s=await fetch(`${this.BASE_URL}/${l}:streamGenerateContent?key=${r}&alt=sse`,{method:"POST",headers:{"Content-Type":"application/json"},body:i});if(!s.ok){const p=await s.json().catch(()=>({}));throw new Error(`API Error (${s.status}): ${((d=p.error)==null?void 0:d.message)||s.statusText}`)}const a=(f=s.body)==null?void 0:f.getReader();if(!a)throw new Error("Failed to read stream.");const u=new TextDecoder;for(;;){const{done:p,value:c}=await a.read();if(p)break;const w=u.decode(c).split(`
`);for(const x of w)if(x.startsWith("data: "))try{const P=(S=(E=(_=(g=(m=JSON.parse(x.substring(6)).candidates)==null?void 0:m[0])==null?void 0:g.content)==null?void 0:_.parts)==null?void 0:E[0])==null?void 0:S.text;P&&(yield P)}catch{}}}}class S_ extends zo{constructor(){super("openrouter",Se.ENDPOINTS.OPENROUTER)}async makeRequest(t,n={},r=!1){var s;const o=mt(this.providerId),i=new Headers(n.headers||{});i.set("Content-Type","application/json"),i.set("Authorization",`Bearer ${o}`),i.set("HTTP-Referer",Se.OPENROUTER_REFERRER_URL),i.set("X-Title",Se.OPENROUTER_APP_TITLE);const l=await fetch(`${this.baseUrl}${t}`,{...n,headers:i});if(!l.ok){const a=await l.json().catch(()=>({}));throw new Error(`API Error (${l.status}): ${((s=a.error)==null?void 0:s.message)||l.statusText}`)}return r?l:l.json()}}class w_{constructor(t){this.baseUrl=t}async getModels(){try{const t=await fetch(`${this.baseUrl}/api/tags`);return t.ok?(await t.json()).models.map(r=>({id:r.name,name:r.name,description:`Size: ${(r.size/1e9).toFixed(2)} GB`})).sort((r,o)=>r.name.localeCompare(o.name)):[]}catch(t){return cn.error("Failed to connect to Ollama:",t),[]}}async*chat(t,n){var s,a;const r=JSON.stringify({model:n,messages:t.map(({role:u,content:d})=>({role:u,content:d})),stream:!0}),o=await fetch(`${this.baseUrl}/api/chat`,{method:"POST",headers:{"Content-Type":"application/json"},body:r});if(!o.ok){const u=await o.json().catch(()=>({}));throw new Error(`Ollama Error (${o.status}): ${u.error||o.statusText}`)}const i=(s=o.body)==null?void 0:s.getReader();if(!i)throw new Error("Failed to read stream.");const l=new TextDecoder;for(;;){const{done:u,value:d}=await i.read();if(u)break;const m=l.decode(d).split(`
`);for(const g of m)if(g.trim())try{const _=JSON.parse(g);if((a=_.message)!=null&&a.content&&(yield _.message.content),_.done)return}catch{cn.error("Error parsing Ollama stream chunk:",g)}}}}function Yr(e,t){const n=Gt.getState().providers[e];switch(e){case"openai":return new zo(e,Se.ENDPOINTS.OPENAI);case"groq":return new zo(e,Se.ENDPOINTS.GROQ);case"deepseek":return new zo(e,Se.ENDPOINTS.DEEPSEEK);case"anthropic":return new v_;case"google":return new E_;case"openrouter":return new S_;case"ollama":const r=(t==null?void 0:t.baseURL)||(n==null?void 0:n.baseURL)||Se.DEFAULT_OLLAMA_URL;return new w_(r);default:const o=e;throw new Error(`Provider ${o} not implemented.`)}}const qu=()=>({id:Qf(),title:"New Chat",messages:[],createdAt:new Date().toISOString()}),qn=zi()(Kf((e,t)=>({conversations:{},currentConversationId:null,isLoading:!1,addMessage:(n,r)=>{const o={...n,id:Qf(),timestamp:new Date().toISOString(),status:"sent"};return e(i=>{const l=i.conversations[r];return{conversations:{...i.conversations,[r]:{...l,messages:[...l.messages,o]}}}}),o.id},updateMessageStatus:(n,r)=>{e(o=>{const{currentConversationId:i,conversations:l}=o;if(!i||!l[i])return{};const s=l[i].messages,a=s.findIndex(d=>d.id===n);if(a===-1)return{};const u=[...s];return u[a]={...u[a],status:r},{conversations:{...o.conversations,[i]:{...l[i],messages:u}}}})},appendTokenToMessage:(n,r)=>{e(o=>{const{currentConversationId:i,conversations:l}=o;if(!i||!l[i])return{};const s=l[i].messages,a=s.findIndex(f=>f.id===n);if(a===-1)return{};const u=[...s],d=u[a];return u[a]={...d,content:d.content+r},{conversations:{...o.conversations,[i]:{...l[i],messages:u}}}})},sendChatMessage:async n=>{let{currentConversationId:r}=t();if(!r){const i=qu();e(l=>({conversations:{...l.conversations,[i.id]:i},currentConversationId:i.id})),r=i.id}t().addMessage({role:"user",content:n},r);const o=t().addMessage({role:"assistant",content:""},r);t().updateMessageStatus(o,"streaming"),e({isLoading:!0});try{const{useSettingsStore:i}=await h(async()=>{const{useSettingsStore:f}=await Promise.resolve().then(()=>h_);return{useSettingsStore:f}},void 0,import.meta.url),{selectedProvider:l,selectedModel:s}=i.getState(),a=Yr(l),u=t().conversations[r].messages.slice(0,-1),d=a.chat(u,s);for await(const f of d)t().appendTokenToMessage(o,f);t().updateMessageStatus(o,"sent")}catch(i){t().updateMessageStatus(o,"error"),t().appendTokenToMessage(o,`**Error:** ${i.message}`),Ht.getState().addToast({message:i.message,type:"error"})}finally{e({isLoading:!1})}},retryLastUserMessage:async()=>{const{currentConversationId:n,conversations:r}=t();if(!n||!r[n])return;const o=r[n].messages,i=o.map(a=>a.role).lastIndexOf("assistant");if(i===-1||o[i].status!=="error")return;const l=o[i-1];if((l==null?void 0:l.role)!=="user")return;const s=o.slice(0,i);e(a=>({conversations:{...a.conversations,[n]:{...r[n],messages:s}}})),t().sendChatMessage(l.content)},startNewConversation:()=>{const n=qu();e(r=>({conversations:{...r.conversations,[n.id]:n},currentConversationId:n.id}))},setCurrentConversationId:n=>e({currentConversationId:n}),clearAllConversations:()=>e({conversations:{},currentConversationId:null})}),{name:"sahai-chat-storage"})),k_=()=>{const e=Gt(u=>u.selectedProvider),t=Xr(u=>u.openModal),[n,r]=I.useState("idle"),[o,i]=I.useState(null),l=I.useCallback(async()=>{if(!e){r("idle");return}r("loading");const u=Date.now();try{await Yr(e).getModels(),i(Date.now()-u),r("ok")}catch{r("error"),i(null)}},[e]);I.useEffect(()=>{l();const u=setInterval(l,Se.HEALTH_CHECK_INTERVAL_MS);return()=>clearInterval(u)},[l]);const s={idle:"bg-gray-400",loading:"bg-yellow-500 animate-pulse",ok:"bg-green-500",error:"bg-red-500"},a={idle:"Status: Idle",loading:"Status: Checking connection...",ok:`Status: Connected | Latency: ${o??"N/A"}ms. Click for details.`,error:"Status: Connection failed. Click for details."};return v.jsx("div",{className:"flex items-center gap-2 cursor-pointer",title:a[n],onClick:()=>t("status"),children:n==="loading"?v.jsx(qr,{size:14,className:"animate-spin text-yellow-500"}):v.jsx("div",{className:`w-3 h-3 rounded-full ${s[n]}`})})},x_=({options:e,value:t,onChange:n,placeholder:r="Select a model...",disabled:o=!1})=>{const[i,l]=I.useState(!1),[s,a]=I.useState(""),[u,d]=I.useState(-1),f=I.useRef(null),m=I.useRef(null),g=I.useMemo(()=>e.find(p=>p.id===t),[e,t]),_=I.useMemo(()=>e.filter(p=>p.name.toLowerCase().includes(s.toLowerCase())||p.id.toLowerCase().includes(s.toLowerCase())),[e,s]);I.useEffect(()=>{const p=c=>{f.current&&!f.current.contains(c.target)&&l(!1)};return document.addEventListener("mousedown",p),()=>document.removeEventListener("mousedown",p)},[]),I.useEffect(()=>{const p=c=>{if(i)switch(c.key){case"ArrowDown":c.preventDefault(),d(y=>Math.min(y+1,_.length-1));break;case"ArrowUp":c.preventDefault(),d(y=>Math.max(y-1,0));break;case"Enter":c.preventDefault(),u>=0&&_[u]&&E(_[u]);break;case"Escape":l(!1);break}};return window.addEventListener("keydown",p),()=>window.removeEventListener("keydown",p)},[i,u,_]);const E=p=>{n(p.id),a(""),l(!1)},S=()=>{o||(l(!0),setTimeout(()=>{var p;return(p=m.current)==null?void 0:p.focus()},0))};return v.jsxs("div",{ref:f,className:"relative w-full",children:[v.jsxs("button",{onClick:S,disabled:o,className:"flex items-center justify-between w-full px-2 py-1 text-left bg-adobe-secondary rounded border border-transparent hover:border-adobe disabled:opacity-50",children:[v.jsx("span",{className:"truncate text-xs",children:(g==null?void 0:g.name)||r}),v.jsx(jf,{size:14,className:"text-gray-400"})]}),i&&v.jsxs("div",{className:"absolute z-10 w-full mt-1 bg-adobe-bg border border-adobe rounded-md shadow-lg max-h-60 overflow-y-auto",children:[v.jsx("div",{className:"p-2 border-b border-adobe sticky top-0 bg-adobe-bg",children:v.jsxs("div",{className:"relative",children:[v.jsx(Rg,{size:14,className:"absolute left-2 top-1/2 -translate-y-1/2 text-gray-400"}),v.jsx("input",{ref:m,type:"text",value:s,onChange:p=>a(p.target.value),placeholder:"Search models...",className:"w-full pl-7 pr-2 py-1 bg-adobe-secondary border border-adobe rounded text-xs focus:outline-none"})]})}),v.jsx("ul",{className:"py-1",children:_.length>0?_.map((p,c)=>v.jsx("li",{onClick:()=>E(p),onMouseEnter:()=>d(c),className:`px-3 py-1.5 text-xs cursor-pointer ${c===u?"bg-blue-600 text-white":"hover:bg-adobe-secondary"} ${t===p.id?"font-bold":""}`,children:p.name},p.id)):v.jsx("li",{className:"px-3 py-1.5 text-xs text-gray-500",children:"No results found"})})]})]})},C_=()=>{var s;const e=Xr(a=>a.openModal),t=qn(a=>a.startNewConversation),{selectedProvider:n,selectedModel:r,setSelectedModel:o,providers:i}=Gt(),l=((s=i[n])==null?void 0:s.models)||[];return v.jsxs("header",{className:"flex items-center justify-between p-2 border-b border-adobe bg-adobe-secondary flex-shrink-0 gap-2",children:[v.jsxs("div",{className:"flex items-center gap-2 flex-shrink min-w-0",children:[v.jsx(k_,{}),v.jsxs("div",{className:"flex flex-col gap-1 min-w-0",children:[v.jsx("p",{className:"text-xs font-bold truncate capitalize",children:n}),v.jsx(x_,{options:l,value:r,onChange:o,placeholder:l.length===0?"No models found":"Select a model",disabled:l.length===0})]})]}),v.jsxs("div",{className:"flex items-center gap-1 flex-shrink-0",children:[v.jsx("button",{onClick:t,className:"p-1.5 rounded hover:bg-adobe-bg","aria-label":"New Chat",children:v.jsx(Cg,{size:16})}),v.jsx("button",{onClick:()=>e("history"),className:"p-1.5 rounded hover:bg-adobe-bg","aria-label":"Chat History",children:v.jsx(Sg,{size:16})}),v.jsx("button",{onClick:()=>e("settings"),className:"p-1.5 rounded hover:bg-adobe-bg","aria-label":"Settings",children:v.jsx(Lg,{size:16})})]})]})},Yf=[{id:"abap",name:"ABAP",import:()=>h(()=>import("./abap-DsBKuouk.js"),[],import.meta.url)},{id:"actionscript-3",name:"ActionScript",import:()=>h(()=>import("./actionscript-3-D_z4Izcz.js"),[],import.meta.url)},{id:"ada",name:"Ada",import:()=>h(()=>import("./ada-727ZlQH0.js"),[],import.meta.url)},{id:"angular-html",name:"Angular HTML",import:()=>h(()=>import("./angular-html-LfdN0zeE.js").then(e=>e.f),__vite__mapDeps([0,1,2,3]),import.meta.url)},{id:"angular-ts",name:"Angular TypeScript",import:()=>h(()=>import("./angular-ts-CKsD7JZE.js"),__vite__mapDeps([4,0,1,2,3,5]),import.meta.url)},{id:"apache",name:"Apache Conf",import:()=>h(()=>import("./apache-Dn00JSTd.js"),[],import.meta.url)},{id:"apex",name:"Apex",import:()=>h(()=>import("./apex-COJ4H7py.js"),[],import.meta.url)},{id:"apl",name:"APL",import:()=>h(()=>import("./apl-BBq3IX1j.js"),__vite__mapDeps([6,1,2,3,7,8,9]),import.meta.url)},{id:"applescript",name:"AppleScript",import:()=>h(()=>import("./applescript-Bu5BbsvL.js"),[],import.meta.url)},{id:"ara",name:"Ara",import:()=>h(()=>import("./ara-7O62HKoU.js"),[],import.meta.url)},{id:"asciidoc",name:"AsciiDoc",aliases:["adoc"],import:()=>h(()=>import("./asciidoc-BPT9niGB.js"),[],import.meta.url)},{id:"asm",name:"Assembly",import:()=>h(()=>import("./asm-Dhn9LcZ4.js"),[],import.meta.url)},{id:"astro",name:"Astro",import:()=>h(()=>import("./astro-CqkE3fuf.js"),__vite__mapDeps([10,9,2,11,3,12]),import.meta.url)},{id:"awk",name:"AWK",import:()=>h(()=>import("./awk-eg146-Ew.js"),[],import.meta.url)},{id:"ballerina",name:"Ballerina",import:()=>h(()=>import("./ballerina-Du268qiB.js"),[],import.meta.url)},{id:"bat",name:"Batch File",aliases:["batch"],import:()=>h(()=>import("./bat-fje9CFhw.js"),[],import.meta.url)},{id:"beancount",name:"Beancount",import:()=>h(()=>import("./beancount-BwXTMy5W.js"),[],import.meta.url)},{id:"berry",name:"Berry",aliases:["be"],import:()=>h(()=>import("./berry-3xVqZejG.js"),[],import.meta.url)},{id:"bibtex",name:"BibTeX",import:()=>h(()=>import("./bibtex-xW4inM5L.js"),[],import.meta.url)},{id:"bicep",name:"Bicep",import:()=>h(()=>import("./bicep-DHo0CJ0O.js"),[],import.meta.url)},{id:"blade",name:"Blade",import:()=>h(()=>import("./blade-a8OxSdnT.js"),__vite__mapDeps([13,1,2,3,7,8,14,9]),import.meta.url)},{id:"bsl",name:"1C (Enterprise)",aliases:["1c"],import:()=>h(()=>import("./bsl-Dgyn0ogV.js"),__vite__mapDeps([15,16]),import.meta.url)},{id:"c",name:"C",import:()=>h(()=>import("./c-C3t2pwGQ.js"),[],import.meta.url)},{id:"cadence",name:"Cadence",aliases:["cdc"],import:()=>h(()=>import("./cadence-DNquZEk8.js"),[],import.meta.url)},{id:"cairo",name:"Cairo",import:()=>h(()=>import("./cairo--RitsXJZ.js"),__vite__mapDeps([17,18]),import.meta.url)},{id:"clarity",name:"Clarity",import:()=>h(()=>import("./clarity-BHOwM8T6.js"),[],import.meta.url)},{id:"clojure",name:"Clojure",aliases:["clj"],import:()=>h(()=>import("./clojure-DxSadP1t.js"),[],import.meta.url)},{id:"cmake",name:"CMake",import:()=>h(()=>import("./cmake-DbXoA79R.js"),[],import.meta.url)},{id:"cobol",name:"COBOL",import:()=>h(()=>import("./cobol-PTqiYgYu.js"),__vite__mapDeps([19,1,2,3,8]),import.meta.url)},{id:"codeowners",name:"CODEOWNERS",import:()=>h(()=>import("./codeowners-Bp6g37R7.js"),[],import.meta.url)},{id:"codeql",name:"CodeQL",aliases:["ql"],import:()=>h(()=>import("./codeql-sacFqUAJ.js"),[],import.meta.url)},{id:"coffee",name:"CoffeeScript",aliases:["coffeescript"],import:()=>h(()=>import("./coffee-dyiR41kL.js"),__vite__mapDeps([20,2]),import.meta.url)},{id:"common-lisp",name:"Common Lisp",aliases:["lisp"],import:()=>h(()=>import("./common-lisp-C7gG9l05.js"),[],import.meta.url)},{id:"coq",name:"Coq",import:()=>h(()=>import("./coq-Dsg_Bt_b.js"),[],import.meta.url)},{id:"cpp",name:"C++",aliases:["c++"],import:()=>h(()=>import("./cpp-BksuvNSY.js"),__vite__mapDeps([21,22,23,24,14]),import.meta.url)},{id:"crystal",name:"Crystal",import:()=>h(()=>import("./crystal-DtDmRg-F.js"),__vite__mapDeps([25,1,2,3,14,24,26]),import.meta.url)},{id:"csharp",name:"C#",aliases:["c#","cs"],import:()=>h(()=>import("./csharp-D9R-vmeu.js"),[],import.meta.url)},{id:"css",name:"CSS",import:()=>h(()=>import("./css-BPhBrDlE.js"),[],import.meta.url)},{id:"csv",name:"CSV",import:()=>h(()=>import("./csv-B0qRVHPH.js"),[],import.meta.url)},{id:"cue",name:"CUE",import:()=>h(()=>import("./cue-DtFQj3wx.js"),[],import.meta.url)},{id:"cypher",name:"Cypher",aliases:["cql"],import:()=>h(()=>import("./cypher-m2LEI-9-.js"),[],import.meta.url)},{id:"d",name:"D",import:()=>h(()=>import("./d-BoXegm-a.js"),[],import.meta.url)},{id:"dart",name:"Dart",import:()=>h(()=>import("./dart-B9wLZaAG.js"),[],import.meta.url)},{id:"dax",name:"DAX",import:()=>h(()=>import("./dax-ClGRhx96.js"),[],import.meta.url)},{id:"desktop",name:"Desktop",import:()=>h(()=>import("./desktop-DEIpsLCJ.js"),[],import.meta.url)},{id:"diff",name:"Diff",import:()=>h(()=>import("./diff-BgYniUM_.js"),[],import.meta.url)},{id:"docker",name:"Dockerfile",aliases:["dockerfile"],import:()=>h(()=>import("./docker-COcR7UxN.js"),[],import.meta.url)},{id:"dotenv",name:"dotEnv",import:()=>h(()=>import("./dotenv-BjQB5zDj.js"),[],import.meta.url)},{id:"dream-maker",name:"Dream Maker",import:()=>h(()=>import("./dream-maker-C-nORZOA.js"),[],import.meta.url)},{id:"edge",name:"Edge",import:()=>h(()=>import("./edge-D5gP-w-T.js"),__vite__mapDeps([27,11,1,2,3,28]),import.meta.url)},{id:"elixir",name:"Elixir",import:()=>h(()=>import("./elixir-CLiX3zqd.js"),__vite__mapDeps([29,1,2,3]),import.meta.url)},{id:"elm",name:"Elm",import:()=>h(()=>import("./elm-CmHSxxaM.js"),__vite__mapDeps([30,23,24]),import.meta.url)},{id:"emacs-lisp",name:"Emacs Lisp",aliases:["elisp"],import:()=>h(()=>import("./emacs-lisp-BX77sIaO.js"),[],import.meta.url)},{id:"erb",name:"ERB",import:()=>h(()=>import("./erb-BYTLMnw6.js"),__vite__mapDeps([31,1,2,3,32,33,7,8,14,34,11,35,36,21,22,23,24,26,37,38]),import.meta.url)},{id:"erlang",name:"Erlang",aliases:["erl"],import:()=>h(()=>import("./erlang-B-DoSBHF.js"),[],import.meta.url)},{id:"fennel",name:"Fennel",import:()=>h(()=>import("./fennel-bCA53EVm.js"),[],import.meta.url)},{id:"fish",name:"Fish",import:()=>h(()=>import("./fish-w-ucz2PV.js"),[],import.meta.url)},{id:"fluent",name:"Fluent",aliases:["ftl"],import:()=>h(()=>import("./fluent-Dayu4EKP.js"),[],import.meta.url)},{id:"fortran-fixed-form",name:"Fortran (Fixed Form)",aliases:["f","for","f77"],import:()=>h(()=>import("./fortran-fixed-form-TqA4NnZg.js"),__vite__mapDeps([39,40]),import.meta.url)},{id:"fortran-free-form",name:"Fortran (Free Form)",aliases:["f90","f95","f03","f08","f18"],import:()=>h(()=>import("./fortran-free-form-DKXYxT9g.js"),[],import.meta.url)},{id:"fsharp",name:"F#",aliases:["f#","fs"],import:()=>h(()=>import("./fsharp-XplgxFYe.js"),__vite__mapDeps([41,42]),import.meta.url)},{id:"gdresource",name:"GDResource",import:()=>h(()=>import("./gdresource-BHYsBjWJ.js"),__vite__mapDeps([43,44,45]),import.meta.url)},{id:"gdscript",name:"GDScript",import:()=>h(()=>import("./gdscript-DfxzS6Rs.js"),[],import.meta.url)},{id:"gdshader",name:"GDShader",import:()=>h(()=>import("./gdshader-SKMF96pI.js"),[],import.meta.url)},{id:"genie",name:"Genie",import:()=>h(()=>import("./genie-ajMbGru0.js"),[],import.meta.url)},{id:"gherkin",name:"Gherkin",import:()=>h(()=>import("./gherkin--30QC5Em.js"),[],import.meta.url)},{id:"git-commit",name:"Git Commit Message",import:()=>h(()=>import("./git-commit-i4q6IMui.js"),__vite__mapDeps([46,47]),import.meta.url)},{id:"git-rebase",name:"Git Rebase Message",import:()=>h(()=>import("./git-rebase-B-v9cOL2.js"),__vite__mapDeps([48,26]),import.meta.url)},{id:"gleam",name:"Gleam",import:()=>h(()=>import("./gleam-B430Bg39.js"),[],import.meta.url)},{id:"glimmer-js",name:"Glimmer JS",aliases:["gjs"],import:()=>h(()=>import("./glimmer-js-D-cwc0-E.js"),__vite__mapDeps([49,2,11,3,1]),import.meta.url)},{id:"glimmer-ts",name:"Glimmer TS",aliases:["gts"],import:()=>h(()=>import("./glimmer-ts-pgjy16dm.js"),__vite__mapDeps([50,11,3,2,1]),import.meta.url)},{id:"glsl",name:"GLSL",import:()=>h(()=>import("./glsl-DBO2IWDn.js"),__vite__mapDeps([23,24]),import.meta.url)},{id:"gnuplot",name:"Gnuplot",import:()=>h(()=>import("./gnuplot-CM8KxXT1.js"),[],import.meta.url)},{id:"go",name:"Go",import:()=>h(()=>import("./go-B1SYOhNW.js"),[],import.meta.url)},{id:"graphql",name:"GraphQL",aliases:["gql"],import:()=>h(()=>import("./graphql-cDcHW_If.js"),__vite__mapDeps([34,2,11,35,36]),import.meta.url)},{id:"groovy",name:"Groovy",import:()=>h(()=>import("./groovy-DkBy-JyN.js"),[],import.meta.url)},{id:"hack",name:"Hack",import:()=>h(()=>import("./hack-D1yCygmZ.js"),__vite__mapDeps([51,1,2,3,14]),import.meta.url)},{id:"haml",name:"Ruby Haml",import:()=>h(()=>import("./haml-B2EZWmdv.js"),__vite__mapDeps([33,2,3]),import.meta.url)},{id:"handlebars",name:"Handlebars",aliases:["hbs"],import:()=>h(()=>import("./handlebars-BQGss363.js"),__vite__mapDeps([52,1,2,3,38]),import.meta.url)},{id:"haskell",name:"Haskell",aliases:["hs"],import:()=>h(()=>import("./haskell-BILxekzW.js"),[],import.meta.url)},{id:"haxe",name:"Haxe",import:()=>h(()=>import("./haxe-C5wWYbrZ.js"),[],import.meta.url)},{id:"hcl",name:"HashiCorp HCL",import:()=>h(()=>import("./hcl-HzYwdGDm.js"),[],import.meta.url)},{id:"hjson",name:"Hjson",import:()=>h(()=>import("./hjson-T-Tgc4AT.js"),[],import.meta.url)},{id:"hlsl",name:"HLSL",import:()=>h(()=>import("./hlsl-ifBTmRxC.js"),[],import.meta.url)},{id:"html",name:"HTML",import:()=>h(()=>import("./html-C2L_23MC.js"),__vite__mapDeps([1,2,3]),import.meta.url)},{id:"html-derivative",name:"HTML (Derivative)",import:()=>h(()=>import("./html-derivative-CSfWNPLT.js"),__vite__mapDeps([28,1,2,3]),import.meta.url)},{id:"http",name:"HTTP",import:()=>h(()=>import("./http-FRrOvY1W.js"),__vite__mapDeps([53,26,9,7,8,34,2,11,35,36]),import.meta.url)},{id:"hxml",name:"HXML",import:()=>h(()=>import("./hxml-TIA70rKU.js"),__vite__mapDeps([54,55]),import.meta.url)},{id:"hy",name:"Hy",import:()=>h(()=>import("./hy-BMj5Y0dO.js"),[],import.meta.url)},{id:"imba",name:"Imba",import:()=>h(()=>import("./imba-bv_oIlVt.js"),__vite__mapDeps([56,11]),import.meta.url)},{id:"ini",name:"INI",aliases:["properties"],import:()=>h(()=>import("./ini-BjABl1g7.js"),[],import.meta.url)},{id:"java",name:"Java",import:()=>h(()=>import("./java-xI-RfyKK.js"),[],import.meta.url)},{id:"javascript",name:"JavaScript",aliases:["js"],import:()=>h(()=>import("./javascript-ySlJ1b_l.js"),[],import.meta.url)},{id:"jinja",name:"Jinja",import:()=>h(()=>import("./jinja-DGy0s7-h.js"),__vite__mapDeps([57,1,2,3]),import.meta.url)},{id:"jison",name:"Jison",import:()=>h(()=>import("./jison-BqZprYcd.js"),__vite__mapDeps([58,2]),import.meta.url)},{id:"json",name:"JSON",import:()=>h(()=>import("./json-BQoSv7ci.js"),[],import.meta.url)},{id:"json5",name:"JSON5",import:()=>h(()=>import("./json5-w8dY5SsB.js"),[],import.meta.url)},{id:"jsonc",name:"JSON with Comments",import:()=>h(()=>import("./jsonc-TU54ms6u.js"),[],import.meta.url)},{id:"jsonl",name:"JSON Lines",import:()=>h(()=>import("./jsonl-DREVFZK8.js"),[],import.meta.url)},{id:"jsonnet",name:"Jsonnet",import:()=>h(()=>import("./jsonnet-BfivnA6A.js"),[],import.meta.url)},{id:"jssm",name:"JSSM",aliases:["fsl"],import:()=>h(()=>import("./jssm-P4WzXJd0.js"),[],import.meta.url)},{id:"jsx",name:"JSX",import:()=>h(()=>import("./jsx-BAng5TT0.js"),[],import.meta.url)},{id:"julia",name:"Julia",aliases:["jl"],import:()=>h(()=>import("./julia-BBuGR-5E.js"),__vite__mapDeps([59,21,22,23,24,14,18,2,60]),import.meta.url)},{id:"kotlin",name:"Kotlin",aliases:["kt","kts"],import:()=>h(()=>import("./kotlin-B5lbUyaz.js"),[],import.meta.url)},{id:"kusto",name:"Kusto",aliases:["kql"],import:()=>h(()=>import("./kusto-mebxcVVE.js"),[],import.meta.url)},{id:"latex",name:"LaTeX",import:()=>h(()=>import("./latex-C-cWTeAZ.js"),__vite__mapDeps([61,62,60]),import.meta.url)},{id:"lean",name:"Lean 4",aliases:["lean4"],import:()=>h(()=>import("./lean-XBlWyCtg.js"),[],import.meta.url)},{id:"less",name:"Less",import:()=>h(()=>import("./less-BfCpw3nA.js"),[],import.meta.url)},{id:"liquid",name:"Liquid",import:()=>h(()=>import("./liquid-D3W5UaiH.js"),__vite__mapDeps([63,1,2,3,9]),import.meta.url)},{id:"log",name:"Log file",import:()=>h(()=>import("./log-Cc5clBb7.js"),[],import.meta.url)},{id:"logo",name:"Logo",import:()=>h(()=>import("./logo-IuBKFhSY.js"),[],import.meta.url)},{id:"lua",name:"Lua",import:()=>h(()=>import("./lua-CvWAzNxB.js"),__vite__mapDeps([37,24]),import.meta.url)},{id:"luau",name:"Luau",import:()=>h(()=>import("./luau-Du5NY7AG.js"),[],import.meta.url)},{id:"make",name:"Makefile",aliases:["makefile"],import:()=>h(()=>import("./make-Bvotw-X0.js"),[],import.meta.url)},{id:"markdown",name:"Markdown",aliases:["md"],import:()=>h(()=>import("./markdown-UIAJJxZW.js"),[],import.meta.url)},{id:"marko",name:"Marko",import:()=>h(()=>import("./marko-z0MBrx5-.js"),__vite__mapDeps([64,3,65,5,2]),import.meta.url)},{id:"matlab",name:"MATLAB",import:()=>h(()=>import("./matlab-D9-PGadD.js"),[],import.meta.url)},{id:"mdc",name:"MDC",import:()=>h(()=>import("./mdc-DB_EDNY_.js"),__vite__mapDeps([66,42,38,28,1,2,3]),import.meta.url)},{id:"mdx",name:"MDX",import:()=>h(()=>import("./mdx-sdHcTMYB.js"),[],import.meta.url)},{id:"mermaid",name:"Mermaid",aliases:["mmd"],import:()=>h(()=>import("./mermaid-Ci6OQyBP.js"),[],import.meta.url)},{id:"mipsasm",name:"MIPS Assembly",aliases:["mips"],import:()=>h(()=>import("./mipsasm-BC5c_5Pe.js"),[],import.meta.url)},{id:"mojo",name:"Mojo",import:()=>h(()=>import("./mojo-Tz6hzZYG.js"),[],import.meta.url)},{id:"move",name:"Move",import:()=>h(()=>import("./move-DB_GagMm.js"),[],import.meta.url)},{id:"narrat",name:"Narrat Language",aliases:["nar"],import:()=>h(()=>import("./narrat-DLbgOhZU.js"),[],import.meta.url)},{id:"nextflow",name:"Nextflow",aliases:["nf"],import:()=>h(()=>import("./nextflow-B0XVJmRM.js"),[],import.meta.url)},{id:"nginx",name:"Nginx",import:()=>h(()=>import("./nginx-D_VnBJ67.js"),__vite__mapDeps([67,37,24]),import.meta.url)},{id:"nim",name:"Nim",import:()=>h(()=>import("./nim-ZlGxZxc3.js"),__vite__mapDeps([68,24,1,2,3,7,8,23,42]),import.meta.url)},{id:"nix",name:"Nix",import:()=>h(()=>import("./nix-shcSOmrb.js"),[],import.meta.url)},{id:"nushell",name:"nushell",aliases:["nu"],import:()=>h(()=>import("./nushell-D4Tzg5kh.js"),[],import.meta.url)},{id:"objective-c",name:"Objective-C",aliases:["objc"],import:()=>h(()=>import("./objective-c-Deuh7S70.js"),[],import.meta.url)},{id:"objective-cpp",name:"Objective-C++",import:()=>h(()=>import("./objective-cpp-BUEGK8hf.js"),[],import.meta.url)},{id:"ocaml",name:"OCaml",import:()=>h(()=>import("./ocaml-BNioltXt.js"),[],import.meta.url)},{id:"pascal",name:"Pascal",import:()=>h(()=>import("./pascal-JqZropPD.js"),[],import.meta.url)},{id:"perl",name:"Perl",import:()=>h(()=>import("./perl-CHQXSrWU.js"),__vite__mapDeps([69,1,2,3,7,8,14]),import.meta.url)},{id:"php",name:"PHP",import:()=>h(()=>import("./php-B5ebYQev.js"),__vite__mapDeps([70,1,2,3,7,8,14,9]),import.meta.url)},{id:"plsql",name:"PL/SQL",import:()=>h(()=>import("./plsql-LKU2TuZ1.js"),[],import.meta.url)},{id:"po",name:"Gettext PO",aliases:["pot","potx"],import:()=>h(()=>import("./po-BFLt1xDp.js"),[],import.meta.url)},{id:"polar",name:"Polar",import:()=>h(()=>import("./polar-DKykz6zU.js"),[],import.meta.url)},{id:"postcss",name:"PostCSS",import:()=>h(()=>import("./postcss-B3ZDOciz.js"),[],import.meta.url)},{id:"powerquery",name:"PowerQuery",import:()=>h(()=>import("./powerquery-CSHBycmS.js"),[],import.meta.url)},{id:"powershell",name:"PowerShell",aliases:["ps","ps1"],import:()=>h(()=>import("./powershell-BIEUsx6d.js"),[],import.meta.url)},{id:"prisma",name:"Prisma",import:()=>h(()=>import("./prisma-B48N-Iqd.js"),[],import.meta.url)},{id:"prolog",name:"Prolog",import:()=>h(()=>import("./prolog-BY-TUvya.js"),[],import.meta.url)},{id:"proto",name:"Protocol Buffer 3",aliases:["protobuf"],import:()=>h(()=>import("./proto-zocC4JxJ.js"),[],import.meta.url)},{id:"pug",name:"Pug",aliases:["jade"],import:()=>h(()=>import("./pug-CM9l7STV.js"),__vite__mapDeps([71,2,3,1]),import.meta.url)},{id:"puppet",name:"Puppet",import:()=>h(()=>import("./puppet-Cza_XSSt.js"),[],import.meta.url)},{id:"purescript",name:"PureScript",import:()=>h(()=>import("./purescript-Bg-kzb6g.js"),[],import.meta.url)},{id:"python",name:"Python",aliases:["py"],import:()=>h(()=>import("./python-DhUJRlN_.js"),[],import.meta.url)},{id:"qml",name:"QML",import:()=>h(()=>import("./qml-D8XfuvdV.js"),__vite__mapDeps([72,2]),import.meta.url)},{id:"qmldir",name:"QML Directory",import:()=>h(()=>import("./qmldir-C8lEn-DE.js"),[],import.meta.url)},{id:"qss",name:"Qt Style Sheets",import:()=>h(()=>import("./qss-DhMKtDLN.js"),[],import.meta.url)},{id:"r",name:"R",import:()=>h(()=>import("./r-CwjWoCRV.js"),[],import.meta.url)},{id:"racket",name:"Racket",import:()=>h(()=>import("./racket-CzouJOBO.js"),[],import.meta.url)},{id:"raku",name:"Raku",aliases:["perl6"],import:()=>h(()=>import("./raku-B1bQXN8T.js"),[],import.meta.url)},{id:"razor",name:"ASP.NET Razor",import:()=>h(()=>import("./razor-CNLDkMZG.js"),__vite__mapDeps([73,1,2,3,74]),import.meta.url)},{id:"reg",name:"Windows Registry Script",import:()=>h(()=>import("./reg-5LuOXUq_.js"),[],import.meta.url)},{id:"regexp",name:"RegExp",aliases:["regex"],import:()=>h(()=>import("./regexp-DWJ3fJO_.js"),[],import.meta.url)},{id:"rel",name:"Rel",import:()=>h(()=>import("./rel-DJlmqQ1C.js"),[],import.meta.url)},{id:"riscv",name:"RISC-V",import:()=>h(()=>import("./riscv-QhoSD0DR.js"),[],import.meta.url)},{id:"rst",name:"reStructuredText",import:()=>h(()=>import("./rst-4NLicBqY.js"),__vite__mapDeps([75,28,1,2,3,21,22,23,24,14,18,26,38,76,32,33,7,8,34,11,35,36,37]),import.meta.url)},{id:"ruby",name:"Ruby",aliases:["rb"],import:()=>h(()=>import("./ruby-DeZ3UC14.js"),__vite__mapDeps([32,1,2,3,33,7,8,14,34,11,35,36,21,22,23,24,26,37,38]),import.meta.url)},{id:"rust",name:"Rust",aliases:["rs"],import:()=>h(()=>import("./rust-Be6lgOlo.js"),[],import.meta.url)},{id:"sas",name:"SAS",import:()=>h(()=>import("./sas-BmTFh92c.js"),__vite__mapDeps([77,14]),import.meta.url)},{id:"sass",name:"Sass",import:()=>h(()=>import("./sass-BJ4Li9vH.js"),[],import.meta.url)},{id:"scala",name:"Scala",import:()=>h(()=>import("./scala-DQVVAn-B.js"),[],import.meta.url)},{id:"scheme",name:"Scheme",import:()=>h(()=>import("./scheme-BJGe-b2p.js"),[],import.meta.url)},{id:"scss",name:"SCSS",import:()=>h(()=>import("./scss-C31hgJw-.js"),__vite__mapDeps([5,3]),import.meta.url)},{id:"sdbl",name:"1C (Query)",aliases:["1c-query"],import:()=>h(()=>import("./sdbl-BLhTXw86.js"),[],import.meta.url)},{id:"shaderlab",name:"ShaderLab",aliases:["shader"],import:()=>h(()=>import("./shaderlab-B7qAK45m.js"),__vite__mapDeps([78,79]),import.meta.url)},{id:"shellscript",name:"Shell",aliases:["bash","sh","shell","zsh"],import:()=>h(()=>import("./shellscript-atvbtKCR.js"),[],import.meta.url)},{id:"shellsession",name:"Shell Session",aliases:["console"],import:()=>h(()=>import("./shellsession-C_rIy8kc.js"),__vite__mapDeps([80,26]),import.meta.url)},{id:"smalltalk",name:"Smalltalk",import:()=>h(()=>import("./smalltalk-DkLiglaE.js"),[],import.meta.url)},{id:"solidity",name:"Solidity",import:()=>h(()=>import("./solidity-C1w2a3ep.js"),[],import.meta.url)},{id:"soy",name:"Closure Templates",aliases:["closure-templates"],import:()=>h(()=>import("./soy-C-lX7w71.js"),__vite__mapDeps([81,1,2,3]),import.meta.url)},{id:"sparql",name:"SPARQL",import:()=>h(()=>import("./sparql-bYkjHRlG.js"),__vite__mapDeps([82,83]),import.meta.url)},{id:"splunk",name:"Splunk Query Language",aliases:["spl"],import:()=>h(()=>import("./splunk-Cf8iN4DR.js"),[],import.meta.url)},{id:"sql",name:"SQL",import:()=>h(()=>import("./sql-COK4E0Yg.js"),[],import.meta.url)},{id:"ssh-config",name:"SSH Config",import:()=>h(()=>import("./ssh-config-BknIz3MU.js"),[],import.meta.url)},{id:"stata",name:"Stata",import:()=>h(()=>import("./stata-DorPZHa4.js"),__vite__mapDeps([84,14]),import.meta.url)},{id:"stylus",name:"Stylus",aliases:["styl"],import:()=>h(()=>import("./stylus-BeQkCIfX.js"),[],import.meta.url)},{id:"svelte",name:"Svelte",import:()=>h(()=>import("./svelte-MSaWC3Je.js"),__vite__mapDeps([85,2,11,3,12]),import.meta.url)},{id:"swift",name:"Swift",import:()=>h(()=>import("./swift-BSxZ-RaX.js"),[],import.meta.url)},{id:"system-verilog",name:"SystemVerilog",import:()=>h(()=>import("./system-verilog-C7L56vO4.js"),[],import.meta.url)},{id:"systemd",name:"Systemd Units",import:()=>h(()=>import("./systemd-CUnW07Te.js"),[],import.meta.url)},{id:"talonscript",name:"TalonScript",aliases:["talon"],import:()=>h(()=>import("./talonscript-C1XDQQGZ.js"),[],import.meta.url)},{id:"tasl",name:"Tasl",import:()=>h(()=>import("./tasl-CQjiPCtT.js"),[],import.meta.url)},{id:"tcl",name:"Tcl",import:()=>h(()=>import("./tcl-DQ1-QYvQ.js"),[],import.meta.url)},{id:"templ",name:"Templ",import:()=>h(()=>import("./templ-dwX3ZSMB.js"),__vite__mapDeps([86,87,2,3]),import.meta.url)},{id:"terraform",name:"Terraform",aliases:["tf","tfvars"],import:()=>h(()=>import("./terraform-BbSNqyBO.js"),[],import.meta.url)},{id:"tex",name:"TeX",import:()=>h(()=>import("./tex-rYs2v40G.js"),__vite__mapDeps([62,60]),import.meta.url)},{id:"toml",name:"TOML",import:()=>h(()=>import("./toml-CB2ApiWb.js"),[],import.meta.url)},{id:"ts-tags",name:"TypeScript with Tags",aliases:["lit"],import:()=>h(()=>import("./ts-tags-CipyTH0X.js"),__vite__mapDeps([88,11,3,2,23,24,1,14,7,8]),import.meta.url)},{id:"tsv",name:"TSV",import:()=>h(()=>import("./tsv-B_m7g4N7.js"),[],import.meta.url)},{id:"tsx",name:"TSX",import:()=>h(()=>import("./tsx-B6W0miNI.js"),[],import.meta.url)},{id:"turtle",name:"Turtle",import:()=>h(()=>import("./turtle-BMR_PYu6.js"),[],import.meta.url)},{id:"twig",name:"Twig",import:()=>h(()=>import("./twig-NC5TFiHP.js"),__vite__mapDeps([89,3,2,5,70,1,7,8,14,9,18,32,33,34,11,35,36,21,22,23,24,26,37,38]),import.meta.url)},{id:"typescript",name:"TypeScript",aliases:["ts"],import:()=>h(()=>import("./typescript-Dj6nwHGl.js"),[],import.meta.url)},{id:"typespec",name:"TypeSpec",aliases:["tsp"],import:()=>h(()=>import("./typespec-BpWG_bgh.js"),[],import.meta.url)},{id:"typst",name:"Typst",aliases:["typ"],import:()=>h(()=>import("./typst-BVUVsWT6.js"),[],import.meta.url)},{id:"v",name:"V",import:()=>h(()=>import("./v-CAQ2eGtk.js"),[],import.meta.url)},{id:"vala",name:"Vala",import:()=>h(()=>import("./vala-BFOHcciG.js"),[],import.meta.url)},{id:"vb",name:"Visual Basic",aliases:["cmd"],import:()=>h(()=>import("./vb-CdO5JTpU.js"),[],import.meta.url)},{id:"verilog",name:"Verilog",import:()=>h(()=>import("./verilog-CJaU5se_.js"),[],import.meta.url)},{id:"vhdl",name:"VHDL",import:()=>h(()=>import("./vhdl-DYoNaHQp.js"),[],import.meta.url)},{id:"viml",name:"Vim Script",aliases:["vim","vimscript"],import:()=>h(()=>import("./viml-m4uW47V2.js"),[],import.meta.url)},{id:"vue",name:"Vue",import:()=>h(()=>import("./vue-BuYVFjOK.js"),__vite__mapDeps([90,1,2,3,11,9,28]),import.meta.url)},{id:"vue-html",name:"Vue HTML",import:()=>h(()=>import("./vue-html-xdeiXROB.js"),__vite__mapDeps([91,90,1,2,3,11,9,28]),import.meta.url)},{id:"vyper",name:"Vyper",aliases:["vy"],import:()=>h(()=>import("./vyper-nyqBNV6O.js"),[],import.meta.url)},{id:"wasm",name:"WebAssembly",import:()=>h(()=>import("./wasm-C6j12Q_x.js"),[],import.meta.url)},{id:"wenyan",name:"Wenyan",aliases:["文言"],import:()=>h(()=>import("./wenyan-7A4Fjokl.js"),[],import.meta.url)},{id:"wgsl",name:"WGSL",import:()=>h(()=>import("./wgsl-CB0Krxn9.js"),[],import.meta.url)},{id:"wikitext",name:"Wikitext",aliases:["mediawiki","wiki"],import:()=>h(()=>import("./wikitext-DCE3LsBG.js"),[],import.meta.url)},{id:"wolfram",name:"Wolfram",aliases:["wl"],import:()=>h(()=>import("./wolfram-C3FkfJm5.js"),[],import.meta.url)},{id:"xml",name:"XML",import:()=>h(()=>import("./xml-e3z08dGr.js"),__vite__mapDeps([7,8]),import.meta.url)},{id:"xsl",name:"XSL",import:()=>h(()=>import("./xsl-Dd0NUgwM.js"),__vite__mapDeps([92,7,8]),import.meta.url)},{id:"yaml",name:"YAML",aliases:["yml"],import:()=>h(()=>import("./yaml-CVw76BM1.js"),[],import.meta.url)},{id:"zenscript",name:"ZenScript",import:()=>h(()=>import("./zenscript-HnGAYVZD.js"),[],import.meta.url)},{id:"zig",name:"Zig",import:()=>h(()=>import("./zig-BVz_zdnA.js"),[],import.meta.url)}],R_=Object.fromEntries(Yf.map(e=>[e.id,e.import])),P_=Object.fromEntries(Yf.flatMap(e=>{var t;return((t=e.aliases)==null?void 0:t.map(n=>[n,e.import]))||[]})),T_={...R_,...P_},L_=[{id:"andromeeda",displayName:"Andromeeda",type:"dark",import:()=>h(()=>import("./andromeeda-C3khCPGq.js"),[],import.meta.url)},{id:"aurora-x",displayName:"Aurora X",type:"dark",import:()=>h(()=>import("./aurora-x-D-2ljcwZ.js"),[],import.meta.url)},{id:"ayu-dark",displayName:"Ayu Dark",type:"dark",import:()=>h(()=>import("./ayu-dark-Cv9koXgw.js"),[],import.meta.url)},{id:"catppuccin-frappe",displayName:"Catppuccin Frappé",type:"dark",import:()=>h(()=>import("./catppuccin-frappe-CD_QflpE.js"),[],import.meta.url)},{id:"catppuccin-latte",displayName:"Catppuccin Latte",type:"light",import:()=>h(()=>import("./catppuccin-latte-DRW-0cLl.js"),[],import.meta.url)},{id:"catppuccin-macchiato",displayName:"Catppuccin Macchiato",type:"dark",import:()=>h(()=>import("./catppuccin-macchiato-C-_shW-Y.js"),[],import.meta.url)},{id:"catppuccin-mocha",displayName:"Catppuccin Mocha",type:"dark",import:()=>h(()=>import("./catppuccin-mocha-LGGdnPYs.js"),[],import.meta.url)},{id:"dark-plus",displayName:"Dark Plus",type:"dark",import:()=>h(()=>import("./dark-plus-C3mMm8J8.js"),[],import.meta.url)},{id:"dracula",displayName:"Dracula Theme",type:"dark",import:()=>h(()=>import("./dracula-BzJJZx-M.js"),[],import.meta.url)},{id:"dracula-soft",displayName:"Dracula Theme Soft",type:"dark",import:()=>h(()=>import("./dracula-soft-BXkSAIEj.js"),[],import.meta.url)},{id:"everforest-dark",displayName:"Everforest Dark",type:"dark",import:()=>h(()=>import("./everforest-dark-BgDCqdQA.js"),[],import.meta.url)},{id:"everforest-light",displayName:"Everforest Light",type:"light",import:()=>h(()=>import("./everforest-light-C8M2exoo.js"),[],import.meta.url)},{id:"github-dark",displayName:"GitHub Dark",type:"dark",import:()=>h(()=>import("./github-dark-DHJKELXO.js"),[],import.meta.url)},{id:"github-dark-default",displayName:"GitHub Dark Default",type:"dark",import:()=>h(()=>import("./github-dark-default-Cuk6v7N8.js"),[],import.meta.url)},{id:"github-dark-dimmed",displayName:"GitHub Dark Dimmed",type:"dark",import:()=>h(()=>import("./github-dark-dimmed-DH5Ifo-i.js"),[],import.meta.url)},{id:"github-dark-high-contrast",displayName:"GitHub Dark High Contrast",type:"dark",import:()=>h(()=>import("./github-dark-high-contrast-E3gJ1_iC.js"),[],import.meta.url)},{id:"github-light",displayName:"GitHub Light",type:"light",import:()=>h(()=>import("./github-light-DAi9KRSo.js"),[],import.meta.url)},{id:"github-light-default",displayName:"GitHub Light Default",type:"light",import:()=>h(()=>import("./github-light-default-D7oLnXFd.js"),[],import.meta.url)},{id:"github-light-high-contrast",displayName:"GitHub Light High Contrast",type:"light",import:()=>h(()=>import("./github-light-high-contrast-BfjtVDDH.js"),[],import.meta.url)},{id:"houston",displayName:"Houston",type:"dark",import:()=>h(()=>import("./houston-DnULxvSX.js"),[],import.meta.url)},{id:"kanagawa-dragon",displayName:"Kanagawa Dragon",type:"dark",import:()=>h(()=>import("./kanagawa-dragon-CkXjmgJE.js"),[],import.meta.url)},{id:"kanagawa-lotus",displayName:"Kanagawa Lotus",type:"light",import:()=>h(()=>import("./kanagawa-lotus-CfQXZHmo.js"),[],import.meta.url)},{id:"kanagawa-wave",displayName:"Kanagawa Wave",type:"dark",import:()=>h(()=>import("./kanagawa-wave-DWedfzmr.js"),[],import.meta.url)},{id:"laserwave",displayName:"LaserWave",type:"dark",import:()=>h(()=>import("./laserwave-DUszq2jm.js"),[],import.meta.url)},{id:"light-plus",displayName:"Light Plus",type:"light",import:()=>h(()=>import("./light-plus-B7mTdjB0.js"),[],import.meta.url)},{id:"material-theme",displayName:"Material Theme",type:"dark",import:()=>h(()=>import("./material-theme-D5KoaKCx.js"),[],import.meta.url)},{id:"material-theme-darker",displayName:"Material Theme Darker",type:"dark",import:()=>h(()=>import("./material-theme-darker-BfHTSMKl.js"),[],import.meta.url)},{id:"material-theme-lighter",displayName:"Material Theme Lighter",type:"light",import:()=>h(()=>import("./material-theme-lighter-B0m2ddpp.js"),[],import.meta.url)},{id:"material-theme-ocean",displayName:"Material Theme Ocean",type:"dark",import:()=>h(()=>import("./material-theme-ocean-CyktbL80.js"),[],import.meta.url)},{id:"material-theme-palenight",displayName:"Material Theme Palenight",type:"dark",import:()=>h(()=>import("./material-theme-palenight-Csfq5Kiy.js"),[],import.meta.url)},{id:"min-dark",displayName:"Min Dark",type:"dark",import:()=>h(()=>import("./min-dark-CafNBF8u.js"),[],import.meta.url)},{id:"min-light",displayName:"Min Light",type:"light",import:()=>h(()=>import("./min-light-CTRr51gU.js"),[],import.meta.url)},{id:"monokai",displayName:"Monokai",type:"dark",import:()=>h(()=>import("./monokai-D4h5O-jR.js"),[],import.meta.url)},{id:"night-owl",displayName:"Night Owl",type:"dark",import:()=>h(()=>import("./night-owl-C39BiMTA.js"),[],import.meta.url)},{id:"nord",displayName:"Nord",type:"dark",import:()=>h(()=>import("./nord-Ddv68eIx.js"),[],import.meta.url)},{id:"one-dark-pro",displayName:"One Dark Pro",type:"dark",import:()=>h(()=>import("./one-dark-pro-GBQ2dnAY.js"),[],import.meta.url)},{id:"one-light",displayName:"One Light",type:"light",import:()=>h(()=>import("./one-light-PoHY5YXO.js"),[],import.meta.url)},{id:"plastic",displayName:"Plastic",type:"dark",import:()=>h(()=>import("./plastic-3e1v2bzS.js"),[],import.meta.url)},{id:"poimandres",displayName:"Poimandres",type:"dark",import:()=>h(()=>import("./poimandres-CS3Unz2-.js"),[],import.meta.url)},{id:"red",displayName:"Red",type:"dark",import:()=>h(()=>import("./red-bN70gL4F.js"),[],import.meta.url)},{id:"rose-pine",displayName:"Rosé Pine",type:"dark",import:()=>h(()=>import("./rose-pine-CmCqftbK.js"),[],import.meta.url)},{id:"rose-pine-dawn",displayName:"Rosé Pine Dawn",type:"light",import:()=>h(()=>import("./rose-pine-dawn-Ds-gbosJ.js"),[],import.meta.url)},{id:"rose-pine-moon",displayName:"Rosé Pine Moon",type:"dark",import:()=>h(()=>import("./rose-pine-moon-CjDtw9vr.js"),[],import.meta.url)},{id:"slack-dark",displayName:"Slack Dark",type:"dark",import:()=>h(()=>import("./slack-dark-BthQWCQV.js"),[],import.meta.url)},{id:"slack-ochin",displayName:"Slack Ochin",type:"light",import:()=>h(()=>import("./slack-ochin-DqwNpetd.js"),[],import.meta.url)},{id:"snazzy-light",displayName:"Snazzy Light",type:"light",import:()=>h(()=>import("./snazzy-light-Bw305WKR.js"),[],import.meta.url)},{id:"solarized-dark",displayName:"Solarized Dark",type:"dark",import:()=>h(()=>import("./solarized-dark-DXbdFlpD.js"),[],import.meta.url)},{id:"solarized-light",displayName:"Solarized Light",type:"light",import:()=>h(()=>import("./solarized-light-L9t79GZl.js"),[],import.meta.url)},{id:"synthwave-84",displayName:"Synthwave '84",type:"dark",import:()=>h(()=>import("./synthwave-84-CbfX1IO0.js"),[],import.meta.url)},{id:"tokyo-night",displayName:"Tokyo Night",type:"dark",import:()=>h(()=>import("./tokyo-night-DBQeEorK.js"),[],import.meta.url)},{id:"vesper",displayName:"Vesper",type:"dark",import:()=>h(()=>import("./vesper-BEBZ7ncR.js"),[],import.meta.url)},{id:"vitesse-black",displayName:"Vitesse Black",type:"dark",import:()=>h(()=>import("./vitesse-black-Bkuqu6BP.js"),[],import.meta.url)},{id:"vitesse-dark",displayName:"Vitesse Dark",type:"dark",import:()=>h(()=>import("./vitesse-dark-D0r3Knsf.js"),[],import.meta.url)},{id:"vitesse-light",displayName:"Vitesse Light",type:"light",import:()=>h(()=>import("./vitesse-light-CVO1_9PV.js"),[],import.meta.url)}],O_=Object.fromEntries(L_.map(e=>[e.id,e.import]));let ht=class extends Error{constructor(t){super(t),this.name="ShikiError"}},Ra=class extends Error{constructor(t){super(t),this.name="ShikiError"}};function A_(){return 2147483648}function I_(){return typeof performance<"u"?performance.now():Date.now()}const N_=(e,t)=>e+(t-e%t)%t;async function D_(e){let t,n;const r={};function o(g){n=g,r.HEAPU8=new Uint8Array(g),r.HEAPU32=new Uint32Array(g)}function i(g,_,E){r.HEAPU8.copyWithin(g,_,_+E)}function l(g){try{return t.grow(g-n.byteLength+65535>>>16),o(t.buffer),1}catch{}}function s(g){const _=r.HEAPU8.length;g=g>>>0;const E=A_();if(g>E)return!1;for(let S=1;S<=4;S*=2){let p=_*(1+.2/S);p=Math.min(p,g+100663296);const c=Math.min(E,N_(Math.max(g,p),65536));if(l(c))return!0}return!1}const a=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function u(g,_,E=1024){const S=_+E;let p=_;for(;g[p]&&!(p>=S);)++p;if(p-_>16&&g.buffer&&a)return a.decode(g.subarray(_,p));let c="";for(;_<p;){let y=g[_++];if(!(y&128)){c+=String.fromCharCode(y);continue}const w=g[_++]&63;if((y&224)===192){c+=String.fromCharCode((y&31)<<6|w);continue}const x=g[_++]&63;if((y&240)===224?y=(y&15)<<12|w<<6|x:y=(y&7)<<18|w<<12|x<<6|g[_++]&63,y<65536)c+=String.fromCharCode(y);else{const T=y-65536;c+=String.fromCharCode(55296|T>>10,56320|T&1023)}}return c}function d(g,_){return g?u(r.HEAPU8,g,_):""}const f={emscripten_get_now:I_,emscripten_memcpy_big:i,emscripten_resize_heap:s,fd_write:()=>0};async function m(){const _=await e({env:f,wasi_snapshot_preview1:f});t=_.memory,o(t.buffer),Object.assign(r,_),r.UTF8ToString=d}return await m(),r}var j_=Object.defineProperty,M_=(e,t,n)=>t in e?j_(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,ne=(e,t,n)=>(M_(e,typeof t!="symbol"?t+"":t,n),n);let le=null;function b_(e){throw new Ra(e.UTF8ToString(e.getLastOnigError()))}class Ui{constructor(t){ne(this,"utf16Length"),ne(this,"utf8Length"),ne(this,"utf16Value"),ne(this,"utf8Value"),ne(this,"utf16OffsetToUtf8"),ne(this,"utf8OffsetToUtf16");const n=t.length,r=Ui._utf8ByteLength(t),o=r!==n,i=o?new Uint32Array(n+1):null;o&&(i[n]=r);const l=o?new Uint32Array(r+1):null;o&&(l[r]=n);const s=new Uint8Array(r);let a=0;for(let u=0;u<n;u++){const d=t.charCodeAt(u);let f=d,m=!1;if(d>=55296&&d<=56319&&u+1<n){const g=t.charCodeAt(u+1);g>=56320&&g<=57343&&(f=(d-55296<<10)+65536|g-56320,m=!0)}o&&(i[u]=a,m&&(i[u+1]=a),f<=127?l[a+0]=u:f<=2047?(l[a+0]=u,l[a+1]=u):f<=65535?(l[a+0]=u,l[a+1]=u,l[a+2]=u):(l[a+0]=u,l[a+1]=u,l[a+2]=u,l[a+3]=u)),f<=127?s[a++]=f:f<=2047?(s[a++]=192|(f&1984)>>>6,s[a++]=128|(f&63)>>>0):f<=65535?(s[a++]=224|(f&61440)>>>12,s[a++]=128|(f&4032)>>>6,s[a++]=128|(f&63)>>>0):(s[a++]=240|(f&1835008)>>>18,s[a++]=128|(f&258048)>>>12,s[a++]=128|(f&4032)>>>6,s[a++]=128|(f&63)>>>0),m&&u++}this.utf16Length=n,this.utf8Length=r,this.utf16Value=t,this.utf8Value=s,this.utf16OffsetToUtf8=i,this.utf8OffsetToUtf16=l}static _utf8ByteLength(t){let n=0;for(let r=0,o=t.length;r<o;r++){const i=t.charCodeAt(r);let l=i,s=!1;if(i>=55296&&i<=56319&&r+1<o){const a=t.charCodeAt(r+1);a>=56320&&a<=57343&&(l=(i-55296<<10)+65536|a-56320,s=!0)}l<=127?n+=1:l<=2047?n+=2:l<=65535?n+=3:n+=4,s&&r++}return n}createString(t){const n=t.omalloc(this.utf8Length);return t.HEAPU8.set(this.utf8Value,n),n}}const tt=class{constructor(e){if(ne(this,"id",++tt.LAST_ID),ne(this,"_onigBinding"),ne(this,"content"),ne(this,"utf16Length"),ne(this,"utf8Length"),ne(this,"utf16OffsetToUtf8"),ne(this,"utf8OffsetToUtf16"),ne(this,"ptr"),!le)throw new Ra("Must invoke loadWasm first.");this._onigBinding=le,this.content=e;const t=new Ui(e);this.utf16Length=t.utf16Length,this.utf8Length=t.utf8Length,this.utf16OffsetToUtf8=t.utf16OffsetToUtf8,this.utf8OffsetToUtf16=t.utf8OffsetToUtf16,this.utf8Length<1e4&&!tt._sharedPtrInUse?(tt._sharedPtr||(tt._sharedPtr=le.omalloc(1e4)),tt._sharedPtrInUse=!0,le.HEAPU8.set(t.utf8Value,tt._sharedPtr),this.ptr=tt._sharedPtr):this.ptr=t.createString(le)}convertUtf8OffsetToUtf16(e){return this.utf8OffsetToUtf16?e<0?0:e>this.utf8Length?this.utf16Length:this.utf8OffsetToUtf16[e]:e}convertUtf16OffsetToUtf8(e){return this.utf16OffsetToUtf8?e<0?0:e>this.utf16Length?this.utf8Length:this.utf16OffsetToUtf8[e]:e}dispose(){this.ptr===tt._sharedPtr?tt._sharedPtrInUse=!1:this._onigBinding.ofree(this.ptr)}};let Jr=tt;ne(Jr,"LAST_ID",0);ne(Jr,"_sharedPtr",0);ne(Jr,"_sharedPtrInUse",!1);class V_{constructor(t){if(ne(this,"_onigBinding"),ne(this,"_ptr"),!le)throw new Ra("Must invoke loadWasm first.");const n=[],r=[];for(let s=0,a=t.length;s<a;s++){const u=new Ui(t[s]);n[s]=u.createString(le),r[s]=u.utf8Length}const o=le.omalloc(4*t.length);le.HEAPU32.set(n,o/4);const i=le.omalloc(4*t.length);le.HEAPU32.set(r,i/4);const l=le.createOnigScanner(o,i,t.length);for(let s=0,a=t.length;s<a;s++)le.ofree(n[s]);le.ofree(i),le.ofree(o),l===0&&b_(le),this._onigBinding=le,this._ptr=l}dispose(){this._onigBinding.freeOnigScanner(this._ptr)}findNextMatchSync(t,n,r){let o=0;if(typeof r=="number"&&(o=r),typeof t=="string"){t=new Jr(t);const i=this._findNextMatchSync(t,n,!1,o);return t.dispose(),i}return this._findNextMatchSync(t,n,!1,o)}_findNextMatchSync(t,n,r,o){const i=this._onigBinding,l=i.findNextOnigScannerMatch(this._ptr,t.id,t.ptr,t.utf8Length,t.convertUtf16OffsetToUtf8(n),o);if(l===0)return null;const s=i.HEAPU32;let a=l/4;const u=s[a++],d=s[a++],f=[];for(let m=0;m<d;m++){const g=t.convertUtf8OffsetToUtf16(s[a++]),_=t.convertUtf8OffsetToUtf16(s[a++]);f[m]={start:g,end:_,length:_-g}}return{index:u,captureIndices:f}}}function z_(e){return typeof e.instantiator=="function"}function U_(e){return typeof e.default=="function"}function $_(e){return typeof e.data<"u"}function B_(e){return typeof Response<"u"&&e instanceof Response}function F_(e){var t;return typeof ArrayBuffer<"u"&&(e instanceof ArrayBuffer||ArrayBuffer.isView(e))||typeof Buffer<"u"&&((t=Buffer.isBuffer)==null?void 0:t.call(Buffer,e))||typeof SharedArrayBuffer<"u"&&e instanceof SharedArrayBuffer||typeof Uint32Array<"u"&&e instanceof Uint32Array}let wo;function H_(e){if(wo)return wo;async function t(){le=await D_(async n=>{let r=e;return r=await r,typeof r=="function"&&(r=await r(n)),typeof r=="function"&&(r=await r(n)),z_(r)?r=await r.instantiator(n):U_(r)?r=await r.default(n):($_(r)&&(r=r.data),B_(r)?typeof WebAssembly.instantiateStreaming=="function"?r=await G_(r)(n):r=await W_(r)(n):F_(r)?r=await vl(r)(n):r instanceof WebAssembly.Module?r=await vl(r)(n):"default"in r&&r.default instanceof WebAssembly.Module&&(r=await vl(r.default)(n))),"instance"in r&&(r=r.instance),"exports"in r&&(r=r.exports),r})}return wo=t(),wo}function vl(e){return t=>WebAssembly.instantiate(e,t)}function G_(e){return t=>WebAssembly.instantiateStreaming(e,t)}function W_(e){return async t=>{const n=await e.arrayBuffer();return WebAssembly.instantiate(n,t)}}let K_;function Q_(){return K_}async function Jf(e){return e&&await H_(e),{createScanner(t){return new V_(t.map(n=>typeof n=="string"?n:n.source))},createString(t){return new Jr(t)}}}function q_(e){return Pa(e)}function Pa(e){return Array.isArray(e)?X_(e):e instanceof RegExp?e:typeof e=="object"?Y_(e):e}function X_(e){let t=[];for(let n=0,r=e.length;n<r;n++)t[n]=Pa(e[n]);return t}function Y_(e){let t={};for(let n in e)t[n]=Pa(e[n]);return t}function Zf(e,...t){return t.forEach(n=>{for(let r in n)e[r]=n[r]}),e}function ep(e){const t=~e.lastIndexOf("/")||~e.lastIndexOf("\\");return t===0?e:~t===e.length-1?ep(e.substring(0,e.length-1)):e.substr(~t+1)}var El=/\$(\d+)|\${(\d+):\/(downcase|upcase)}/g,ko=class{static hasCaptures(e){return e===null?!1:(El.lastIndex=0,El.test(e))}static replaceCaptures(e,t,n){return e.replace(El,(r,o,i,l)=>{let s=n[parseInt(o||i,10)];if(s){let a=t.substring(s.start,s.end);for(;a[0]===".";)a=a.substring(1);switch(l){case"downcase":return a.toLowerCase();case"upcase":return a.toUpperCase();default:return a}}else return r})}};function tp(e,t){return e<t?-1:e>t?1:0}function np(e,t){if(e===null&&t===null)return 0;if(!e)return-1;if(!t)return 1;let n=e.length,r=t.length;if(n===r){for(let o=0;o<n;o++){let i=tp(e[o],t[o]);if(i!==0)return i}return 0}return n-r}function Xu(e){return!!(/^#[0-9a-f]{6}$/i.test(e)||/^#[0-9a-f]{8}$/i.test(e)||/^#[0-9a-f]{3}$/i.test(e)||/^#[0-9a-f]{4}$/i.test(e))}function rp(e){return e.replace(/[\-\\\{\}\*\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&")}var op=class{constructor(e){k(this,"cache",new Map);this.fn=e}get(e){if(this.cache.has(e))return this.cache.get(e);const t=this.fn(e);return this.cache.set(e,t),t}},fi=class{constructor(e,t,n){k(this,"_cachedMatchRoot",new op(e=>this._root.match(e)));this._colorMap=e,this._defaults=t,this._root=n}static createFromRawTheme(e,t){return this.createFromParsedTheme(ey(e),t)}static createFromParsedTheme(e,t){return ny(e,t)}getColorMap(){return this._colorMap.getColorMap()}getDefaults(){return this._defaults}match(e){if(e===null)return this._defaults;const t=e.scopeName,r=this._cachedMatchRoot.get(t).find(o=>J_(e.parent,o.parentScopes));return r?new ip(r.fontStyle,r.foreground,r.background):null}},Sl=class Uo{constructor(t,n){this.parent=t,this.scopeName=n}static push(t,n){for(const r of n)t=new Uo(t,r);return t}static from(...t){let n=null;for(let r=0;r<t.length;r++)n=new Uo(n,t[r]);return n}push(t){return new Uo(this,t)}getSegments(){let t=this;const n=[];for(;t;)n.push(t.scopeName),t=t.parent;return n.reverse(),n}toString(){return this.getSegments().join(" ")}extends(t){return this===t?!0:this.parent===null?!1:this.parent.extends(t)}getExtensionIfDefined(t){const n=[];let r=this;for(;r&&r!==t;)n.push(r.scopeName),r=r.parent;return r===t?n.reverse():void 0}};function J_(e,t){if(t.length===0)return!0;for(let n=0;n<t.length;n++){let r=t[n],o=!1;if(r===">"){if(n===t.length-1)return!1;r=t[++n],o=!0}for(;e&&!Z_(e.scopeName,r);){if(o)return!1;e=e.parent}if(!e)return!1;e=e.parent}return!0}function Z_(e,t){return t===e||e.startsWith(t)&&e[t.length]==="."}var ip=class{constructor(e,t,n){this.fontStyle=e,this.foregroundId=t,this.backgroundId=n}};function ey(e){if(!e)return[];if(!e.settings||!Array.isArray(e.settings))return[];let t=e.settings,n=[],r=0;for(let o=0,i=t.length;o<i;o++){let l=t[o];if(!l.settings)continue;let s;if(typeof l.scope=="string"){let f=l.scope;f=f.replace(/^[,]+/,""),f=f.replace(/[,]+$/,""),s=f.split(",")}else Array.isArray(l.scope)?s=l.scope:s=[""];let a=-1;if(typeof l.settings.fontStyle=="string"){a=0;let f=l.settings.fontStyle.split(" ");for(let m=0,g=f.length;m<g;m++)switch(f[m]){case"italic":a=a|1;break;case"bold":a=a|2;break;case"underline":a=a|4;break;case"strikethrough":a=a|8;break}}let u=null;typeof l.settings.foreground=="string"&&Xu(l.settings.foreground)&&(u=l.settings.foreground);let d=null;typeof l.settings.background=="string"&&Xu(l.settings.background)&&(d=l.settings.background);for(let f=0,m=s.length;f<m;f++){let _=s[f].trim().split(" "),E=_[_.length-1],S=null;_.length>1&&(S=_.slice(0,_.length-1),S.reverse()),n[r++]=new ty(E,S,o,a,u,d)}}return n}var ty=class{constructor(e,t,n,r,o,i){this.scope=e,this.parentScopes=t,this.index=n,this.fontStyle=r,this.foreground=o,this.background=i}},ft=(e=>(e[e.NotSet=-1]="NotSet",e[e.None=0]="None",e[e.Italic=1]="Italic",e[e.Bold=2]="Bold",e[e.Underline=4]="Underline",e[e.Strikethrough=8]="Strikethrough",e))(ft||{});function ny(e,t){e.sort((a,u)=>{let d=tp(a.scope,u.scope);return d!==0||(d=np(a.parentScopes,u.parentScopes),d!==0)?d:a.index-u.index});let n=0,r="#000000",o="#ffffff";for(;e.length>=1&&e[0].scope==="";){let a=e.shift();a.fontStyle!==-1&&(n=a.fontStyle),a.foreground!==null&&(r=a.foreground),a.background!==null&&(o=a.background)}let i=new ry(t),l=new ip(n,i.getId(r),i.getId(o)),s=new iy(new Ss(0,null,-1,0,0),[]);for(let a=0,u=e.length;a<u;a++){let d=e[a];s.insert(0,d.scope,d.parentScopes,d.fontStyle,i.getId(d.foreground),i.getId(d.background))}return new fi(i,l,s)}var ry=class{constructor(e){k(this,"_isFrozen");k(this,"_lastColorId");k(this,"_id2color");k(this,"_color2id");if(this._lastColorId=0,this._id2color=[],this._color2id=Object.create(null),Array.isArray(e)){this._isFrozen=!0;for(let t=0,n=e.length;t<n;t++)this._color2id[e[t]]=t,this._id2color[t]=e[t]}else this._isFrozen=!1}getId(e){if(e===null)return 0;e=e.toUpperCase();let t=this._color2id[e];if(t)return t;if(this._isFrozen)throw new Error(`Missing color in color map - ${e}`);return t=++this._lastColorId,this._color2id[e]=t,this._id2color[t]=e,t}getColorMap(){return this._id2color.slice(0)}},oy=Object.freeze([]),Ss=class lp{constructor(t,n,r,o,i){k(this,"scopeDepth");k(this,"parentScopes");k(this,"fontStyle");k(this,"foreground");k(this,"background");this.scopeDepth=t,this.parentScopes=n||oy,this.fontStyle=r,this.foreground=o,this.background=i}clone(){return new lp(this.scopeDepth,this.parentScopes,this.fontStyle,this.foreground,this.background)}static cloneArr(t){let n=[];for(let r=0,o=t.length;r<o;r++)n[r]=t[r].clone();return n}acceptOverwrite(t,n,r,o){this.scopeDepth>t?console.log("how did this happen?"):this.scopeDepth=t,n!==-1&&(this.fontStyle=n),r!==0&&(this.foreground=r),o!==0&&(this.background=o)}},iy=class ws{constructor(t,n=[],r={}){k(this,"_rulesWithParentScopes");this._mainRule=t,this._children=r,this._rulesWithParentScopes=n}static _cmpBySpecificity(t,n){if(t.scopeDepth!==n.scopeDepth)return n.scopeDepth-t.scopeDepth;let r=0,o=0;for(;t.parentScopes[r]===">"&&r++,n.parentScopes[o]===">"&&o++,!(r>=t.parentScopes.length||o>=n.parentScopes.length);){const i=n.parentScopes[o].length-t.parentScopes[r].length;if(i!==0)return i;r++,o++}return n.parentScopes.length-t.parentScopes.length}match(t){if(t!==""){let r=t.indexOf("."),o,i;if(r===-1?(o=t,i=""):(o=t.substring(0,r),i=t.substring(r+1)),this._children.hasOwnProperty(o))return this._children[o].match(i)}const n=this._rulesWithParentScopes.concat(this._mainRule);return n.sort(ws._cmpBySpecificity),n}insert(t,n,r,o,i,l){if(n===""){this._doInsertHere(t,r,o,i,l);return}let s=n.indexOf("."),a,u;s===-1?(a=n,u=""):(a=n.substring(0,s),u=n.substring(s+1));let d;this._children.hasOwnProperty(a)?d=this._children[a]:(d=new ws(this._mainRule.clone(),Ss.cloneArr(this._rulesWithParentScopes)),this._children[a]=d),d.insert(t+1,u,r,o,i,l)}_doInsertHere(t,n,r,o,i){if(n===null){this._mainRule.acceptOverwrite(t,r,o,i);return}for(let l=0,s=this._rulesWithParentScopes.length;l<s;l++){let a=this._rulesWithParentScopes[l];if(np(a.parentScopes,n)===0){a.acceptOverwrite(t,r,o,i);return}}r===-1&&(r=this._mainRule.fontStyle),o===0&&(o=this._mainRule.foreground),i===0&&(i=this._mainRule.background),this._rulesWithParentScopes.push(new Ss(t,n,r,o,i))}},Gn=class ze{static toBinaryStr(t){return t.toString(2).padStart(32,"0")}static print(t){const n=ze.getLanguageId(t),r=ze.getTokenType(t),o=ze.getFontStyle(t),i=ze.getForeground(t),l=ze.getBackground(t);console.log({languageId:n,tokenType:r,fontStyle:o,foreground:i,background:l})}static getLanguageId(t){return(t&255)>>>0}static getTokenType(t){return(t&768)>>>8}static containsBalancedBrackets(t){return(t&1024)!==0}static getFontStyle(t){return(t&30720)>>>11}static getForeground(t){return(t&16744448)>>>15}static getBackground(t){return(t&4278190080)>>>24}static set(t,n,r,o,i,l,s){let a=ze.getLanguageId(t),u=ze.getTokenType(t),d=ze.containsBalancedBrackets(t)?1:0,f=ze.getFontStyle(t),m=ze.getForeground(t),g=ze.getBackground(t);return n!==0&&(a=n),r!==8&&(u=r),o!==null&&(d=o?1:0),i!==-1&&(f=i),l!==0&&(m=l),s!==0&&(g=s),(a<<0|u<<8|d<<10|f<<11|m<<15|g<<24)>>>0}};function pi(e,t){const n=[],r=ly(e);let o=r.next();for(;o!==null;){let a=0;if(o.length===2&&o.charAt(1)===":"){switch(o.charAt(0)){case"R":a=1;break;case"L":a=-1;break;default:console.log(`Unknown priority ${o} in scope selector`)}o=r.next()}let u=l();if(n.push({matcher:u,priority:a}),o!==",")break;o=r.next()}return n;function i(){if(o==="-"){o=r.next();const a=i();return u=>!!a&&!a(u)}if(o==="("){o=r.next();const a=s();return o===")"&&(o=r.next()),a}if(Yu(o)){const a=[];do a.push(o),o=r.next();while(Yu(o));return u=>t(a,u)}return null}function l(){const a=[];let u=i();for(;u;)a.push(u),u=i();return d=>a.every(f=>f(d))}function s(){const a=[];let u=l();for(;u&&(a.push(u),o==="|"||o===",");){do o=r.next();while(o==="|"||o===",");u=l()}return d=>a.some(f=>f(d))}}function Yu(e){return!!e&&!!e.match(/[\w\.:]+/)}function ly(e){let t=/([LR]:|[\w\.:][\w\.:\-]*|[\,\|\-\(\)])/g,n=t.exec(e);return{next:()=>{if(!n)return null;const r=n[0];return n=t.exec(e),r}}}function sp(e){typeof e.dispose=="function"&&e.dispose()}var Ur=class{constructor(e){this.scopeName=e}toKey(){return this.scopeName}},sy=class{constructor(e,t){this.scopeName=e,this.ruleName=t}toKey(){return`${this.scopeName}#${this.ruleName}`}},ay=class{constructor(){k(this,"_references",[]);k(this,"_seenReferenceKeys",new Set);k(this,"visitedRule",new Set)}get references(){return this._references}add(e){const t=e.toKey();this._seenReferenceKeys.has(t)||(this._seenReferenceKeys.add(t),this._references.push(e))}},uy=class{constructor(e,t){k(this,"seenFullScopeRequests",new Set);k(this,"seenPartialScopeRequests",new Set);k(this,"Q");this.repo=e,this.initialScopeName=t,this.seenFullScopeRequests.add(this.initialScopeName),this.Q=[new Ur(this.initialScopeName)]}processQueue(){const e=this.Q;this.Q=[];const t=new ay;for(const n of e)cy(n,this.initialScopeName,this.repo,t);for(const n of t.references)if(n instanceof Ur){if(this.seenFullScopeRequests.has(n.scopeName))continue;this.seenFullScopeRequests.add(n.scopeName),this.Q.push(n)}else{if(this.seenFullScopeRequests.has(n.scopeName)||this.seenPartialScopeRequests.has(n.toKey()))continue;this.seenPartialScopeRequests.add(n.toKey()),this.Q.push(n)}}};function cy(e,t,n,r){const o=n.lookup(e.scopeName);if(!o){if(e.scopeName===t)throw new Error(`No grammar provided for <${t}>`);return}const i=n.lookup(t);e instanceof Ur?$o({baseGrammar:i,selfGrammar:o},r):ks(e.ruleName,{baseGrammar:i,selfGrammar:o,repository:o.repository},r);const l=n.injections(e.scopeName);if(l)for(const s of l)r.add(new Ur(s))}function ks(e,t,n){if(t.repository&&t.repository[e]){const r=t.repository[e];mi([r],t,n)}}function $o(e,t){e.selfGrammar.patterns&&Array.isArray(e.selfGrammar.patterns)&&mi(e.selfGrammar.patterns,{...e,repository:e.selfGrammar.repository},t),e.selfGrammar.injections&&mi(Object.values(e.selfGrammar.injections),{...e,repository:e.selfGrammar.repository},t)}function mi(e,t,n){for(const r of e){if(n.visitedRule.has(r))continue;n.visitedRule.add(r);const o=r.repository?Zf({},t.repository,r.repository):t.repository;Array.isArray(r.patterns)&&mi(r.patterns,{...t,repository:o},n);const i=r.include;if(!i)continue;const l=ap(i);switch(l.kind){case 0:$o({...t,selfGrammar:t.baseGrammar},n);break;case 1:$o(t,n);break;case 2:ks(l.ruleName,{...t,repository:o},n);break;case 3:case 4:const s=l.scopeName===t.selfGrammar.scopeName?t.selfGrammar:l.scopeName===t.baseGrammar.scopeName?t.baseGrammar:void 0;if(s){const a={baseGrammar:t.baseGrammar,selfGrammar:s,repository:o};l.kind===4?ks(l.ruleName,a,n):$o(a,n)}else l.kind===4?n.add(new sy(l.scopeName,l.ruleName)):n.add(new Ur(l.scopeName));break}}}var dy=class{constructor(){k(this,"kind",0)}},fy=class{constructor(){k(this,"kind",1)}},py=class{constructor(e){k(this,"kind",2);this.ruleName=e}},my=class{constructor(e){k(this,"kind",3);this.scopeName=e}},hy=class{constructor(e,t){k(this,"kind",4);this.scopeName=e,this.ruleName=t}};function ap(e){if(e==="$base")return new dy;if(e==="$self")return new fy;const t=e.indexOf("#");if(t===-1)return new my(e);if(t===0)return new py(e.substring(1));{const n=e.substring(0,t),r=e.substring(t+1);return new hy(n,r)}}var gy=/\\(\d+)/,Ju=/\\(\d+)/g,_y=-1,up=-2;var Zr=class{constructor(e,t,n,r){k(this,"$location");k(this,"id");k(this,"_nameIsCapturing");k(this,"_name");k(this,"_contentNameIsCapturing");k(this,"_contentName");this.$location=e,this.id=t,this._name=n||null,this._nameIsCapturing=ko.hasCaptures(this._name),this._contentName=r||null,this._contentNameIsCapturing=ko.hasCaptures(this._contentName)}get debugName(){const e=this.$location?`${ep(this.$location.filename)}:${this.$location.line}`:"unknown";return`${this.constructor.name}#${this.id} @ ${e}`}getName(e,t){return!this._nameIsCapturing||this._name===null||e===null||t===null?this._name:ko.replaceCaptures(this._name,e,t)}getContentName(e,t){return!this._contentNameIsCapturing||this._contentName===null?this._contentName:ko.replaceCaptures(this._contentName,e,t)}},yy=class extends Zr{constructor(t,n,r,o,i){super(t,n,r,o);k(this,"retokenizeCapturedWithRuleId");this.retokenizeCapturedWithRuleId=i}dispose(){}collectPatterns(t,n){throw new Error("Not supported!")}compile(t,n){throw new Error("Not supported!")}compileAG(t,n,r,o){throw new Error("Not supported!")}},vy=class extends Zr{constructor(t,n,r,o,i){super(t,n,r,null);k(this,"_match");k(this,"captures");k(this,"_cachedCompiledPatterns");this._match=new $r(o,this.id),this.captures=i,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugMatchRegExp(){return`${this._match.source}`}collectPatterns(t,n){n.push(this._match)}compile(t,n){return this._getCachedCompiledPatterns(t).compile(t)}compileAG(t,n,r,o){return this._getCachedCompiledPatterns(t).compileAG(t,r,o)}_getCachedCompiledPatterns(t){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new Br,this.collectPatterns(t,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},Zu=class extends Zr{constructor(t,n,r,o,i){super(t,n,r,o);k(this,"hasMissingPatterns");k(this,"patterns");k(this,"_cachedCompiledPatterns");this.patterns=i.patterns,this.hasMissingPatterns=i.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}collectPatterns(t,n){for(const r of this.patterns)t.getRule(r).collectPatterns(t,n)}compile(t,n){return this._getCachedCompiledPatterns(t).compile(t)}compileAG(t,n,r,o){return this._getCachedCompiledPatterns(t).compileAG(t,r,o)}_getCachedCompiledPatterns(t){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new Br,this.collectPatterns(t,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},xs=class extends Zr{constructor(t,n,r,o,i,l,s,a,u,d){super(t,n,r,o);k(this,"_begin");k(this,"beginCaptures");k(this,"_end");k(this,"endHasBackReferences");k(this,"endCaptures");k(this,"applyEndPatternLast");k(this,"hasMissingPatterns");k(this,"patterns");k(this,"_cachedCompiledPatterns");this._begin=new $r(i,this.id),this.beginCaptures=l,this._end=new $r(s||"￿",-1),this.endHasBackReferences=this._end.hasBackReferences,this.endCaptures=a,this.applyEndPatternLast=u||!1,this.patterns=d.patterns,this.hasMissingPatterns=d.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugEndRegExp(){return`${this._end.source}`}getEndWithResolvedBackReferences(t,n){return this._end.resolveBackReferences(t,n)}collectPatterns(t,n){n.push(this._begin)}compile(t,n){return this._getCachedCompiledPatterns(t,n).compile(t)}compileAG(t,n,r,o){return this._getCachedCompiledPatterns(t,n).compileAG(t,r,o)}_getCachedCompiledPatterns(t,n){if(!this._cachedCompiledPatterns){this._cachedCompiledPatterns=new Br;for(const r of this.patterns)t.getRule(r).collectPatterns(t,this._cachedCompiledPatterns);this.applyEndPatternLast?this._cachedCompiledPatterns.push(this._end.hasBackReferences?this._end.clone():this._end):this._cachedCompiledPatterns.unshift(this._end.hasBackReferences?this._end.clone():this._end)}return this._end.hasBackReferences&&(this.applyEndPatternLast?this._cachedCompiledPatterns.setSource(this._cachedCompiledPatterns.length()-1,n):this._cachedCompiledPatterns.setSource(0,n)),this._cachedCompiledPatterns}},hi=class extends Zr{constructor(t,n,r,o,i,l,s,a,u){super(t,n,r,o);k(this,"_begin");k(this,"beginCaptures");k(this,"whileCaptures");k(this,"_while");k(this,"whileHasBackReferences");k(this,"hasMissingPatterns");k(this,"patterns");k(this,"_cachedCompiledPatterns");k(this,"_cachedCompiledWhilePatterns");this._begin=new $r(i,this.id),this.beginCaptures=l,this.whileCaptures=a,this._while=new $r(s,up),this.whileHasBackReferences=this._while.hasBackReferences,this.patterns=u.patterns,this.hasMissingPatterns=u.hasMissingPatterns,this._cachedCompiledPatterns=null,this._cachedCompiledWhilePatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null),this._cachedCompiledWhilePatterns&&(this._cachedCompiledWhilePatterns.dispose(),this._cachedCompiledWhilePatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugWhileRegExp(){return`${this._while.source}`}getWhileWithResolvedBackReferences(t,n){return this._while.resolveBackReferences(t,n)}collectPatterns(t,n){n.push(this._begin)}compile(t,n){return this._getCachedCompiledPatterns(t).compile(t)}compileAG(t,n,r,o){return this._getCachedCompiledPatterns(t).compileAG(t,r,o)}_getCachedCompiledPatterns(t){if(!this._cachedCompiledPatterns){this._cachedCompiledPatterns=new Br;for(const n of this.patterns)t.getRule(n).collectPatterns(t,this._cachedCompiledPatterns)}return this._cachedCompiledPatterns}compileWhile(t,n){return this._getCachedCompiledWhilePatterns(t,n).compile(t)}compileWhileAG(t,n,r,o){return this._getCachedCompiledWhilePatterns(t,n).compileAG(t,r,o)}_getCachedCompiledWhilePatterns(t,n){return this._cachedCompiledWhilePatterns||(this._cachedCompiledWhilePatterns=new Br,this._cachedCompiledWhilePatterns.push(this._while.hasBackReferences?this._while.clone():this._while)),this._while.hasBackReferences&&this._cachedCompiledWhilePatterns.setSource(0,n||"￿"),this._cachedCompiledWhilePatterns}},cp=class he{static createCaptureRule(t,n,r,o,i){return t.registerRule(l=>new yy(n,l,r,o,i))}static getCompiledRuleId(t,n,r){return t.id||n.registerRule(o=>{if(t.id=o,t.match)return new vy(t.$vscodeTextmateLocation,t.id,t.name,t.match,he._compileCaptures(t.captures,n,r));if(typeof t.begin>"u"){t.repository&&(r=Zf({},r,t.repository));let i=t.patterns;return typeof i>"u"&&t.include&&(i=[{include:t.include}]),new Zu(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,he._compilePatterns(i,n,r))}return t.while?new hi(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,t.begin,he._compileCaptures(t.beginCaptures||t.captures,n,r),t.while,he._compileCaptures(t.whileCaptures||t.captures,n,r),he._compilePatterns(t.patterns,n,r)):new xs(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,t.begin,he._compileCaptures(t.beginCaptures||t.captures,n,r),t.end,he._compileCaptures(t.endCaptures||t.captures,n,r),t.applyEndPatternLast,he._compilePatterns(t.patterns,n,r))}),t.id}static _compileCaptures(t,n,r){let o=[];if(t){let i=0;for(const l in t){if(l==="$vscodeTextmateLocation")continue;const s=parseInt(l,10);s>i&&(i=s)}for(let l=0;l<=i;l++)o[l]=null;for(const l in t){if(l==="$vscodeTextmateLocation")continue;const s=parseInt(l,10);let a=0;t[l].patterns&&(a=he.getCompiledRuleId(t[l],n,r)),o[s]=he.createCaptureRule(n,t[l].$vscodeTextmateLocation,t[l].name,t[l].contentName,a)}}return o}static _compilePatterns(t,n,r){let o=[];if(t)for(let i=0,l=t.length;i<l;i++){const s=t[i];let a=-1;if(s.include){const u=ap(s.include);switch(u.kind){case 0:case 1:a=he.getCompiledRuleId(r[s.include],n,r);break;case 2:let d=r[u.ruleName];d&&(a=he.getCompiledRuleId(d,n,r));break;case 3:case 4:const f=u.scopeName,m=u.kind===4?u.ruleName:null,g=n.getExternalGrammar(f,r);if(g)if(m){let _=g.repository[m];_&&(a=he.getCompiledRuleId(_,n,g.repository))}else a=he.getCompiledRuleId(g.repository.$self,n,g.repository);break}}else a=he.getCompiledRuleId(s,n,r);if(a!==-1){const u=n.getRule(a);let d=!1;if((u instanceof Zu||u instanceof xs||u instanceof hi)&&u.hasMissingPatterns&&u.patterns.length===0&&(d=!0),d)continue;o.push(a)}}return{patterns:o,hasMissingPatterns:(t?t.length:0)!==o.length}}},$r=class dp{constructor(t,n){k(this,"source");k(this,"ruleId");k(this,"hasAnchor");k(this,"hasBackReferences");k(this,"_anchorCache");if(t&&typeof t=="string"){const r=t.length;let o=0,i=[],l=!1;for(let s=0;s<r;s++)if(t.charAt(s)==="\\"&&s+1<r){const u=t.charAt(s+1);u==="z"?(i.push(t.substring(o,s)),i.push("$(?!\\n)(?<!\\n)"),o=s+2):(u==="A"||u==="G")&&(l=!0),s++}this.hasAnchor=l,o===0?this.source=t:(i.push(t.substring(o,r)),this.source=i.join(""))}else this.hasAnchor=!1,this.source=t;this.hasAnchor?this._anchorCache=this._buildAnchorCache():this._anchorCache=null,this.ruleId=n,typeof this.source=="string"?this.hasBackReferences=gy.test(this.source):this.hasBackReferences=!1}clone(){return new dp(this.source,this.ruleId)}setSource(t){this.source!==t&&(this.source=t,this.hasAnchor&&(this._anchorCache=this._buildAnchorCache()))}resolveBackReferences(t,n){if(typeof this.source!="string")throw new Error("This method should only be called if the source is a string");let r=n.map(o=>t.substring(o.start,o.end));return Ju.lastIndex=0,this.source.replace(Ju,(o,i)=>rp(r[parseInt(i,10)]||""))}_buildAnchorCache(){if(typeof this.source!="string")throw new Error("This method should only be called if the source is a string");let t=[],n=[],r=[],o=[],i,l,s,a;for(i=0,l=this.source.length;i<l;i++)s=this.source.charAt(i),t[i]=s,n[i]=s,r[i]=s,o[i]=s,s==="\\"&&i+1<l&&(a=this.source.charAt(i+1),a==="A"?(t[i+1]="￿",n[i+1]="￿",r[i+1]="A",o[i+1]="A"):a==="G"?(t[i+1]="￿",n[i+1]="G",r[i+1]="￿",o[i+1]="G"):(t[i+1]=a,n[i+1]=a,r[i+1]=a,o[i+1]=a),i++);return{A0_G0:t.join(""),A0_G1:n.join(""),A1_G0:r.join(""),A1_G1:o.join("")}}resolveAnchors(t,n){return!this.hasAnchor||!this._anchorCache||typeof this.source!="string"?this.source:t?n?this._anchorCache.A1_G1:this._anchorCache.A1_G0:n?this._anchorCache.A0_G1:this._anchorCache.A0_G0}},Br=class{constructor(){k(this,"_items");k(this,"_hasAnchors");k(this,"_cached");k(this,"_anchorCache");this._items=[],this._hasAnchors=!1,this._cached=null,this._anchorCache={A0_G0:null,A0_G1:null,A1_G0:null,A1_G1:null}}dispose(){this._disposeCaches()}_disposeCaches(){this._cached&&(this._cached.dispose(),this._cached=null),this._anchorCache.A0_G0&&(this._anchorCache.A0_G0.dispose(),this._anchorCache.A0_G0=null),this._anchorCache.A0_G1&&(this._anchorCache.A0_G1.dispose(),this._anchorCache.A0_G1=null),this._anchorCache.A1_G0&&(this._anchorCache.A1_G0.dispose(),this._anchorCache.A1_G0=null),this._anchorCache.A1_G1&&(this._anchorCache.A1_G1.dispose(),this._anchorCache.A1_G1=null)}push(e){this._items.push(e),this._hasAnchors=this._hasAnchors||e.hasAnchor}unshift(e){this._items.unshift(e),this._hasAnchors=this._hasAnchors||e.hasAnchor}length(){return this._items.length}setSource(e,t){this._items[e].source!==t&&(this._disposeCaches(),this._items[e].setSource(t))}compile(e){if(!this._cached){let t=this._items.map(n=>n.source);this._cached=new ec(e,t,this._items.map(n=>n.ruleId))}return this._cached}compileAG(e,t,n){return this._hasAnchors?t?n?(this._anchorCache.A1_G1||(this._anchorCache.A1_G1=this._resolveAnchors(e,t,n)),this._anchorCache.A1_G1):(this._anchorCache.A1_G0||(this._anchorCache.A1_G0=this._resolveAnchors(e,t,n)),this._anchorCache.A1_G0):n?(this._anchorCache.A0_G1||(this._anchorCache.A0_G1=this._resolveAnchors(e,t,n)),this._anchorCache.A0_G1):(this._anchorCache.A0_G0||(this._anchorCache.A0_G0=this._resolveAnchors(e,t,n)),this._anchorCache.A0_G0):this.compile(e)}_resolveAnchors(e,t,n){let r=this._items.map(o=>o.resolveAnchors(t,n));return new ec(e,r,this._items.map(o=>o.ruleId))}},ec=class{constructor(e,t,n){k(this,"scanner");this.regExps=t,this.rules=n,this.scanner=e.createOnigScanner(t)}dispose(){typeof this.scanner.dispose=="function"&&this.scanner.dispose()}toString(){const e=[];for(let t=0,n=this.rules.length;t<n;t++)e.push("   - "+this.rules[t]+": "+this.regExps[t]);return e.join(`
`)}findNextMatchSync(e,t,n){const r=this.scanner.findNextMatchSync(e,t,n);return r?{ruleId:this.rules[r.index],captureIndices:r.captureIndices}:null}},wl=class{constructor(e,t){this.languageId=e,this.tokenType=t}},ut,Ey=(ut=class{constructor(t,n){k(this,"_defaultAttributes");k(this,"_embeddedLanguagesMatcher");k(this,"_getBasicScopeAttributes",new op(t=>{const n=this._scopeToLanguage(t),r=this._toStandardTokenType(t);return new wl(n,r)}));this._defaultAttributes=new wl(t,8),this._embeddedLanguagesMatcher=new Sy(Object.entries(n||{}))}getDefaultAttributes(){return this._defaultAttributes}getBasicScopeAttributes(t){return t===null?ut._NULL_SCOPE_METADATA:this._getBasicScopeAttributes.get(t)}_scopeToLanguage(t){return this._embeddedLanguagesMatcher.match(t)||0}_toStandardTokenType(t){const n=t.match(ut.STANDARD_TOKEN_TYPE_REGEXP);if(!n)return 8;switch(n[1]){case"comment":return 1;case"string":return 2;case"regex":return 3;case"meta.embedded":return 0}throw new Error("Unexpected match for standard token type!")}},k(ut,"_NULL_SCOPE_METADATA",new wl(0,0)),k(ut,"STANDARD_TOKEN_TYPE_REGEXP",/\b(comment|string|regex|meta\.embedded)\b/),ut),Sy=class{constructor(e){k(this,"values");k(this,"scopesRegExp");if(e.length===0)this.values=null,this.scopesRegExp=null;else{this.values=new Map(e);const t=e.map(([n,r])=>rp(n));t.sort(),t.reverse(),this.scopesRegExp=new RegExp(`^((${t.join(")|(")}))($|\\.)`,"")}}match(e){if(!this.scopesRegExp)return;const t=e.match(this.scopesRegExp);if(t)return this.values.get(t[1])}},tc=class{constructor(e,t){this.stack=e,this.stoppedEarly=t}};function fp(e,t,n,r,o,i,l,s){const a=t.content.length;let u=!1,d=-1;if(l){const g=wy(e,t,n,r,o,i);o=g.stack,r=g.linePos,n=g.isFirstLine,d=g.anchorPosition}const f=Date.now();for(;!u;){if(s!==0&&Date.now()-f>s)return new tc(o,!0);m()}return new tc(o,!1);function m(){const g=ky(e,t,n,r,o,d);if(!g){i.produce(o,a),u=!0;return}const _=g.captureIndices,E=g.matchedRuleId,S=_&&_.length>0?_[0].end>r:!1;if(E===_y){const p=o.getRule(e);i.produce(o,_[0].start),o=o.withContentNameScopesList(o.nameScopesList),dr(e,t,n,o,i,p.endCaptures,_),i.produce(o,_[0].end);const c=o;if(o=o.parent,d=c.getAnchorPos(),!S&&c.getEnterPos()===r){o=c,i.produce(o,a),u=!0;return}}else{const p=e.getRule(E);i.produce(o,_[0].start);const c=o,y=p.getName(t.content,_),w=o.contentNameScopesList.pushAttributed(y,e);if(o=o.push(E,r,d,_[0].end===a,null,w,w),p instanceof xs){const x=p;dr(e,t,n,o,i,x.beginCaptures,_),i.produce(o,_[0].end),d=_[0].end;const T=x.getContentName(t.content,_),P=w.pushAttributed(T,e);if(o=o.withContentNameScopesList(P),x.endHasBackReferences&&(o=o.withEndRule(x.getEndWithResolvedBackReferences(t.content,_))),!S&&c.hasSameRuleAs(o)){o=o.pop(),i.produce(o,a),u=!0;return}}else if(p instanceof hi){const x=p;dr(e,t,n,o,i,x.beginCaptures,_),i.produce(o,_[0].end),d=_[0].end;const T=x.getContentName(t.content,_),P=w.pushAttributed(T,e);if(o=o.withContentNameScopesList(P),x.whileHasBackReferences&&(o=o.withEndRule(x.getWhileWithResolvedBackReferences(t.content,_))),!S&&c.hasSameRuleAs(o)){o=o.pop(),i.produce(o,a),u=!0;return}}else if(dr(e,t,n,o,i,p.captures,_),i.produce(o,_[0].end),o=o.pop(),!S){o=o.safePop(),i.produce(o,a),u=!0;return}}_[0].end>r&&(r=_[0].end,n=!1)}}function wy(e,t,n,r,o,i){let l=o.beginRuleCapturedEOL?0:-1;const s=[];for(let a=o;a;a=a.pop()){const u=a.getRule(e);u instanceof hi&&s.push({rule:u,stack:a})}for(let a=s.pop();a;a=s.pop()){const{ruleScanner:u,findOptions:d}=Ry(a.rule,e,a.stack.endRule,n,r===l),f=u.findNextMatchSync(t,r,d);if(f){if(f.ruleId!==up){o=a.stack.pop();break}f.captureIndices&&f.captureIndices.length&&(i.produce(a.stack,f.captureIndices[0].start),dr(e,t,n,a.stack,i,a.rule.whileCaptures,f.captureIndices),i.produce(a.stack,f.captureIndices[0].end),l=f.captureIndices[0].end,f.captureIndices[0].end>r&&(r=f.captureIndices[0].end,n=!1))}else{o=a.stack.pop();break}}return{stack:o,linePos:r,anchorPosition:l,isFirstLine:n}}function ky(e,t,n,r,o,i){const l=xy(e,t,n,r,o,i),s=e.getInjections();if(s.length===0)return l;const a=Cy(s,e,t,n,r,o,i);if(!a)return l;if(!l)return a;const u=l.captureIndices[0].start,d=a.captureIndices[0].start;return d<u||a.priorityMatch&&d===u?a:l}function xy(e,t,n,r,o,i){const l=o.getRule(e),{ruleScanner:s,findOptions:a}=pp(l,e,o.endRule,n,r===i),u=s.findNextMatchSync(t,r,a);return u?{captureIndices:u.captureIndices,matchedRuleId:u.ruleId}:null}function Cy(e,t,n,r,o,i,l){let s=Number.MAX_VALUE,a=null,u,d=0;const f=i.contentNameScopesList.getScopeNames();for(let m=0,g=e.length;m<g;m++){const _=e[m];if(!_.matcher(f))continue;const E=t.getRule(_.ruleId),{ruleScanner:S,findOptions:p}=pp(E,t,null,r,o===l),c=S.findNextMatchSync(n,o,p);if(!c)continue;const y=c.captureIndices[0].start;if(!(y>=s)&&(s=y,a=c.captureIndices,u=c.ruleId,d=_.priority,s===o))break}return a?{priorityMatch:d===-1,captureIndices:a,matchedRuleId:u}:null}function pp(e,t,n,r,o){return{ruleScanner:e.compileAG(t,n,r,o),findOptions:0}}function Ry(e,t,n,r,o){return{ruleScanner:e.compileWhileAG(t,n,r,o),findOptions:0}}function dr(e,t,n,r,o,i,l){if(i.length===0)return;const s=t.content,a=Math.min(i.length,l.length),u=[],d=l[0].end;for(let f=0;f<a;f++){const m=i[f];if(m===null)continue;const g=l[f];if(g.length===0)continue;if(g.start>d)break;for(;u.length>0&&u[u.length-1].endPos<=g.start;)o.produceFromScopes(u[u.length-1].scopes,u[u.length-1].endPos),u.pop();if(u.length>0?o.produceFromScopes(u[u.length-1].scopes,g.start):o.produce(r,g.start),m.retokenizeCapturedWithRuleId){const E=m.getName(s,l),S=r.contentNameScopesList.pushAttributed(E,e),p=m.getContentName(s,l),c=S.pushAttributed(p,e),y=r.push(m.retokenizeCapturedWithRuleId,g.start,-1,!1,null,S,c),w=e.createOnigString(s.substring(0,g.end));fp(e,w,n&&g.start===0,g.start,y,o,!1,0),sp(w);continue}const _=m.getName(s,l);if(_!==null){const S=(u.length>0?u[u.length-1].scopes:r.contentNameScopesList).pushAttributed(_,e);u.push(new Py(S,g.end))}}for(;u.length>0;)o.produceFromScopes(u[u.length-1].scopes,u[u.length-1].endPos),u.pop()}var Py=class{constructor(e,t){k(this,"scopes");k(this,"endPos");this.scopes=e,this.endPos=t}};function Ty(e,t,n,r,o,i,l,s){return new Oy(e,t,n,r,o,i,l,s)}function nc(e,t,n,r,o){const i=pi(t,gi),l=cp.getCompiledRuleId(n,r,o.repository);for(const s of i)e.push({debugSelector:t,matcher:s.matcher,ruleId:l,grammar:o,priority:s.priority})}function gi(e,t){if(t.length<e.length)return!1;let n=0;return e.every(r=>{for(let o=n;o<t.length;o++)if(Ly(t[o],r))return n=o+1,!0;return!1})}function Ly(e,t){if(!e)return!1;if(e===t)return!0;const n=t.length;return e.length>n&&e.substr(0,n)===t&&e[n]==="."}var Oy=class{constructor(e,t,n,r,o,i,l,s){k(this,"_rootId");k(this,"_lastRuleId");k(this,"_ruleId2desc");k(this,"_includedGrammars");k(this,"_grammarRepository");k(this,"_grammar");k(this,"_injections");k(this,"_basicScopeAttributesProvider");k(this,"_tokenTypeMatchers");if(this._rootScopeName=e,this.balancedBracketSelectors=i,this._onigLib=s,this._basicScopeAttributesProvider=new Ey(n,r),this._rootId=-1,this._lastRuleId=0,this._ruleId2desc=[null],this._includedGrammars={},this._grammarRepository=l,this._grammar=rc(t,null),this._injections=null,this._tokenTypeMatchers=[],o)for(const a of Object.keys(o)){const u=pi(a,gi);for(const d of u)this._tokenTypeMatchers.push({matcher:d.matcher,type:o[a]})}}get themeProvider(){return this._grammarRepository}dispose(){for(const e of this._ruleId2desc)e&&e.dispose()}createOnigScanner(e){return this._onigLib.createOnigScanner(e)}createOnigString(e){return this._onigLib.createOnigString(e)}getMetadataForScope(e){return this._basicScopeAttributesProvider.getBasicScopeAttributes(e)}_collectInjections(){const e={lookup:o=>o===this._rootScopeName?this._grammar:this.getExternalGrammar(o),injections:o=>this._grammarRepository.injections(o)},t=[],n=this._rootScopeName,r=e.lookup(n);if(r){const o=r.injections;if(o)for(let l in o)nc(t,l,o[l],this,r);const i=this._grammarRepository.injections(n);i&&i.forEach(l=>{const s=this.getExternalGrammar(l);if(s){const a=s.injectionSelector;a&&nc(t,a,s,this,s)}})}return t.sort((o,i)=>o.priority-i.priority),t}getInjections(){return this._injections===null&&(this._injections=this._collectInjections()),this._injections}registerRule(e){const t=++this._lastRuleId,n=e(t);return this._ruleId2desc[t]=n,n}getRule(e){return this._ruleId2desc[e]}getExternalGrammar(e,t){if(this._includedGrammars[e])return this._includedGrammars[e];if(this._grammarRepository){const n=this._grammarRepository.lookup(e);if(n)return this._includedGrammars[e]=rc(n,t&&t.$base),this._includedGrammars[e]}}tokenizeLine(e,t,n=0){const r=this._tokenize(e,t,!1,n);return{tokens:r.lineTokens.getResult(r.ruleStack,r.lineLength),ruleStack:r.ruleStack,stoppedEarly:r.stoppedEarly}}tokenizeLine2(e,t,n=0){const r=this._tokenize(e,t,!0,n);return{tokens:r.lineTokens.getBinaryResult(r.ruleStack,r.lineLength),ruleStack:r.ruleStack,stoppedEarly:r.stoppedEarly}}_tokenize(e,t,n,r){this._rootId===-1&&(this._rootId=cp.getCompiledRuleId(this._grammar.repository.$self,this,this._grammar.repository),this.getInjections());let o;if(!t||t===Cs.NULL){o=!0;const u=this._basicScopeAttributesProvider.getDefaultAttributes(),d=this.themeProvider.getDefaults(),f=Gn.set(0,u.languageId,u.tokenType,null,d.fontStyle,d.foregroundId,d.backgroundId),m=this.getRule(this._rootId).getName(null,null);let g;m?g=Sr.createRootAndLookUpScopeName(m,f,this):g=Sr.createRoot("unknown",f),t=new Cs(null,this._rootId,-1,-1,!1,null,g,g)}else o=!1,t.reset();e=e+`
`;const i=this.createOnigString(e),l=i.content.length,s=new Iy(n,e,this._tokenTypeMatchers,this.balancedBracketSelectors),a=fp(this,i,o,0,t,s,!0,r);return sp(i),{lineLength:l,lineTokens:s,ruleStack:a.stack,stoppedEarly:a.stoppedEarly}}};function rc(e,t){return e=q_(e),e.repository=e.repository||{},e.repository.$self={$vscodeTextmateLocation:e.$vscodeTextmateLocation,patterns:e.patterns,name:e.scopeName},e.repository.$base=t||e.repository.$self,e}var Sr=class nt{constructor(t,n,r){this.parent=t,this.scopePath=n,this.tokenAttributes=r}static fromExtension(t,n){let r=t,o=(t==null?void 0:t.scopePath)??null;for(const i of n)o=Sl.push(o,i.scopeNames),r=new nt(r,o,i.encodedTokenAttributes);return r}static createRoot(t,n){return new nt(null,new Sl(null,t),n)}static createRootAndLookUpScopeName(t,n,r){const o=r.getMetadataForScope(t),i=new Sl(null,t),l=r.themeProvider.themeMatch(i),s=nt.mergeAttributes(n,o,l);return new nt(null,i,s)}get scopeName(){return this.scopePath.scopeName}toString(){return this.getScopeNames().join(" ")}equals(t){return nt.equals(this,t)}static equals(t,n){do{if(t===n||!t&&!n)return!0;if(!t||!n||t.scopeName!==n.scopeName||t.tokenAttributes!==n.tokenAttributes)return!1;t=t.parent,n=n.parent}while(!0)}static mergeAttributes(t,n,r){let o=-1,i=0,l=0;return r!==null&&(o=r.fontStyle,i=r.foregroundId,l=r.backgroundId),Gn.set(t,n.languageId,n.tokenType,null,o,i,l)}pushAttributed(t,n){if(t===null)return this;if(t.indexOf(" ")===-1)return nt._pushAttributed(this,t,n);const r=t.split(/ /g);let o=this;for(const i of r)o=nt._pushAttributed(o,i,n);return o}static _pushAttributed(t,n,r){const o=r.getMetadataForScope(n),i=t.scopePath.push(n),l=r.themeProvider.themeMatch(i),s=nt.mergeAttributes(t.tokenAttributes,o,l);return new nt(t,i,s)}getScopeNames(){return this.scopePath.getSegments()}getExtensionIfDefined(t){var o;const n=[];let r=this;for(;r&&r!==t;)n.push({encodedTokenAttributes:r.tokenAttributes,scopeNames:r.scopePath.getExtensionIfDefined(((o=r.parent)==null?void 0:o.scopePath)??null)}),r=r.parent;return r===t?n.reverse():void 0}},Be,Cs=(Be=class{constructor(t,n,r,o,i,l,s,a){k(this,"_stackElementBrand");k(this,"_enterPos");k(this,"_anchorPos");k(this,"depth");this.parent=t,this.ruleId=n,this.beginRuleCapturedEOL=i,this.endRule=l,this.nameScopesList=s,this.contentNameScopesList=a,this.depth=this.parent?this.parent.depth+1:1,this._enterPos=r,this._anchorPos=o}equals(t){return t===null?!1:Be._equals(this,t)}static _equals(t,n){return t===n?!0:this._structuralEquals(t,n)?Sr.equals(t.contentNameScopesList,n.contentNameScopesList):!1}static _structuralEquals(t,n){do{if(t===n||!t&&!n)return!0;if(!t||!n||t.depth!==n.depth||t.ruleId!==n.ruleId||t.endRule!==n.endRule)return!1;t=t.parent,n=n.parent}while(!0)}clone(){return this}static _reset(t){for(;t;)t._enterPos=-1,t._anchorPos=-1,t=t.parent}reset(){Be._reset(this)}pop(){return this.parent}safePop(){return this.parent?this.parent:this}push(t,n,r,o,i,l,s){return new Be(this,t,n,r,o,i,l,s)}getEnterPos(){return this._enterPos}getAnchorPos(){return this._anchorPos}getRule(t){return t.getRule(this.ruleId)}toString(){const t=[];return this._writeString(t,0),"["+t.join(",")+"]"}_writeString(t,n){var r,o;return this.parent&&(n=this.parent._writeString(t,n)),t[n++]=`(${this.ruleId}, ${(r=this.nameScopesList)==null?void 0:r.toString()}, ${(o=this.contentNameScopesList)==null?void 0:o.toString()})`,n}withContentNameScopesList(t){return this.contentNameScopesList===t?this:this.parent.push(this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,this.endRule,this.nameScopesList,t)}withEndRule(t){return this.endRule===t?this:new Be(this.parent,this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,t,this.nameScopesList,this.contentNameScopesList)}hasSameRuleAs(t){let n=this;for(;n&&n._enterPos===t._enterPos;){if(n.ruleId===t.ruleId)return!0;n=n.parent}return!1}toStateStackFrame(){var t,n,r;return{ruleId:this.ruleId,beginRuleCapturedEOL:this.beginRuleCapturedEOL,endRule:this.endRule,nameScopesList:((n=this.nameScopesList)==null?void 0:n.getExtensionIfDefined(((t=this.parent)==null?void 0:t.nameScopesList)??null))??[],contentNameScopesList:((r=this.contentNameScopesList)==null?void 0:r.getExtensionIfDefined(this.nameScopesList))??[]}}static pushFrame(t,n){const r=Sr.fromExtension((t==null?void 0:t.nameScopesList)??null,n.nameScopesList);return new Be(t,n.ruleId,n.enterPos??-1,n.anchorPos??-1,n.beginRuleCapturedEOL,n.endRule,r,Sr.fromExtension(r,n.contentNameScopesList))}},k(Be,"NULL",new Be(null,0,0,0,!1,null,null,null)),Be),Ay=class{constructor(e,t){k(this,"balancedBracketScopes");k(this,"unbalancedBracketScopes");k(this,"allowAny",!1);this.balancedBracketScopes=e.flatMap(n=>n==="*"?(this.allowAny=!0,[]):pi(n,gi).map(r=>r.matcher)),this.unbalancedBracketScopes=t.flatMap(n=>pi(n,gi).map(r=>r.matcher))}get matchesAlways(){return this.allowAny&&this.unbalancedBracketScopes.length===0}get matchesNever(){return this.balancedBracketScopes.length===0&&!this.allowAny}match(e){for(const t of this.unbalancedBracketScopes)if(t(e))return!1;for(const t of this.balancedBracketScopes)if(t(e))return!0;return this.allowAny}},Iy=class{constructor(e,t,n,r){k(this,"_emitBinaryTokens");k(this,"_lineText");k(this,"_tokens");k(this,"_binaryTokens");k(this,"_lastTokenEndIndex");k(this,"_tokenTypeOverrides");this.balancedBracketSelectors=r,this._emitBinaryTokens=e,this._tokenTypeOverrides=n,this._lineText=null,this._tokens=[],this._binaryTokens=[],this._lastTokenEndIndex=0}produce(e,t){this.produceFromScopes(e.contentNameScopesList,t)}produceFromScopes(e,t){var r;if(this._lastTokenEndIndex>=t)return;if(this._emitBinaryTokens){let o=(e==null?void 0:e.tokenAttributes)??0,i=!1;if((r=this.balancedBracketSelectors)!=null&&r.matchesAlways&&(i=!0),this._tokenTypeOverrides.length>0||this.balancedBracketSelectors&&!this.balancedBracketSelectors.matchesAlways&&!this.balancedBracketSelectors.matchesNever){const l=(e==null?void 0:e.getScopeNames())??[];for(const s of this._tokenTypeOverrides)s.matcher(l)&&(o=Gn.set(o,0,s.type,null,-1,0,0));this.balancedBracketSelectors&&(i=this.balancedBracketSelectors.match(l))}if(i&&(o=Gn.set(o,0,8,i,-1,0,0)),this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-1]===o){this._lastTokenEndIndex=t;return}this._binaryTokens.push(this._lastTokenEndIndex),this._binaryTokens.push(o),this._lastTokenEndIndex=t;return}const n=(e==null?void 0:e.getScopeNames())??[];this._tokens.push({startIndex:this._lastTokenEndIndex,endIndex:t,scopes:n}),this._lastTokenEndIndex=t}getResult(e,t){return this._tokens.length>0&&this._tokens[this._tokens.length-1].startIndex===t-1&&this._tokens.pop(),this._tokens.length===0&&(this._lastTokenEndIndex=-1,this.produce(e,t),this._tokens[this._tokens.length-1].startIndex=0),this._tokens}getBinaryResult(e,t){this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-2]===t-1&&(this._binaryTokens.pop(),this._binaryTokens.pop()),this._binaryTokens.length===0&&(this._lastTokenEndIndex=-1,this.produce(e,t),this._binaryTokens[this._binaryTokens.length-2]=0);const n=new Uint32Array(this._binaryTokens.length);for(let r=0,o=this._binaryTokens.length;r<o;r++)n[r]=this._binaryTokens[r];return n}},Ny=class{constructor(e,t){k(this,"_grammars",new Map);k(this,"_rawGrammars",new Map);k(this,"_injectionGrammars",new Map);k(this,"_theme");this._onigLib=t,this._theme=e}dispose(){for(const e of this._grammars.values())e.dispose()}setTheme(e){this._theme=e}getColorMap(){return this._theme.getColorMap()}addGrammar(e,t){this._rawGrammars.set(e.scopeName,e),t&&this._injectionGrammars.set(e.scopeName,t)}lookup(e){return this._rawGrammars.get(e)}injections(e){return this._injectionGrammars.get(e)}getDefaults(){return this._theme.getDefaults()}themeMatch(e){return this._theme.match(e)}grammarForScopeName(e,t,n,r,o){if(!this._grammars.has(e)){let i=this._rawGrammars.get(e);if(!i)return null;this._grammars.set(e,Ty(e,i,t,n,r,o,this,this._onigLib))}return this._grammars.get(e)}},Dy=class{constructor(t){k(this,"_options");k(this,"_syncRegistry");k(this,"_ensureGrammarCache");this._options=t,this._syncRegistry=new Ny(fi.createFromRawTheme(t.theme,t.colorMap),t.onigLib),this._ensureGrammarCache=new Map}dispose(){this._syncRegistry.dispose()}setTheme(t,n){this._syncRegistry.setTheme(fi.createFromRawTheme(t,n))}getColorMap(){return this._syncRegistry.getColorMap()}loadGrammarWithEmbeddedLanguages(t,n,r){return this.loadGrammarWithConfiguration(t,n,{embeddedLanguages:r})}loadGrammarWithConfiguration(t,n,r){return this._loadGrammar(t,n,r.embeddedLanguages,r.tokenTypes,new Ay(r.balancedBracketSelectors||[],r.unbalancedBracketSelectors||[]))}loadGrammar(t){return this._loadGrammar(t,0,null,null,null)}_loadGrammar(t,n,r,o,i){const l=new uy(this._syncRegistry,t);for(;l.Q.length>0;)l.Q.map(s=>this._loadSingleGrammar(s.scopeName)),l.processQueue();return this._grammarForScopeName(t,n,r,o,i)}_loadSingleGrammar(t){this._ensureGrammarCache.has(t)||(this._doLoadSingleGrammar(t),this._ensureGrammarCache.set(t,!0))}_doLoadSingleGrammar(t){const n=this._options.loadGrammar(t);if(n){const r=typeof this._options.getInjections=="function"?this._options.getInjections(t):void 0;this._syncRegistry.addGrammar(n,r)}}addGrammar(t,n=[],r=0,o=null){return this._syncRegistry.addGrammar(t,n),this._grammarForScopeName(t.scopeName,r,o)}_grammarForScopeName(t,n=0,r=null,o=null,i=null){return this._syncRegistry.grammarForScopeName(t,n,r,o,i)}},Rs=Cs.NULL;const jy=["area","base","basefont","bgsound","br","col","command","embed","frame","hr","image","img","input","keygen","link","meta","param","source","track","wbr"];class eo{constructor(t,n,r){this.normal=n,this.property=t,r&&(this.space=r)}}eo.prototype.normal={};eo.prototype.property={};eo.prototype.space=void 0;function mp(e,t){const n={},r={};for(const o of e)Object.assign(n,o.property),Object.assign(r,o.normal);return new eo(n,r,t)}function Ps(e){return e.toLowerCase()}class Oe{constructor(t,n){this.attribute=n,this.property=t}}Oe.prototype.attribute="";Oe.prototype.booleanish=!1;Oe.prototype.boolean=!1;Oe.prototype.commaOrSpaceSeparated=!1;Oe.prototype.commaSeparated=!1;Oe.prototype.defined=!1;Oe.prototype.mustUseProperty=!1;Oe.prototype.number=!1;Oe.prototype.overloadedBoolean=!1;Oe.prototype.property="";Oe.prototype.spaceSeparated=!1;Oe.prototype.space=void 0;let My=0;const j=pn(),Z=pn(),Ts=pn(),R=pn(),B=pn(),jn=pn(),Ae=pn();function pn(){return 2**++My}const Ls=Object.freeze(Object.defineProperty({__proto__:null,boolean:j,booleanish:Z,commaOrSpaceSeparated:Ae,commaSeparated:jn,number:R,overloadedBoolean:Ts,spaceSeparated:B},Symbol.toStringTag,{value:"Module"})),kl=Object.keys(Ls);class Ta extends Oe{constructor(t,n,r,o){let i=-1;if(super(t,n),oc(this,"space",o),typeof r=="number")for(;++i<kl.length;){const l=kl[i];oc(this,kl[i],(r&Ls[l])===Ls[l])}}}Ta.prototype.defined=!0;function oc(e,t,n){n&&(e[t]=n)}function Xn(e){const t={},n={};for(const[r,o]of Object.entries(e.properties)){const i=new Ta(r,e.transform(e.attributes||{},r),o,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(i.mustUseProperty=!0),t[r]=i,n[Ps(r)]=r,n[Ps(i.attribute)]=r}return new eo(t,n,e.space)}const hp=Xn({properties:{ariaActiveDescendant:null,ariaAtomic:Z,ariaAutoComplete:null,ariaBusy:Z,ariaChecked:Z,ariaColCount:R,ariaColIndex:R,ariaColSpan:R,ariaControls:B,ariaCurrent:null,ariaDescribedBy:B,ariaDetails:null,ariaDisabled:Z,ariaDropEffect:B,ariaErrorMessage:null,ariaExpanded:Z,ariaFlowTo:B,ariaGrabbed:Z,ariaHasPopup:null,ariaHidden:Z,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:B,ariaLevel:R,ariaLive:null,ariaModal:Z,ariaMultiLine:Z,ariaMultiSelectable:Z,ariaOrientation:null,ariaOwns:B,ariaPlaceholder:null,ariaPosInSet:R,ariaPressed:Z,ariaReadOnly:Z,ariaRelevant:null,ariaRequired:Z,ariaRoleDescription:B,ariaRowCount:R,ariaRowIndex:R,ariaRowSpan:R,ariaSelected:Z,ariaSetSize:R,ariaSort:null,ariaValueMax:R,ariaValueMin:R,ariaValueNow:R,ariaValueText:null,role:null},transform(e,t){return t==="role"?t:"aria-"+t.slice(4).toLowerCase()}});function gp(e,t){return t in e?e[t]:t}function _p(e,t){return gp(e,t.toLowerCase())}const by=Xn({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:jn,acceptCharset:B,accessKey:B,action:null,allow:null,allowFullScreen:j,allowPaymentRequest:j,allowUserMedia:j,alt:null,as:null,async:j,autoCapitalize:null,autoComplete:B,autoFocus:j,autoPlay:j,blocking:B,capture:null,charSet:null,checked:j,cite:null,className:B,cols:R,colSpan:null,content:null,contentEditable:Z,controls:j,controlsList:B,coords:R|jn,crossOrigin:null,data:null,dateTime:null,decoding:null,default:j,defer:j,dir:null,dirName:null,disabled:j,download:Ts,draggable:Z,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:j,formTarget:null,headers:B,height:R,hidden:Ts,high:R,href:null,hrefLang:null,htmlFor:B,httpEquiv:B,id:null,imageSizes:null,imageSrcSet:null,inert:j,inputMode:null,integrity:null,is:null,isMap:j,itemId:null,itemProp:B,itemRef:B,itemScope:j,itemType:B,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:j,low:R,manifest:null,max:null,maxLength:R,media:null,method:null,min:null,minLength:R,multiple:j,muted:j,name:null,nonce:null,noModule:j,noValidate:j,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:j,optimum:R,pattern:null,ping:B,placeholder:null,playsInline:j,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:j,referrerPolicy:null,rel:B,required:j,reversed:j,rows:R,rowSpan:R,sandbox:B,scope:null,scoped:j,seamless:j,selected:j,shadowRootClonable:j,shadowRootDelegatesFocus:j,shadowRootMode:null,shape:null,size:R,sizes:null,slot:null,span:R,spellCheck:Z,src:null,srcDoc:null,srcLang:null,srcSet:null,start:R,step:null,style:null,tabIndex:R,target:null,title:null,translate:null,type:null,typeMustMatch:j,useMap:null,value:Z,width:R,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:B,axis:null,background:null,bgColor:null,border:R,borderColor:null,bottomMargin:R,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:j,declare:j,event:null,face:null,frame:null,frameBorder:null,hSpace:R,leftMargin:R,link:null,longDesc:null,lowSrc:null,marginHeight:R,marginWidth:R,noResize:j,noHref:j,noShade:j,noWrap:j,object:null,profile:null,prompt:null,rev:null,rightMargin:R,rules:null,scheme:null,scrolling:Z,standby:null,summary:null,text:null,topMargin:R,valueType:null,version:null,vAlign:null,vLink:null,vSpace:R,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:j,disableRemotePlayback:j,prefix:null,property:null,results:R,security:null,unselectable:null},space:"html",transform:_p}),Vy=Xn({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:Ae,accentHeight:R,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:R,amplitude:R,arabicForm:null,ascent:R,attributeName:null,attributeType:null,azimuth:R,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:R,by:null,calcMode:null,capHeight:R,className:B,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:R,diffuseConstant:R,direction:null,display:null,dur:null,divisor:R,dominantBaseline:null,download:j,dx:null,dy:null,edgeMode:null,editable:null,elevation:R,enableBackground:null,end:null,event:null,exponent:R,externalResourcesRequired:null,fill:null,fillOpacity:R,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:jn,g2:jn,glyphName:jn,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:R,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:R,horizOriginX:R,horizOriginY:R,id:null,ideographic:R,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:R,k:R,k1:R,k2:R,k3:R,k4:R,kernelMatrix:Ae,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:R,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:R,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:R,overlineThickness:R,paintOrder:null,panose1:null,path:null,pathLength:R,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:B,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:R,pointsAtY:R,pointsAtZ:R,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:Ae,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:Ae,rev:Ae,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:Ae,requiredFeatures:Ae,requiredFonts:Ae,requiredFormats:Ae,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:R,specularExponent:R,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:R,strikethroughThickness:R,string:null,stroke:null,strokeDashArray:Ae,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:R,strokeOpacity:R,strokeWidth:null,style:null,surfaceScale:R,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:Ae,tabIndex:R,tableValues:null,target:null,targetX:R,targetY:R,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:Ae,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:R,underlineThickness:R,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:R,values:null,vAlphabetic:R,vMathematical:R,vectorEffect:null,vHanging:R,vIdeographic:R,version:null,vertAdvY:R,vertOriginX:R,vertOriginY:R,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:R,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:gp}),yp=Xn({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(e,t){return"xlink:"+t.slice(5).toLowerCase()}}),vp=Xn({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:_p}),Ep=Xn({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(e,t){return"xml:"+t.slice(3).toLowerCase()}}),zy=/[A-Z]/g,ic=/-[a-z]/g,Uy=/^data[-\w.:]+$/i;function $y(e,t){const n=Ps(t);let r=t,o=Oe;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&n.slice(0,4)==="data"&&Uy.test(t)){if(t.charAt(4)==="-"){const i=t.slice(5).replace(ic,Fy);r="data"+i.charAt(0).toUpperCase()+i.slice(1)}else{const i=t.slice(4);if(!ic.test(i)){let l=i.replace(zy,By);l.charAt(0)!=="-"&&(l="-"+l),t="data"+l}}o=Ta}return new o(r,t)}function By(e){return"-"+e.toLowerCase()}function Fy(e){return e.charAt(1).toUpperCase()}const Hy=mp([hp,by,yp,vp,Ep],"html"),Sp=mp([hp,Vy,yp,vp,Ep],"svg"),lc={}.hasOwnProperty;function Gy(e,t){const n=t||{};function r(o,...i){let l=r.invalid;const s=r.handlers;if(o&&lc.call(o,e)){const a=String(o[e]);l=lc.call(s,a)?s[a]:r.unknown}if(l)return l.call(this,o,...i)}return r.handlers=n.handlers||{},r.invalid=n.invalid,r.unknown=n.unknown,r}const Wy=/["&'<>`]/g,Ky=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Qy=/[\x01-\t\v\f\x0E-\x1F\x7F\x81\x8D\x8F\x90\x9D\xA0-\uFFFF]/g,qy=/[|\\{}()[\]^$+*?.]/g,sc=new WeakMap;function Xy(e,t){if(e=e.replace(t.subset?Yy(t.subset):Wy,r),t.subset||t.escapeOnly)return e;return e.replace(Ky,n).replace(Qy,r);function n(o,i,l){return t.format((o.charCodeAt(0)-55296)*1024+o.charCodeAt(1)-56320+65536,l.charCodeAt(i+2),t)}function r(o,i,l){return t.format(o.charCodeAt(0),l.charCodeAt(i+1),t)}}function Yy(e){let t=sc.get(e);return t||(t=Jy(e),sc.set(e,t)),t}function Jy(e){const t=[];let n=-1;for(;++n<e.length;)t.push(e[n].replace(qy,"\\$&"));return new RegExp("(?:"+t.join("|")+")","g")}const Zy=/[\dA-Fa-f]/;function ev(e,t,n){const r="&#x"+e.toString(16).toUpperCase();return n&&t&&!Zy.test(String.fromCharCode(t))?r:r+";"}const tv=/\d/;function nv(e,t,n){const r="&#"+String(e);return n&&t&&!tv.test(String.fromCharCode(t))?r:r+";"}const rv=["AElig","AMP","Aacute","Acirc","Agrave","Aring","Atilde","Auml","COPY","Ccedil","ETH","Eacute","Ecirc","Egrave","Euml","GT","Iacute","Icirc","Igrave","Iuml","LT","Ntilde","Oacute","Ocirc","Ograve","Oslash","Otilde","Ouml","QUOT","REG","THORN","Uacute","Ucirc","Ugrave","Uuml","Yacute","aacute","acirc","acute","aelig","agrave","amp","aring","atilde","auml","brvbar","ccedil","cedil","cent","copy","curren","deg","divide","eacute","ecirc","egrave","eth","euml","frac12","frac14","frac34","gt","iacute","icirc","iexcl","igrave","iquest","iuml","laquo","lt","macr","micro","middot","nbsp","not","ntilde","oacute","ocirc","ograve","ordf","ordm","oslash","otilde","ouml","para","plusmn","pound","quot","raquo","reg","sect","shy","sup1","sup2","sup3","szlig","thorn","times","uacute","ucirc","ugrave","uml","uuml","yacute","yen","yuml"],xl={nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",times:"×",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",divide:"÷",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",fnof:"ƒ",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",bull:"•",hellip:"…",prime:"′",Prime:"″",oline:"‾",frasl:"⁄",weierp:"℘",image:"ℑ",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦",quot:'"',amp:"&",lt:"<",gt:">",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",circ:"ˆ",tilde:"˜",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",permil:"‰",lsaquo:"‹",rsaquo:"›",euro:"€"},ov=["cent","copy","divide","gt","lt","not","para","times"],wp={}.hasOwnProperty,Os={};let xo;for(xo in xl)wp.call(xl,xo)&&(Os[xl[xo]]=xo);const iv=/[^\dA-Za-z]/;function lv(e,t,n,r){const o=String.fromCharCode(e);if(wp.call(Os,o)){const i=Os[o],l="&"+i;return n&&rv.includes(i)&&!ov.includes(i)&&(!r||t&&t!==61&&iv.test(String.fromCharCode(t)))?l:l+";"}return""}function sv(e,t,n){let r=ev(e,t,n.omitOptionalSemicolons),o;if((n.useNamedReferences||n.useShortestReferences)&&(o=lv(e,t,n.omitOptionalSemicolons,n.attribute)),(n.useShortestReferences||!o)&&n.useShortestReferences){const i=nv(e,t,n.omitOptionalSemicolons);i.length<r.length&&(r=i)}return o&&(!n.useShortestReferences||o.length<r.length)?o:r}function Mn(e,t){return Xy(e,Object.assign({format:sv},t))}const av=/^>|^->|<!--|-->|--!>|<!-$/g,uv=[">"],cv=["<",">"];function dv(e,t,n,r){return r.settings.bogusComments?"<?"+Mn(e.value,Object.assign({},r.settings.characterReferences,{subset:uv}))+">":"<!--"+e.value.replace(av,o)+"-->";function o(i){return Mn(i,Object.assign({},r.settings.characterReferences,{subset:cv}))}}function fv(e,t,n,r){return"<!"+(r.settings.upperDoctype?"DOCTYPE":"doctype")+(r.settings.tightDoctype?"":" ")+"html>"}function ac(e,t){const n=String(e);if(typeof t!="string")throw new TypeError("Expected character");let r=0,o=n.indexOf(t);for(;o!==-1;)r++,o=n.indexOf(t,o+t.length);return r}function pv(e,t){const n=t||{};return(e[e.length-1]===""?[...e,""]:e).join((n.padRight?" ":"")+","+(n.padLeft===!1?"":" ")).trim()}function mv(e){return e.join(" ").trim()}const hv=/[ \t\n\f\r]/g;function La(e){return typeof e=="object"?e.type==="text"?uc(e.value):!1:uc(e)}function uc(e){return e.replace(hv,"")===""}const oe=xp(1),kp=xp(-1),gv=[];function xp(e){return t;function t(n,r,o){const i=n?n.children:gv;let l=(r||0)+e,s=i[l];if(!o)for(;s&&La(s);)l+=e,s=i[l];return s}}const _v={}.hasOwnProperty;function Cp(e){return t;function t(n,r,o){return _v.call(e,n.tagName)&&e[n.tagName](n,r,o)}}const Oa=Cp({body:vv,caption:Cl,colgroup:Cl,dd:kv,dt:wv,head:Cl,html:yv,li:Sv,optgroup:xv,option:Cv,p:Ev,rp:cc,rt:cc,tbody:Pv,td:dc,tfoot:Tv,th:dc,thead:Rv,tr:Lv});function Cl(e,t,n){const r=oe(n,t,!0);return!r||r.type!=="comment"&&!(r.type==="text"&&La(r.value.charAt(0)))}function yv(e,t,n){const r=oe(n,t);return!r||r.type!=="comment"}function vv(e,t,n){const r=oe(n,t);return!r||r.type!=="comment"}function Ev(e,t,n){const r=oe(n,t);return r?r.type==="element"&&(r.tagName==="address"||r.tagName==="article"||r.tagName==="aside"||r.tagName==="blockquote"||r.tagName==="details"||r.tagName==="div"||r.tagName==="dl"||r.tagName==="fieldset"||r.tagName==="figcaption"||r.tagName==="figure"||r.tagName==="footer"||r.tagName==="form"||r.tagName==="h1"||r.tagName==="h2"||r.tagName==="h3"||r.tagName==="h4"||r.tagName==="h5"||r.tagName==="h6"||r.tagName==="header"||r.tagName==="hgroup"||r.tagName==="hr"||r.tagName==="main"||r.tagName==="menu"||r.tagName==="nav"||r.tagName==="ol"||r.tagName==="p"||r.tagName==="pre"||r.tagName==="section"||r.tagName==="table"||r.tagName==="ul"):!n||!(n.type==="element"&&(n.tagName==="a"||n.tagName==="audio"||n.tagName==="del"||n.tagName==="ins"||n.tagName==="map"||n.tagName==="noscript"||n.tagName==="video"))}function Sv(e,t,n){const r=oe(n,t);return!r||r.type==="element"&&r.tagName==="li"}function wv(e,t,n){const r=oe(n,t);return!!(r&&r.type==="element"&&(r.tagName==="dt"||r.tagName==="dd"))}function kv(e,t,n){const r=oe(n,t);return!r||r.type==="element"&&(r.tagName==="dt"||r.tagName==="dd")}function cc(e,t,n){const r=oe(n,t);return!r||r.type==="element"&&(r.tagName==="rp"||r.tagName==="rt")}function xv(e,t,n){const r=oe(n,t);return!r||r.type==="element"&&r.tagName==="optgroup"}function Cv(e,t,n){const r=oe(n,t);return!r||r.type==="element"&&(r.tagName==="option"||r.tagName==="optgroup")}function Rv(e,t,n){const r=oe(n,t);return!!(r&&r.type==="element"&&(r.tagName==="tbody"||r.tagName==="tfoot"))}function Pv(e,t,n){const r=oe(n,t);return!r||r.type==="element"&&(r.tagName==="tbody"||r.tagName==="tfoot")}function Tv(e,t,n){return!oe(n,t)}function Lv(e,t,n){const r=oe(n,t);return!r||r.type==="element"&&r.tagName==="tr"}function dc(e,t,n){const r=oe(n,t);return!r||r.type==="element"&&(r.tagName==="td"||r.tagName==="th")}const Ov=Cp({body:Nv,colgroup:Dv,head:Iv,html:Av,tbody:jv});function Av(e){const t=oe(e,-1);return!t||t.type!=="comment"}function Iv(e){const t=new Set;for(const r of e.children)if(r.type==="element"&&(r.tagName==="base"||r.tagName==="title")){if(t.has(r.tagName))return!1;t.add(r.tagName)}const n=e.children[0];return!n||n.type==="element"}function Nv(e){const t=oe(e,-1,!0);return!t||t.type!=="comment"&&!(t.type==="text"&&La(t.value.charAt(0)))&&!(t.type==="element"&&(t.tagName==="meta"||t.tagName==="link"||t.tagName==="script"||t.tagName==="style"||t.tagName==="template"))}function Dv(e,t,n){const r=kp(n,t),o=oe(e,-1,!0);return n&&r&&r.type==="element"&&r.tagName==="colgroup"&&Oa(r,n.children.indexOf(r),n)?!1:!!(o&&o.type==="element"&&o.tagName==="col")}function jv(e,t,n){const r=kp(n,t),o=oe(e,-1);return n&&r&&r.type==="element"&&(r.tagName==="thead"||r.tagName==="tbody")&&Oa(r,n.children.indexOf(r),n)?!1:!!(o&&o.type==="element"&&o.tagName==="tr")}const Co={name:[[`	
\f\r &/=>`.split(""),`	
\f\r "&'/=>\``.split("")],[`\0	
\f\r "&'/<=>`.split(""),`\0	
\f\r "&'/<=>\``.split("")]],unquoted:[[`	
\f\r &>`.split(""),`\0	
\f\r "&'<=>\``.split("")],[`\0	
\f\r "&'<=>\``.split(""),`\0	
\f\r "&'<=>\``.split("")]],single:[["&'".split(""),"\"&'`".split("")],["\0&'".split(""),"\0\"&'`".split("")]],double:[['"&'.split(""),"\"&'`".split("")],['\0"&'.split(""),"\0\"&'`".split("")]]};function Mv(e,t,n,r){const o=r.schema,i=o.space==="svg"?!1:r.settings.omitOptionalTags;let l=o.space==="svg"?r.settings.closeEmptyElements:r.settings.voids.includes(e.tagName.toLowerCase());const s=[];let a;o.space==="html"&&e.tagName==="svg"&&(r.schema=Sp);const u=bv(r,e.properties),d=r.all(o.space==="html"&&e.tagName==="template"?e.content:e);return r.schema=o,d&&(l=!1),(u||!i||!Ov(e,t,n))&&(s.push("<",e.tagName,u?" "+u:""),l&&(o.space==="svg"||r.settings.closeSelfClosing)&&(a=u.charAt(u.length-1),(!r.settings.tightSelfClosing||a==="/"||a&&a!=='"'&&a!=="'")&&s.push(" "),s.push("/")),s.push(">")),s.push(d),!l&&(!i||!Oa(e,t,n))&&s.push("</"+e.tagName+">"),s.join("")}function bv(e,t){const n=[];let r=-1,o;if(t){for(o in t)if(t[o]!==null&&t[o]!==void 0){const i=Vv(e,o,t[o]);i&&n.push(i)}}for(;++r<n.length;){const i=e.settings.tightAttributes?n[r].charAt(n[r].length-1):void 0;r!==n.length-1&&i!=='"'&&i!=="'"&&(n[r]+=" ")}return n.join("")}function Vv(e,t,n){const r=$y(e.schema,t),o=e.settings.allowParseErrors&&e.schema.space==="html"?0:1,i=e.settings.allowDangerousCharacters?0:1;let l=e.quote,s;if(r.overloadedBoolean&&(n===r.attribute||n==="")?n=!0:(r.boolean||r.overloadedBoolean)&&(typeof n!="string"||n===r.attribute||n==="")&&(n=!!n),n==null||n===!1||typeof n=="number"&&Number.isNaN(n))return"";const a=Mn(r.attribute,Object.assign({},e.settings.characterReferences,{subset:Co.name[o][i]}));return n===!0||(n=Array.isArray(n)?(r.commaSeparated?pv:mv)(n,{padLeft:!e.settings.tightCommaSeparatedLists}):String(n),e.settings.collapseEmptyAttributes&&!n)?a:(e.settings.preferUnquoted&&(s=Mn(n,Object.assign({},e.settings.characterReferences,{attribute:!0,subset:Co.unquoted[o][i]}))),s!==n&&(e.settings.quoteSmart&&ac(n,l)>ac(n,e.alternative)&&(l=e.alternative),s=l+Mn(n,Object.assign({},e.settings.characterReferences,{subset:(l==="'"?Co.single:Co.double)[o][i],attribute:!0}))+l),a+(s&&"="+s))}const zv=["<","&"];function Rp(e,t,n,r){return n&&n.type==="element"&&(n.tagName==="script"||n.tagName==="style")?e.value:Mn(e.value,Object.assign({},r.settings.characterReferences,{subset:zv}))}function Uv(e,t,n,r){return r.settings.allowDangerousHtml?e.value:Rp(e,t,n,r)}function $v(e,t,n,r){return r.all(e)}const Bv=Gy("type",{invalid:Fv,unknown:Hv,handlers:{comment:dv,doctype:fv,element:Mv,raw:Uv,root:$v,text:Rp}});function Fv(e){throw new Error("Expected node, not `"+e+"`")}function Hv(e){const t=e;throw new Error("Cannot compile unknown node `"+t.type+"`")}const Gv={},Wv={},Kv=[];function Qv(e,t){const n=Gv,r=n.quote||'"',o=r==='"'?"'":'"';if(r!=='"'&&r!=="'")throw new Error("Invalid quote `"+r+"`, expected `'` or `\"`");return{one:qv,all:Xv,settings:{omitOptionalTags:n.omitOptionalTags||!1,allowParseErrors:n.allowParseErrors||!1,allowDangerousCharacters:n.allowDangerousCharacters||!1,quoteSmart:n.quoteSmart||!1,preferUnquoted:n.preferUnquoted||!1,tightAttributes:n.tightAttributes||!1,upperDoctype:n.upperDoctype||!1,tightDoctype:n.tightDoctype||!1,bogusComments:n.bogusComments||!1,tightCommaSeparatedLists:n.tightCommaSeparatedLists||!1,tightSelfClosing:n.tightSelfClosing||!1,collapseEmptyAttributes:n.collapseEmptyAttributes||!1,allowDangerousHtml:n.allowDangerousHtml||!1,voids:n.voids||jy,characterReferences:n.characterReferences||Wv,closeSelfClosing:n.closeSelfClosing||!1,closeEmptyElements:n.closeEmptyElements||!1},schema:n.space==="svg"?Sp:Hy,quote:r,alternative:o}.one(Array.isArray(e)?{type:"root",children:e}:e,void 0,void 0)}function qv(e,t,n){return Bv(e,t,n,this)}function Xv(e){const t=[],n=e&&e.children||Kv;let r=-1;for(;++r<n.length;)t[r]=this.one(n[r],r,e);return t.join("")}function Yv(e){return Array.isArray(e)?e:[e]}function $i(e,t=!1){var i;const n=e.split(/(\r?\n)/g);let r=0;const o=[];for(let l=0;l<n.length;l+=2){const s=t?n[l]+(n[l+1]||""):n[l];o.push([s,r]),r+=n[l].length,r+=((i=n[l+1])==null?void 0:i.length)||0}return o}function Aa(e){return!e||["plaintext","txt","text","plain"].includes(e)}function Pp(e){return e==="ansi"||Aa(e)}function Ia(e){return e==="none"}function Tp(e){return Ia(e)}function Lp(e,t){var r;if(!t)return e;e.properties||(e.properties={}),(r=e.properties).class||(r.class=[]),typeof e.properties.class=="string"&&(e.properties.class=e.properties.class.split(/\s+/g)),Array.isArray(e.properties.class)||(e.properties.class=[]);const n=Array.isArray(t)?t:t.split(/\s+/g);for(const o of n)o&&!e.properties.class.includes(o)&&e.properties.class.push(o);return e}function Jv(e,t){let n=0;const r=[];for(const o of t)o>n&&r.push({...e,content:e.content.slice(n,o),offset:e.offset+n}),n=o;return n<e.content.length&&r.push({...e,content:e.content.slice(n),offset:e.offset+n}),r}function Zv(e,t){const n=Array.from(t instanceof Set?t:new Set(t)).sort((r,o)=>r-o);return n.length?e.map(r=>r.flatMap(o=>{const i=n.filter(l=>o.offset<l&&l<o.offset+o.content.length).map(l=>l-o.offset).sort((l,s)=>l-s);return i.length?Jv(o,i):o})):e}async function Op(e){return Promise.resolve(typeof e=="function"?e():e).then(t=>t.default||t)}function _i(e,t){const n=typeof e=="string"?{}:{...e.colorReplacements},r=typeof e=="string"?e:e.name;for(const[o,i]of Object.entries((t==null?void 0:t.colorReplacements)||{}))typeof i=="string"?n[o]=i:o===r&&Object.assign(n,i);return n}function en(e,t){return e&&((t==null?void 0:t[e==null?void 0:e.toLowerCase()])||e)}function Ap(e){const t={};return e.color&&(t.color=e.color),e.bgColor&&(t["background-color"]=e.bgColor),e.fontStyle&&(e.fontStyle&ft.Italic&&(t["font-style"]="italic"),e.fontStyle&ft.Bold&&(t["font-weight"]="bold"),e.fontStyle&ft.Underline&&(t["text-decoration"]="underline")),t}function e0(e){return typeof e=="string"?e:Object.entries(e).map(([t,n])=>`${t}:${n}`).join(";")}function t0(e){const t=$i(e,!0).map(([o])=>o);function n(o){if(o===e.length)return{line:t.length-1,character:t[t.length-1].length};let i=o,l=0;for(const s of t){if(i<s.length)break;i-=s.length,l++}return{line:l,character:i}}function r(o,i){let l=0;for(let s=0;s<o;s++)l+=t[s].length;return l+=i,l}return{lines:t,indexToPos:n,posToIndex:r}}class Ee extends Error{constructor(t){super(t),this.name="ShikiError"}}const Ip=new WeakMap;function Bi(e,t){Ip.set(e,t)}function Fr(e){return Ip.get(e)}class Yn{constructor(...t){k(this,"_stacks",{});k(this,"lang");if(t.length===2){const[n,r]=t;this.lang=r,this._stacks=n}else{const[n,r,o]=t;this.lang=r,this._stacks={[o]:n}}}get themes(){return Object.keys(this._stacks)}get theme(){return this.themes[0]}get _stack(){return this._stacks[this.theme]}static initial(t,n){return new Yn(Object.fromEntries(Yv(n).map(r=>[r,Rs])),t)}getInternalStack(t=this.theme){return this._stacks[t]}get scopes(){return fc(this._stacks[this.theme])}getScopes(t=this.theme){return fc(this._stacks[t])}toJSON(){return{lang:this.lang,theme:this.theme,themes:this.themes,scopes:this.scopes}}}function fc(e){const t=[],n=new Set;function r(o){var l;if(n.has(o))return;n.add(o);const i=(l=o==null?void 0:o.nameScopesList)==null?void 0:l.scopeName;i&&t.push(i),o.parent&&r(o.parent)}return r(e),t}function n0(e,t){if(!(e instanceof Yn))throw new Ee("Invalid grammar state");return e.getInternalStack(t)}function r0(){const e=new WeakMap;function t(n){if(!e.has(n.meta)){let r=function(l){if(typeof l=="number"){if(l<0||l>n.source.length)throw new Ee(`Invalid decoration offset: ${l}. Code length: ${n.source.length}`);return{...o.indexToPos(l),offset:l}}else{const s=o.lines[l.line];if(s===void 0)throw new Ee(`Invalid decoration position ${JSON.stringify(l)}. Lines length: ${o.lines.length}`);if(l.character<0||l.character>s.length)throw new Ee(`Invalid decoration position ${JSON.stringify(l)}. Line ${l.line} length: ${s.length}`);return{...l,offset:o.posToIndex(l.line,l.character)}}};const o=t0(n.source),i=(n.options.decorations||[]).map(l=>({...l,start:r(l.start),end:r(l.end)}));o0(i),e.set(n.meta,{decorations:i,converter:o,source:n.source})}return e.get(n.meta)}return{name:"shiki:decorations",tokens(n){var l;if(!((l=this.options.decorations)!=null&&l.length))return;const o=t(this).decorations.flatMap(s=>[s.start.offset,s.end.offset]);return Zv(n,o)},code(n){var d;if(!((d=this.options.decorations)!=null&&d.length))return;const r=t(this),o=Array.from(n.children).filter(f=>f.type==="element"&&f.tagName==="span");if(o.length!==r.converter.lines.length)throw new Ee(`Number of lines in code element (${o.length}) does not match the number of lines in the source (${r.converter.lines.length}). Failed to apply decorations.`);function i(f,m,g,_){const E=o[f];let S="",p=-1,c=-1;if(m===0&&(p=0),g===0&&(c=0),g===Number.POSITIVE_INFINITY&&(c=E.children.length),p===-1||c===-1)for(let w=0;w<E.children.length;w++)S+=Np(E.children[w]),p===-1&&S.length===m&&(p=w+1),c===-1&&S.length===g&&(c=w+1);if(p===-1)throw new Ee(`Failed to find start index for decoration ${JSON.stringify(_.start)}`);if(c===-1)throw new Ee(`Failed to find end index for decoration ${JSON.stringify(_.end)}`);const y=E.children.slice(p,c);if(!_.alwaysWrap&&y.length===E.children.length)s(E,_,"line");else if(!_.alwaysWrap&&y.length===1&&y[0].type==="element")s(y[0],_,"token");else{const w={type:"element",tagName:"span",properties:{},children:y};s(w,_,"wrapper"),E.children.splice(p,y.length,w)}}function l(f,m){o[f]=s(o[f],m,"line")}function s(f,m,g){var S;const _=m.properties||{},E=m.transform||(p=>p);return f.tagName=m.tagName||"span",f.properties={...f.properties,..._,class:f.properties.class},(S=m.properties)!=null&&S.class&&Lp(f,m.properties.class),f=E(f,g)||f,f}const a=[],u=r.decorations.sort((f,m)=>m.start.offset-f.start.offset);for(const f of u){const{start:m,end:g}=f;if(m.line===g.line)i(m.line,m.character,g.character,f);else if(m.line<g.line){i(m.line,m.character,Number.POSITIVE_INFINITY,f);for(let _=m.line+1;_<g.line;_++)a.unshift(()=>l(_,f));i(g.line,0,g.character,f)}}a.forEach(f=>f())}}}function o0(e){for(let t=0;t<e.length;t++){const n=e[t];if(n.start.offset>n.end.offset)throw new Ee(`Invalid decoration range: ${JSON.stringify(n.start)} - ${JSON.stringify(n.end)}`);for(let r=t+1;r<e.length;r++){const o=e[r],i=n.start.offset<o.start.offset&&o.start.offset<n.end.offset,l=n.start.offset<o.end.offset&&o.end.offset<n.end.offset,s=o.start.offset<n.start.offset&&n.start.offset<o.end.offset,a=o.start.offset<n.end.offset&&n.end.offset<o.end.offset;if(i||l||s||a){if(l&&l||s&&a)continue;throw new Ee(`Decorations ${JSON.stringify(n.start)} and ${JSON.stringify(o.start)} intersect.`)}}}}function Np(e){return e.type==="text"?e.value:e.type==="element"?e.children.map(Np).join(""):""}const i0=[r0()];function yi(e){return[...e.transformers||[],...i0]}var tn=["black","red","green","yellow","blue","magenta","cyan","white","brightBlack","brightRed","brightGreen","brightYellow","brightBlue","brightMagenta","brightCyan","brightWhite"],Rl={1:"bold",2:"dim",3:"italic",4:"underline",7:"reverse",9:"strikethrough"};function l0(e,t){const n=e.indexOf("\x1B[",t);if(n!==-1){const r=e.indexOf("m",n);return{sequence:e.substring(n+2,r).split(";"),startPosition:n,position:r+1}}return{position:e.length}}function pc(e,t){let n=1;const r=e[t+n++];let o;if(r==="2"){const i=[e[t+n++],e[t+n++],e[t+n]].map(l=>Number.parseInt(l));i.length===3&&!i.some(l=>Number.isNaN(l))&&(o={type:"rgb",rgb:i})}else if(r==="5"){const i=Number.parseInt(e[t+n]);Number.isNaN(i)||(o={type:"table",index:Number(i)})}return[n,o]}function s0(e){const t=[];for(let n=0;n<e.length;n++){const r=e[n],o=Number.parseInt(r);if(!Number.isNaN(o))if(o===0)t.push({type:"resetAll"});else if(o<=9)Rl[o]&&t.push({type:"setDecoration",value:Rl[o]});else if(o<=29){const i=Rl[o-20];i&&t.push({type:"resetDecoration",value:i})}else if(o<=37)t.push({type:"setForegroundColor",value:{type:"named",name:tn[o-30]}});else if(o===38){const[i,l]=pc(e,n);l&&t.push({type:"setForegroundColor",value:l}),n+=i}else if(o===39)t.push({type:"resetForegroundColor"});else if(o<=47)t.push({type:"setBackgroundColor",value:{type:"named",name:tn[o-40]}});else if(o===48){const[i,l]=pc(e,n);l&&t.push({type:"setBackgroundColor",value:l}),n+=i}else o===49?t.push({type:"resetBackgroundColor"}):o>=90&&o<=97?t.push({type:"setForegroundColor",value:{type:"named",name:tn[o-90+8]}}):o>=100&&o<=107&&t.push({type:"setBackgroundColor",value:{type:"named",name:tn[o-100+8]}})}return t}function a0(){let e=null,t=null,n=new Set;return{parse(r){const o=[];let i=0;do{const l=l0(r,i),s=l.sequence?r.substring(i,l.startPosition):r.substring(i);if(s.length>0&&o.push({value:s,foreground:e,background:t,decorations:new Set(n)}),l.sequence){const a=s0(l.sequence);for(const u of a)u.type==="resetAll"?(e=null,t=null,n.clear()):u.type==="resetForegroundColor"?e=null:u.type==="resetBackgroundColor"?t=null:u.type==="resetDecoration"&&n.delete(u.value);for(const u of a)u.type==="setForegroundColor"?e=u.value:u.type==="setBackgroundColor"?t=u.value:u.type==="setDecoration"&&n.add(u.value)}i=l.position}while(i<r.length);return o}}}var u0={black:"#000000",red:"#bb0000",green:"#00bb00",yellow:"#bbbb00",blue:"#0000bb",magenta:"#ff00ff",cyan:"#00bbbb",white:"#eeeeee",brightBlack:"#555555",brightRed:"#ff5555",brightGreen:"#00ff00",brightYellow:"#ffff55",brightBlue:"#5555ff",brightMagenta:"#ff55ff",brightCyan:"#55ffff",brightWhite:"#ffffff"};function c0(e=u0){function t(s){return e[s]}function n(s){return`#${s.map(a=>Math.max(0,Math.min(a,255)).toString(16).padStart(2,"0")).join("")}`}let r;function o(){if(r)return r;r=[];for(let u=0;u<tn.length;u++)r.push(t(tn[u]));let s=[0,95,135,175,215,255];for(let u=0;u<6;u++)for(let d=0;d<6;d++)for(let f=0;f<6;f++)r.push(n([s[u],s[d],s[f]]));let a=8;for(let u=0;u<24;u++,a+=10)r.push(n([a,a,a]));return r}function i(s){return o()[s]}function l(s){switch(s.type){case"named":return t(s.name);case"rgb":return n(s.rgb);case"table":return i(s.index)}}return{value:l}}function d0(e,t,n){const r=_i(e,n),o=$i(t),i=c0(Object.fromEntries(tn.map(s=>{var a;return[s,(a=e.colors)==null?void 0:a[`terminal.ansi${s[0].toUpperCase()}${s.substring(1)}`]]}))),l=a0();return o.map(s=>l.parse(s[0]).map(a=>{let u,d;a.decorations.has("reverse")?(u=a.background?i.value(a.background):e.bg,d=a.foreground?i.value(a.foreground):e.fg):(u=a.foreground?i.value(a.foreground):e.fg,d=a.background?i.value(a.background):void 0),u=en(u,r),d=en(d,r),a.decorations.has("dim")&&(u=f0(u));let f=ft.None;return a.decorations.has("bold")&&(f|=ft.Bold),a.decorations.has("italic")&&(f|=ft.Italic),a.decorations.has("underline")&&(f|=ft.Underline),{content:a.value,offset:s[1],color:u,bgColor:d,fontStyle:f}}))}function f0(e){const t=e.match(/#([0-9a-f]{3})([0-9a-f]{3})?([0-9a-f]{2})?/);if(t)if(t[3]){const r=Math.round(Number.parseInt(t[3],16)/2).toString(16).padStart(2,"0");return`#${t[1]}${t[2]}${r}`}else return t[2]?`#${t[1]}${t[2]}80`:`#${Array.from(t[1]).map(r=>`${r}${r}`).join("")}80`;const n=e.match(/var\((--[\w-]+-ansi-[\w-]+)\)/);return n?`var(${n[1]}-dim)`:e}function Na(e,t,n={}){const{lang:r="text",theme:o=e.getLoadedThemes()[0]}=n;if(Aa(r)||Ia(o))return $i(t).map(a=>[{content:a[0],offset:a[1]}]);const{theme:i,colorMap:l}=e.setTheme(o);if(r==="ansi")return d0(i,t,n);const s=e.getLanguage(r);if(n.grammarState){if(n.grammarState.lang!==s.name)throw new ht(`Grammar state language "${n.grammarState.lang}" does not match highlight language "${s.name}"`);if(!n.grammarState.themes.includes(i.name))throw new ht(`Grammar state themes "${n.grammarState.themes}" do not contain highlight theme "${i.name}"`)}return m0(t,s,i,l,n)}function p0(...e){if(e.length===2)return Fr(e[1]);const[t,n,r={}]=e,{lang:o="text",theme:i=t.getLoadedThemes()[0]}=r;if(Aa(o)||Ia(i))throw new ht("Plain language does not have grammar state");if(o==="ansi")throw new ht("ANSI language does not have grammar state");const{theme:l,colorMap:s}=t.setTheme(i),a=t.getLanguage(o);return new Yn(vi(n,a,l,s,r).stateStack,a.name,l.name)}function m0(e,t,n,r,o){const i=vi(e,t,n,r,o),l=new Yn(vi(e,t,n,r,o).stateStack,t.name,n.name);return Bi(i.tokens,l),i.tokens}function vi(e,t,n,r,o){const i=_i(n,o),{tokenizeMaxLineLength:l=0,tokenizeTimeLimit:s=500}=o,a=$i(e);let u=o.grammarState?n0(o.grammarState,n.name)??Rs:o.grammarContextCode!=null?vi(o.grammarContextCode,t,n,r,{...o,grammarState:void 0,grammarContextCode:void 0}).stateStack:Rs,d=[];const f=[];for(let m=0,g=a.length;m<g;m++){const[_,E]=a[m];if(_===""){d=[],f.push([]);continue}if(l>0&&_.length>=l){d=[],f.push([{content:_,offset:E,color:"",fontStyle:0}]);continue}let S,p,c;o.includeExplanation&&(S=t.tokenizeLine(_,u),p=S.tokens,c=0);const y=t.tokenizeLine2(_,u,s),w=y.tokens.length/2;for(let x=0;x<w;x++){const T=y.tokens[2*x],P=x+1<w?y.tokens[2*x+2]:_.length;if(T===P)continue;const A=y.tokens[2*x+1],$=en(r[Gn.getForeground(A)],i),N=Gn.getFontStyle(A),ye={content:_.substring(T,P),offset:E+T,color:$,fontStyle:N};if(o.includeExplanation){const St=[];if(o.includeExplanation!=="scopeName")for(const Ve of n.settings){let kt;switch(typeof Ve.scope){case"string":kt=Ve.scope.split(/,/).map(Wt=>Wt.trim());break;case"object":kt=Ve.scope;break;default:continue}St.push({settings:Ve,selectors:kt.map(Wt=>Wt.split(/ /))})}ye.explanation=[];let wt=0;for(;T+wt<P;){const Ve=p[c],kt=_.substring(Ve.startIndex,Ve.endIndex);wt+=kt.length,ye.explanation.push({content:kt,scopes:o.includeExplanation==="scopeName"?h0(Ve.scopes):g0(St,Ve.scopes)}),c+=1}}d.push(ye)}f.push(d),d=[],u=y.ruleStack}return{tokens:f,stateStack:u}}function h0(e){return e.map(t=>({scopeName:t}))}function g0(e,t){const n=[];for(let r=0,o=t.length;r<o;r++){const i=t[r];n[r]={scopeName:i,themeMatches:y0(e,i,t.slice(0,r))}}return n}function mc(e,t){return e===t||t.substring(0,e.length)===e&&t[e.length]==="."}function _0(e,t,n){if(!mc(e[e.length-1],t))return!1;let r=e.length-2,o=n.length-1;for(;r>=0&&o>=0;)mc(e[r],n[o])&&(r-=1),o-=1;return r===-1}function y0(e,t,n){const r=[];for(const{selectors:o,settings:i}of e)for(const l of o)if(_0(l,t,n)){r.push(i);break}return r}function Dp(e,t,n){const r=Object.entries(n.themes).filter(a=>a[1]).map(a=>({color:a[0],theme:a[1]})),o=r.map(a=>{const u=Na(e,t,{...n,theme:a.theme}),d=Fr(u),f=typeof a.theme=="string"?a.theme:a.theme.name;return{tokens:u,state:d,theme:f}}),i=v0(...o.map(a=>a.tokens)),l=i[0].map((a,u)=>a.map((d,f)=>{const m={content:d.content,variants:{},offset:d.offset};return"includeExplanation"in n&&n.includeExplanation&&(m.explanation=d.explanation),i.forEach((g,_)=>{const{content:E,explanation:S,offset:p,...c}=g[u][f];m.variants[r[_].color]=c}),m})),s=o[0].state?new Yn(Object.fromEntries(o.map(a=>{var u;return[a.theme,(u=a.state)==null?void 0:u.getInternalStack(a.theme)]})),o[0].state.lang):void 0;return s&&Bi(l,s),l}function v0(...e){const t=e.map(()=>[]),n=e.length;for(let r=0;r<e[0].length;r++){const o=e.map(a=>a[r]),i=t.map(()=>[]);t.forEach((a,u)=>a.push(i[u]));const l=o.map(()=>0),s=o.map(a=>a[0]);for(;s.every(a=>a);){const a=Math.min(...s.map(u=>u.content.length));for(let u=0;u<n;u++){const d=s[u];d.content.length===a?(i[u].push(d),l[u]+=1,s[u]=o[u][l[u]]):(i[u].push({...d,content:d.content.slice(0,a)}),s[u]={...d,content:d.content.slice(a),offset:d.offset+a})}}}return t}function Ei(e,t,n){let r,o,i,l,s,a;if("themes"in n){const{defaultColor:u="light",cssVariablePrefix:d="--shiki-"}=n,f=Object.entries(n.themes).filter(S=>S[1]).map(S=>({color:S[0],theme:S[1]})).sort((S,p)=>S.color===u?-1:p.color===u?1:0);if(f.length===0)throw new ht("`themes` option must not be empty");const m=Dp(e,t,n);if(a=Fr(m),u&&!f.find(S=>S.color===u))throw new ht(`\`themes\` option must contain the defaultColor key \`${u}\``);const g=f.map(S=>e.getTheme(S.theme)),_=f.map(S=>S.color);i=m.map(S=>S.map(p=>E0(p,_,d,u))),a&&Bi(i,a);const E=f.map(S=>_i(S.theme,n));o=f.map((S,p)=>(p===0&&u?"":`${d+S.color}:`)+(en(g[p].fg,E[p])||"inherit")).join(";"),r=f.map((S,p)=>(p===0&&u?"":`${d+S.color}-bg:`)+(en(g[p].bg,E[p])||"inherit")).join(";"),l=`shiki-themes ${g.map(S=>S.name).join(" ")}`,s=u?void 0:[o,r].join(";")}else if("theme"in n){const u=_i(n.theme,n);i=Na(e,t,n);const d=e.getTheme(n.theme);r=en(d.bg,u),o=en(d.fg,u),l=d.name,a=Fr(i)}else throw new ht("Invalid options, either `theme` or `themes` must be provided");return{tokens:i,fg:o,bg:r,themeName:l,rootStyle:s,grammarState:a}}function E0(e,t,n,r){const o={content:e.content,explanation:e.explanation,offset:e.offset},i=t.map(a=>Ap(e.variants[a])),l=new Set(i.flatMap(a=>Object.keys(a))),s={};return i.forEach((a,u)=>{for(const d of l){const f=a[d]||"inherit";if(u===0&&r)s[d]=f;else{const m=d==="color"?"":d==="background-color"?"-bg":`-${d}`,g=n+t[u]+(d==="color"?"":m);s[g]=f}}}),o.htmlStyle=s,o}function Si(e,t,n,r={meta:{},options:n,codeToHast:(o,i)=>Si(e,o,i),codeToTokens:(o,i)=>Ei(e,o,i)}){var g,_;let o=t;for(const E of yi(n))o=((g=E.preprocess)==null?void 0:g.call(r,o,n))||o;let{tokens:i,fg:l,bg:s,themeName:a,rootStyle:u,grammarState:d}=Ei(e,o,n);const{mergeWhitespaces:f=!0}=n;f===!0?i=w0(i):f==="never"&&(i=k0(i));const m={...r,get source(){return o}};for(const E of yi(n))i=((_=E.tokens)==null?void 0:_.call(m,i))||i;return S0(i,{...n,fg:l,bg:s,themeName:a,rootStyle:u},m,d)}function S0(e,t,n,r=Fr(e)){var _,E,S;const o=yi(t),i=[],l={type:"root",children:[]},{structure:s="classic",tabindex:a="0"}=t;let u={type:"element",tagName:"pre",properties:{class:`shiki ${t.themeName||""}`,style:t.rootStyle||`background-color:${t.bg};color:${t.fg}`,...a!==!1&&a!=null?{tabindex:a.toString()}:{},...Object.fromEntries(Array.from(Object.entries(t.meta||{})).filter(([p])=>!p.startsWith("_")))},children:[]},d={type:"element",tagName:"code",properties:{},children:i};const f=[],m={...n,structure:s,addClassToHast:Lp,get source(){return n.source},get tokens(){return e},get options(){return t},get root(){return l},get pre(){return u},get code(){return d},get lines(){return f}};if(e.forEach((p,c)=>{var x,T;c&&(s==="inline"?l.children.push({type:"element",tagName:"br",properties:{},children:[]}):s==="classic"&&i.push({type:"text",value:`
`}));let y={type:"element",tagName:"span",properties:{class:"line"},children:[]},w=0;for(const P of p){let A={type:"element",tagName:"span",properties:{...P.htmlAttrs},children:[{type:"text",value:P.content}]};P.htmlStyle;const $=e0(P.htmlStyle||Ap(P));$&&(A.properties.style=$);for(const N of o)A=((x=N==null?void 0:N.span)==null?void 0:x.call(m,A,c+1,w,y,P))||A;s==="inline"?l.children.push(A):s==="classic"&&y.children.push(A),w+=P.content.length}if(s==="classic"){for(const P of o)y=((T=P==null?void 0:P.line)==null?void 0:T.call(m,y,c+1))||y;f.push(y),i.push(y)}}),s==="classic"){for(const p of o)d=((_=p==null?void 0:p.code)==null?void 0:_.call(m,d))||d;u.children.push(d);for(const p of o)u=((E=p==null?void 0:p.pre)==null?void 0:E.call(m,u))||u;l.children.push(u)}let g=l;for(const p of o)g=((S=p==null?void 0:p.root)==null?void 0:S.call(m,g))||g;return r&&Bi(g,r),g}function w0(e){return e.map(t=>{const n=[];let r="",o=0;return t.forEach((i,l)=>{const a=!(i.fontStyle&&i.fontStyle&ft.Underline);a&&i.content.match(/^\s+$/)&&t[l+1]?(o||(o=i.offset),r+=i.content):r?(a?n.push({...i,offset:o,content:r+i.content}):n.push({content:r,offset:o},i),o=0,r=""):n.push(i)}),n})}function k0(e){return e.map(t=>t.flatMap(n=>{if(n.content.match(/^\s+$/))return n;const r=n.content.match(/^(\s*)(.*?)(\s*)$/);if(!r)return n;const[,o,i,l]=r;if(!o&&!l)return n;const s=[{...n,offset:n.offset+o.length,content:i}];return o&&s.unshift({content:o,offset:n.offset}),l&&s.push({content:l,offset:n.offset+o.length+i.length}),s}))}function x0(e,t,n){var i;const r={meta:{},options:n,codeToHast:(l,s)=>Si(e,l,s),codeToTokens:(l,s)=>Ei(e,l,s)};let o=Qv(Si(e,t,n,r));for(const l of yi(n))o=((i=l.postprocess)==null?void 0:i.call(r,o,n))||o;return o}const hc={light:"#333333",dark:"#bbbbbb"},gc={light:"#fffffe",dark:"#1e1e1e"},_c="__shiki_resolved";function Da(e){var s,a,u,d,f;if(e!=null&&e[_c])return e;const t={...e};t.tokenColors&&!t.settings&&(t.settings=t.tokenColors,delete t.tokenColors),t.type||(t.type="dark"),t.colorReplacements={...t.colorReplacements},t.settings||(t.settings=[]);let{bg:n,fg:r}=t;if(!n||!r){const m=t.settings?t.settings.find(g=>!g.name&&!g.scope):void 0;(s=m==null?void 0:m.settings)!=null&&s.foreground&&(r=m.settings.foreground),(a=m==null?void 0:m.settings)!=null&&a.background&&(n=m.settings.background),!r&&((u=t==null?void 0:t.colors)!=null&&u["editor.foreground"])&&(r=t.colors["editor.foreground"]),!n&&((d=t==null?void 0:t.colors)!=null&&d["editor.background"])&&(n=t.colors["editor.background"]),r||(r=t.type==="light"?hc.light:hc.dark),n||(n=t.type==="light"?gc.light:gc.dark),t.fg=r,t.bg=n}t.settings[0]&&t.settings[0].settings&&!t.settings[0].scope||t.settings.unshift({settings:{foreground:t.fg,background:t.bg}});let o=0;const i=new Map;function l(m){var _;if(i.has(m))return i.get(m);o+=1;const g=`#${o.toString(16).padStart(8,"0").toLowerCase()}`;return(_=t.colorReplacements)!=null&&_[`#${g}`]?l(m):(i.set(m,g),g)}t.settings=t.settings.map(m=>{var S,p;const g=((S=m.settings)==null?void 0:S.foreground)&&!m.settings.foreground.startsWith("#"),_=((p=m.settings)==null?void 0:p.background)&&!m.settings.background.startsWith("#");if(!g&&!_)return m;const E={...m,settings:{...m.settings}};if(g){const c=l(m.settings.foreground);t.colorReplacements[c]=m.settings.foreground,E.settings.foreground=c}if(_){const c=l(m.settings.background);t.colorReplacements[c]=m.settings.background,E.settings.background=c}return E});for(const m of Object.keys(t.colors||{}))if((m==="editor.foreground"||m==="editor.background"||m.startsWith("terminal.ansi"))&&!((f=t.colors[m])!=null&&f.startsWith("#"))){const g=l(t.colors[m]);t.colorReplacements[g]=t.colors[m],t.colors[m]=g}return Object.defineProperty(t,_c,{enumerable:!1,writable:!1,value:!0}),t}async function jp(e){return Array.from(new Set((await Promise.all(e.filter(t=>!Pp(t)).map(async t=>await Op(t).then(n=>Array.isArray(n)?n:[n])))).flat()))}async function Mp(e){return(await Promise.all(e.map(async n=>Tp(n)?null:Da(await Op(n))))).filter(n=>!!n)}class C0 extends Dy{constructor(n,r,o,i={}){super(n);k(this,"_resolvedThemes",new Map);k(this,"_resolvedGrammars",new Map);k(this,"_langMap",new Map);k(this,"_langGraph",new Map);k(this,"_textmateThemeCache",new WeakMap);k(this,"_loadedThemesCache",null);k(this,"_loadedLanguagesCache",null);this._resolver=n,this._themes=r,this._langs=o,this._alias=i,this._themes.map(l=>this.loadTheme(l)),this.loadLanguages(this._langs)}getTheme(n){return typeof n=="string"?this._resolvedThemes.get(n):this.loadTheme(n)}loadTheme(n){const r=Da(n);return r.name&&(this._resolvedThemes.set(r.name,r),this._loadedThemesCache=null),r}getLoadedThemes(){return this._loadedThemesCache||(this._loadedThemesCache=[...this._resolvedThemes.keys()]),this._loadedThemesCache}setTheme(n){let r=this._textmateThemeCache.get(n);r||(r=fi.createFromRawTheme(n),this._textmateThemeCache.set(n,r)),this._syncRegistry.setTheme(r)}getGrammar(n){if(this._alias[n]){const r=new Set([n]);for(;this._alias[n];){if(n=this._alias[n],r.has(n))throw new Ee(`Circular alias \`${Array.from(r).join(" -> ")} -> ${n}\``);r.add(n)}}return this._resolvedGrammars.get(n)}loadLanguage(n){var l,s,a,u;if(this.getGrammar(n.name))return;const r=new Set([...this._langMap.values()].filter(d=>{var f;return(f=d.embeddedLangsLazy)==null?void 0:f.includes(n.name)}));this._resolver.addLanguage(n);const o={balancedBracketSelectors:n.balancedBracketSelectors||["*"],unbalancedBracketSelectors:n.unbalancedBracketSelectors||[]};this._syncRegistry._rawGrammars.set(n.scopeName,n);const i=this.loadGrammarWithConfiguration(n.scopeName,1,o);if(i.name=n.name,this._resolvedGrammars.set(n.name,i),n.aliases&&n.aliases.forEach(d=>{this._alias[d]=n.name}),this._loadedLanguagesCache=null,r.size)for(const d of r)this._resolvedGrammars.delete(d.name),this._loadedLanguagesCache=null,(s=(l=this._syncRegistry)==null?void 0:l._injectionGrammars)==null||s.delete(d.scopeName),(u=(a=this._syncRegistry)==null?void 0:a._grammars)==null||u.delete(d.scopeName),this.loadLanguage(this._langMap.get(d.name))}dispose(){super.dispose(),this._resolvedThemes.clear(),this._resolvedGrammars.clear(),this._langMap.clear(),this._langGraph.clear(),this._loadedThemesCache=null}loadLanguages(n){for(const i of n)this.resolveEmbeddedLanguages(i);const r=Array.from(this._langGraph.entries()),o=r.filter(([i,l])=>!l);if(o.length){const i=r.filter(([l,s])=>{var a;return s&&((a=s.embeddedLangs)==null?void 0:a.some(u=>o.map(([d])=>d).includes(u)))}).filter(l=>!o.includes(l));throw new Ee(`Missing languages ${o.map(([l])=>`\`${l}\``).join(", ")}, required by ${i.map(([l])=>`\`${l}\``).join(", ")}`)}for(const[i,l]of r)this._resolver.addLanguage(l);for(const[i,l]of r)this.loadLanguage(l)}getLoadedLanguages(){return this._loadedLanguagesCache||(this._loadedLanguagesCache=[...new Set([...this._resolvedGrammars.keys(),...Object.keys(this._alias)])]),this._loadedLanguagesCache}resolveEmbeddedLanguages(n){if(this._langMap.set(n.name,n),this._langGraph.set(n.name,n),n.embeddedLangs)for(const r of n.embeddedLangs)this._langGraph.set(r,this._langMap.get(r))}}class R0{constructor(t,n){k(this,"_langs",new Map);k(this,"_scopeToLang",new Map);k(this,"_injections",new Map);k(this,"_onigLib");this._onigLib={createOnigScanner:r=>t.createScanner(r),createOnigString:r=>t.createString(r)},n.forEach(r=>this.addLanguage(r))}get onigLib(){return this._onigLib}getLangRegistration(t){return this._langs.get(t)}loadGrammar(t){return this._scopeToLang.get(t)}addLanguage(t){this._langs.set(t.name,t),t.aliases&&t.aliases.forEach(n=>{this._langs.set(n,t)}),this._scopeToLang.set(t.scopeName,t),t.injectTo&&t.injectTo.forEach(n=>{this._injections.get(n)||this._injections.set(n,[]),this._injections.get(n).push(t.scopeName)})}getInjections(t){const n=t.split(".");let r=[];for(let o=1;o<=n.length;o++){const i=n.slice(0,o).join(".");r=[...r,...this._injections.get(i)||[]]}return r}}let lr=0;function P0(e){lr+=1,e.warnings!==!1&&lr>=10&&lr%10===0&&console.warn(`[Shiki] ${lr} instances have been created. Shiki is supposed to be used as a singleton, consider refactoring your code to cache your highlighter instance; Or call \`highlighter.dispose()\` to release unused instances.`);let t=!1;if(!e.engine)throw new Ee("`engine` option is required for synchronous mode");const n=(e.langs||[]).flat(1),r=(e.themes||[]).flat(1).map(Da),o=new R0(e.engine,n),i=new C0(o,r,n,e.langAlias);let l;function s(c){S();const y=i.getGrammar(typeof c=="string"?c:c.name);if(!y)throw new Ee(`Language \`${c}\` not found, you may need to load it first`);return y}function a(c){if(c==="none")return{bg:"",fg:"",name:"none",settings:[],type:"dark"};S();const y=i.getTheme(c);if(!y)throw new Ee(`Theme \`${c}\` not found, you may need to load it first`);return y}function u(c){S();const y=a(c);l!==c&&(i.setTheme(y),l=c);const w=i.getColorMap();return{theme:y,colorMap:w}}function d(){return S(),i.getLoadedThemes()}function f(){return S(),i.getLoadedLanguages()}function m(...c){S(),i.loadLanguages(c.flat(1))}async function g(...c){return m(await jp(c))}function _(...c){S();for(const y of c.flat(1))i.loadTheme(y)}async function E(...c){return S(),_(await Mp(c))}function S(){if(t)throw new Ee("Shiki instance has been disposed")}function p(){t||(t=!0,i.dispose(),lr-=1)}return{setTheme:u,getTheme:a,getLanguage:s,getLoadedThemes:d,getLoadedLanguages:f,loadLanguage:g,loadLanguageSync:m,loadTheme:E,loadThemeSync:_,dispose:p,[Symbol.dispose]:p}}async function T0(e={}){e.loadWasm;const[t,n,r]=await Promise.all([Mp(e.themes||[]),jp(e.langs||[]),e.engine||Jf(e.loadWasm||Q_())]);return P0({...e,themes:t,langs:n,engine:r})}async function L0(e={}){const t=await T0(e);return{getLastGrammarState:(...n)=>p0(t,...n),codeToTokensBase:(n,r)=>Na(t,n,r),codeToTokensWithThemes:(n,r)=>Dp(t,n,r),codeToTokens:(n,r)=>Ei(t,n,r),codeToHast:(n,r)=>Si(t,n,r),codeToHtml:(n,r)=>x0(t,n,r),...t,getInternalContext:()=>t}}function O0(e,t,n){let r,o,i;{const s=e;r=s.langs,o=s.themes,i=s.engine}async function l(s){function a(g){if(typeof g=="string"){if(Pp(g))return[];const _=r[g];if(!_)throw new ht(`Language \`${g}\` is not included in this bundle. You may want to load it from external source.`);return _}return g}function u(g){if(Tp(g))return"none";if(typeof g=="string"){const _=o[g];if(!_)throw new ht(`Theme \`${g}\` is not included in this bundle. You may want to load it from external source.`);return _}return g}const d=(s.themes??[]).map(g=>u(g)),f=(s.langs??[]).map(g=>a(g)),m=await L0({engine:s.engine??i(),...s,themes:d,langs:f});return{...m,loadLanguage(...g){return m.loadLanguage(...g.map(a))},loadTheme(...g){return m.loadTheme(...g.map(u))}}}return l}const A0=O0({langs:T_,themes:O_,engine:()=>Jf(h(()=>import("./wasm-CG6Dc4jp.js"),[],import.meta.url))}),I0=e=>A0(e);let Ro=null;const N0=["javascript","typescript","tsx","html","css","json","xml","markdown","yaml","scss","less","python","swift","rust","go","java","php","ruby","shell","shellscript"];async function D0(){return Ro||(Ro=await I0({themes:["github-dark"],langs:N0}),Ro)}async function j0(e,t){const n=await D0(),r=n.getLoadedLanguages().includes(t)?t:"txt";return n.codeToHtml(e,{lang:r,theme:"github-dark"})}const M0=({code:e,language:t})=>{const[n,r]=I.useState(""),[o,i]=I.useState(!1),l=Ht(u=>u.addToast);I.useEffect(()=>{j0(e,t).then(r)},[e,t]);const s=()=>{navigator.clipboard.writeText(e),l({message:"Code copied to clipboard",type:"success"})},a=async()=>{try{const u=await __(`runCode(${JSON.stringify(e)})`);l({message:`Execution result: ${u.result||u.error}`,type:u.success?"info":"error"})}catch(u){l({message:`Execution failed: ${u.message}`,type:"error"})}};return v.jsxs("div",{className:"bg-[#282c34] rounded-md my-2 text-sm overflow-hidden",children:[v.jsxs("div",{className:"flex justify-between items-center px-3 py-1 bg-gray-700 text-gray-300",children:[v.jsx("span",{className:"font-mono",children:t}),v.jsxs("div",{className:"flex gap-1",children:[v.jsx("button",{onClick:()=>i(!o),className:"p-1 hover:bg-gray-600 rounded",children:o?v.jsx(jf,{size:14}):v.jsx(hg,{size:14})}),v.jsx("button",{onClick:s,className:"p-1 hover:bg-gray-600 rounded",children:v.jsx(_g,{size:14})}),v.jsx("button",{className:"p-1 hover:bg-gray-600 rounded",children:v.jsx(yg,{size:14})}),["javascript","typescript","extendscript","jsx"].includes(t)&&v.jsx("button",{onClick:a,className:"p-1 hover:bg-gray-600 rounded",children:v.jsx(Og,{size:14})})]})]}),!o&&v.jsx("div",{className:"p-3 overflow-x-auto",dangerouslySetInnerHTML:{__html:n}})]})},b0=({message:e})=>{const t=e.role==="user",{retryLastUserMessage:n}=qn(),r=e.content.split(/(```[\w\s]*\n[\s\S]*?\n```)/g);return v.jsxs("div",{className:`flex items-start gap-3 ${t?"justify-end":"self-start"}`,children:[!t&&v.jsx("div",{className:"flex-shrink-0 w-8 h-8 rounded-full bg-adobe-secondary flex items-center justify-center",children:v.jsx(mg,{size:18})}),v.jsxs("div",{className:`max-w-[85%] rounded-lg p-3 ${t?"bg-blue-600 text-white":e.status==="error"?"bg-red-100 text-red-800":"bg-adobe-secondary"}`,children:[r.map((o,i)=>{if(o.startsWith("```")){const l=o.match(/```(\w*)\n([\s\S]*?)\n```/);if(l){const s=l[1]||"plaintext",a=l[2]||"";return v.jsx(M0,{code:a,language:s},i)}}return v.jsx("p",{className:"whitespace-pre-wrap",dangerouslySetInnerHTML:{__html:o.replace(/\*\*(.*?)\*\*/g,"<b>$1</b>")}},i)}),e.status==="error"&&!t&&v.jsx("div",{className:"mt-2 pt-2 border-t border-red-300 flex justify-end",children:v.jsxs("button",{onClick:n,className:"flex items-center gap-2 text-xs font-semibold text-red-700 hover:text-red-900",children:[v.jsx(bf,{size:12}),"Try again"]})})]}),t&&v.jsx("div",{className:"flex-shrink-0 w-8 h-8 rounded-full bg-adobe-secondary flex items-center justify-center",children:v.jsx(Ag,{size:18})})]})},V0=()=>v.jsxs("div",{className:"flex items-center gap-2 self-start",children:[v.jsx("div",{className:"flex-shrink-0 w-8 h-8 rounded-full bg-adobe-secondary flex items-center justify-center",children:v.jsx(qr,{size:18,className:"animate-spin"})}),v.jsx("div",{className:"bg-adobe-secondary rounded-lg p-3",children:v.jsxs("div",{className:"flex items-center gap-1.5",children:[v.jsx("span",{className:"h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-75"}),v.jsx("span",{className:"h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-150"}),v.jsx("span",{className:"h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-300"})]})})]}),z0=()=>{var i;const{conversations:e,currentConversationId:t,isLoading:n}=qn(),r=t?(i=e[t])==null?void 0:i.messages:[],o=I.useRef(null);return I.useEffect(()=>{o.current&&(o.current.scrollTop=o.current.scrollHeight)},[r]),v.jsx("div",{ref:o,className:"flex-grow p-4 overflow-y-auto",children:v.jsxs("div",{className:"flex flex-col gap-4",children:[r&&r.map(l=>v.jsx(b0,{message:l},l.id)),n&&r&&r.length>0&&r[r.length-1].content===""&&v.jsx(V0,{})]})})},U0=()=>{const[e,t]=I.useState(""),[n,r]=I.useState(!1),{sendChatMessage:o,isLoading:i}=qn(),l=Ht(m=>m.addToast),s=I.useRef(null),a=I.useRef(null),u=()=>{e.trim()&&!i&&(o(e.trim()),t(""),setTimeout(()=>{var m;return(m=s.current)==null?void 0:m.focus()},0))},d=m=>{m.key==="Enter"&&!m.shiftKey&&(m.preventDefault(),u())},f=()=>{var g;if(n){(g=a.current)==null||g.stop(),r(!1);return}const m=window.SpeechRecognition||window.webkitSpeechRecognition;if(!m){l({message:"Voice recognition is not supported in this browser.",type:"error"});return}a.current=new m,a.current.continuous=!0,a.current.interimResults=!0,a.current.lang="en-US",a.current.onstart=()=>r(!0),a.current.onend=()=>r(!1),a.current.onerror=_=>{l({message:`Voice recognition error: ${_.error}`,type:"error"}),r(!1)},a.current.onresult=_=>{let E="";for(let S=_.resultIndex;S<_.results.length;++S)_.results[S].isFinal?t(p=>p+_.results[S][0].transcript+" "):E+=_.results[S][0].transcript},a.current.start()};return I.useEffect(()=>{if(s.current){s.current.style.height="auto";const m=s.current.scrollHeight;s.current.style.height=`${m}px`}},[e]),v.jsx("div",{className:"p-2 border-t border-adobe bg-adobe-bg flex-shrink-0",children:v.jsxs("div",{className:"flex items-start gap-2 p-2 rounded-md bg-adobe-secondary",children:[v.jsx("button",{className:"p-1.5 rounded","aria-label":"Attach File",children:v.jsx(xg,{size:18})}),v.jsx("button",{onClick:f,className:`p-1.5 rounded ${n?"bg-red-500 text-white":""}`,"aria-label":"Voice Input",children:v.jsx(kg,{size:18})}),v.jsx("textarea",{ref:s,value:e,onChange:m=>t(m.target.value),onKeyDown:d,placeholder:n?"Listening...":"Type a message or use voice input...",className:"flex-grow bg-transparent focus:outline-none resize-none max-h-48 overflow-y-auto",rows:1,maxLength:4e3,disabled:i}),v.jsx("button",{onClick:u,disabled:i||!e.trim(),className:"p-1.5 rounded bg-blue-600 text-white disabled:bg-gray-500 self-end",children:i?v.jsx(qr,{size:18,className:"animate-spin"}):v.jsx(Pg,{size:18})})]})})},Fi=({title:e,children:t})=>{const n=Xr(r=>r.closeModal);return I.useEffect(()=>{const r=o=>{o.key==="Escape"&&n()};return window.addEventListener("keydown",r),()=>window.removeEventListener("keydown",r)},[n]),v.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex justify-center items-center z-50",onClick:n,children:v.jsxs("div",{className:"bg-adobe-bg rounded-lg shadow-xl w-full max-w-md m-4 flex flex-col",onClick:r=>r.stopPropagation(),children:[v.jsxs("header",{className:"flex justify-between items-center p-4 border-b border-adobe",children:[v.jsx("h2",{className:"text-lg font-semibold",children:e}),v.jsx("button",{onClick:n,className:"p-1 rounded-full hover:bg-adobe-secondary",children:v.jsx($f,{size:20})})]}),v.jsx("div",{className:"p-4 overflow-y-auto",children:t})]})})},$0=()=>{const{theme:e,setTheme:t}=Gt(),{clearAllConversations:n}=qn(),r=Ht(i=>i.addToast),o=()=>{window.confirm("Are you sure you want to delete all chat history? This action cannot be undone.")&&(n(),r({message:"All chat history has been cleared.",type:"success"}))};return v.jsx(Fi,{title:"Settings",children:v.jsxs("div",{className:"flex flex-col gap-6",children:[v.jsxs("div",{children:[v.jsx("label",{htmlFor:"theme-select",className:"block text-sm font-medium mb-1",children:"Theme"}),v.jsxs("select",{id:"theme-select",value:e,onChange:i=>t(i.target.value),className:"w-full p-2 rounded bg-adobe-secondary border border-adobe focus:outline-none focus:ring-2 focus:ring-blue-500",children:[v.jsx("option",{value:"auto",children:"Auto (Sync with Adobe)"}),v.jsx("option",{value:"light",children:"Light"}),v.jsx("option",{value:"dark",children:"Dark"})]})]}),v.jsxs("div",{className:"border-t border-adobe pt-4",children:[v.jsx("h4",{className:"text-md font-semibold mb-2",children:"Data Management"}),v.jsxs("button",{onClick:o,className:"w-full flex items-center justify-center gap-2 px-3 py-2 rounded bg-red-600 hover:bg-red-700 text-white text-sm",children:[v.jsx(zf,{size:16}),"Clear All Chat History"]}),v.jsx("p",{className:"text-xs text-gray-500 mt-2",children:"This will permanently delete all your conversations and messages stored within the extension."})]})]})})},yc=[{id:"openai",name:"OpenAI",icon:v.jsx(hn,{size:18})},{id:"anthropic",name:"Anthropic",icon:v.jsx(hn,{size:18})},{id:"google",name:"Google Gemini",icon:v.jsx(hn,{size:18})},{id:"groq",name:"Groq",icon:v.jsx(hn,{size:18})},{id:"deepseek",name:"DeepSeek",icon:v.jsx(hn,{size:18})},{id:"openrouter",name:"OpenRouter",icon:v.jsx(hn,{size:18})},{id:"ollama",name:"Ollama",icon:v.jsx(Tg,{size:18})}],B0=({providerId:e,providerName:t})=>{const[n,r]=I.useState(""),[o,i]=I.useState(!1),[l,s]=I.useState(!1),a=Ht(f=>f.addToast);I.useEffect(()=>{const f=mt(e);i(!!f),r("")},[e]);const u=()=>{if(!n.trim()){a({message:"API key cannot be empty.",type:"warning"});return}Es(e,n.trim()),i(!0),r(""),s(!1),a({message:`${t} API key saved successfully.`,type:"success"})},d=()=>{Es(e,""),i(!1),a({message:`${t} API key cleared.`,type:"info"})};return v.jsxs("div",{className:"flex flex-col gap-4",children:[v.jsxs("h3",{className:"text-lg font-semibold",children:[t," Configuration"]}),v.jsxs("div",{children:[v.jsx("label",{className:"block text-sm font-medium mb-1",children:"API Key"}),v.jsxs("div",{className:"flex items-center gap-2",children:[v.jsx("input",{type:l?"text":"password",value:n,onChange:f=>r(f.target.value),placeholder:o?"•••••••••••••••• (Key is set)":"Enter your API key",className:"w-full p-2 rounded bg-adobe-secondary border border-adobe focus:outline-none focus:ring-2 focus:ring-blue-500"}),v.jsx("button",{onClick:()=>s(!l),className:"p-2 hover:bg-adobe-secondary rounded",children:l?v.jsx(vg,{size:18}):v.jsx(Eg,{size:18})})]}),v.jsxs("p",{className:`text-xs mt-1 ${o?"text-green-500":"text-yellow-500"}`,children:["Status: ",o?"API Key is configured":"API Key not set"]})]}),v.jsxs("div",{className:"flex justify-end gap-2",children:[o&&v.jsxs("button",{onClick:d,className:"flex items-center gap-2 px-3 py-2 rounded bg-red-600 hover:bg-red-700 text-white text-sm",children:[v.jsx(zf,{size:16}),"Clear Key"]}),v.jsxs("button",{onClick:u,disabled:!n.trim(),className:"flex items-center gap-2 px-3 py-2 rounded bg-blue-600 hover:bg-blue-700 text-white text-sm disabled:bg-gray-500",children:[v.jsx(Vf,{size:16}),"Save Key"]})]})]})},F0=()=>{const{providers:e,setOllamaBaseUrl:t}=Gt(),[n,r]=I.useState(e.ollama.baseURL||"http://localhost:11434"),[o,i]=I.useState("idle"),l=Ht(d=>d.addToast),s=()=>{t(n),l({message:"Ollama Base URL updated.",type:"success"})},a=async()=>{i("testing");try{const f=await Yr("ollama",{baseURL:n}).getModels();f.length>0?(i("success"),l({message:`Connection successful! Found ${f.length} models.`,type:"success"})):(i("failed"),l({message:"Connection successful, but no models found.",type:"warning"}))}catch{i("failed"),l({message:"Failed to connect to Ollama. Check the URL and ensure Ollama is running.",type:"error"})}},u={idle:null,testing:v.jsx(qr,{size:18,className:"animate-spin text-yellow-500"}),success:v.jsx(Ca,{size:18,className:"text-green-500"}),failed:v.jsx(Mf,{size:18,className:"text-red-500"})}[o];return v.jsxs("div",{className:"flex flex-col gap-4",children:[v.jsx("h3",{className:"text-lg font-semibold",children:"Ollama (Local) Configuration"}),v.jsxs("div",{children:[v.jsx("label",{className:"block text-sm font-medium mb-1",children:"Ollama Server URL"}),v.jsxs("div",{className:"flex items-center gap-2",children:[v.jsx("input",{type:"text",value:n,onChange:d=>r(d.target.value),placeholder:"e.g., http://localhost:11434",className:"w-full p-2 rounded bg-adobe-secondary border border-adobe focus:outline-none focus:ring-2 focus:ring-blue-500"}),u]})]}),v.jsxs("div",{className:"flex justify-end gap-2",children:[v.jsx("button",{onClick:a,disabled:o==="testing",className:"flex items-center gap-2 px-3 py-2 rounded bg-gray-600 hover:bg-gray-700 text-white text-sm disabled:opacity-50",children:"Test Connection"}),v.jsxs("button",{onClick:s,className:"flex items-center gap-2 px-3 py-2 rounded bg-blue-600 hover:bg-blue-700 text-white text-sm",children:[v.jsx(Vf,{size:16}),"Save URL"]})]})]})},H0=()=>{const[e,t]=I.useState("openai"),n=()=>{const r=yc.find(o=>o.id===e);return r?r.id==="ollama"?v.jsx(F0,{}):v.jsx(B0,{providerId:r.id,providerName:r.name}):null};return v.jsx(Fi,{title:"Provider Configuration",children:v.jsxs("div",{className:"flex min-h-[300px]",children:[v.jsx("nav",{className:"w-1/3 border-r border-adobe pr-4",children:v.jsx("ul",{className:"flex flex-col gap-1",children:yc.map(r=>v.jsx("li",{children:v.jsxs("button",{onClick:()=>t(r.id),className:`w-full flex items-center gap-3 text-left p-2 rounded text-sm ${e===r.id?"bg-blue-600 text-white":"hover:bg-adobe-secondary"}`,children:[r.icon,r.name]})},r.id))})}),v.jsx("section",{className:"w-2/3 pl-4",children:n()})]})})},G0=()=>{const{conversations:e,setCurrentConversationId:t}=qn(),{closeModal:n}=Xr(),r=Object.values(e),o=i=>{t(i),n()};return v.jsx(Fi,{title:"Chat History",children:r.length===0?v.jsx("p",{children:"No chat history yet."}):v.jsx("ul",{className:"flex flex-col gap-2",children:r.map(i=>v.jsxs("li",{onClick:()=>o(i.id),className:"p-2 rounded hover:bg-adobe-secondary cursor-pointer",children:[v.jsx("p",{className:"font-semibold",children:i.title}),v.jsx("p",{className:"text-xs opacity-70",children:new Date(i.createdAt).toLocaleString()})]},i.id))})})},W0=["openai","anthropic","google","groq","deepseek","openrouter","ollama"],K0=({providerId:e})=>{const[t,n]=I.useState({status:"loading"}),r=async()=>{n({status:"loading"});const i=Date.now();try{const s=await Yr(e).getModels();s.length>0?n({status:"success",latency:Date.now()-i,modelsCount:s.length}):n({status:"error",error:"Connected, but no models found."})}catch(l){const s=/API key/i.test(l.message);n({status:s?"unconfigured":"error",error:s?"API key not set or invalid.":"Connection failed."})}};I.useEffect(()=>{r()},[e]);const o={loading:v.jsx(qr,{size:16,className:"animate-spin text-yellow-500"}),success:v.jsx(Ca,{size:16,className:"text-green-500"}),error:v.jsx(Mf,{size:16,className:"text-red-500"}),unconfigured:v.jsx(Uf,{size:16,className:"text-orange-500"})};return v.jsxs("tr",{className:"border-b border-adobe",children:[v.jsx("td",{className:"p-2 font-semibold capitalize",children:e}),v.jsx("td",{className:"p-2 text-center",children:o[t.status]}),v.jsx("td",{className:"p-2 text-xs",children:t.latency?`${t.latency} ms`:"N/A"}),v.jsx("td",{className:"p-2 text-xs",children:t.modelsCount??"N/A"}),v.jsx("td",{className:"p-2 text-xs text-gray-400",children:t.error??"OK"}),v.jsx("td",{className:"p-2 text-center",children:v.jsx("button",{onClick:r,title:"Re-check status",children:v.jsx(bf,{size:14,className:"hover:text-blue-500"})})})]})},Q0=()=>v.jsx(Fi,{title:"System Connection Status",children:v.jsxs("div",{className:"max-h-[60vh] overflow-y-auto",children:[v.jsxs("table",{className:"w-full text-sm text-left",children:[v.jsx("thead",{className:"bg-adobe-secondary sticky top-0",children:v.jsxs("tr",{className:"border-b-2 border-adobe",children:[v.jsx("th",{className:"p-2",children:"Provider"}),v.jsx("th",{className:"p-2 text-center",children:"Status"}),v.jsx("th",{className:"p-2",children:"Latency"}),v.jsx("th",{className:"p-2",children:"Models"}),v.jsx("th",{className:"p-2",children:"Details"}),v.jsx("th",{className:"p-2 text-center",children:"Check"})]})}),v.jsx("tbody",{children:W0.map(e=>v.jsx(K0,{providerId:e},e))})]}),v.jsx("p",{className:"text-xs text-gray-500 mt-4",children:"This panel shows the real-time status of each AI provider. Latency is measured by fetching the model list."})]})}),q0={success:v.jsx(Ca,{className:"text-green-500"}),error:v.jsx(gg,{className:"text-red-500"}),warning:v.jsx(Uf,{className:"text-yellow-500"}),info:v.jsx(wg,{className:"text-blue-500"})},X0={success:"bg-green-100 border-green-400",error:"bg-red-100 border-red-400",warning:"bg-yellow-100 border-yellow-400",info:"bg-blue-100 border-blue-400"},Y0=()=>{const{toasts:e,removeToast:t}=Ht();return e.length?v.jsx("div",{className:"fixed top-4 right-4 z-50 flex flex-col gap-2",children:e.map(n=>v.jsxs("div",{className:`flex items-center gap-3 p-3 rounded-md shadow-lg border ${X0[n.type]}`,children:[q0[n.type],v.jsx("p",{className:"text-sm text-gray-800",children:n.message}),v.jsx("button",{onClick:()=>t(n.id),children:v.jsx($f,{size:16,className:"text-gray-500"})})]},n.id))}):null},J0=()=>{const{theme:e,applyTheme:t}=Gt(),n=Xr(o=>o.activeModal);I.useEffect(()=>{t()},[e,t]);const r=()=>{switch(n){case"settings":return v.jsx($0,{});case"provider":return v.jsx(H0,{});case"history":return v.jsx(G0,{});case"status":return v.jsx(Q0,{});default:return null}};return v.jsxs("div",{className:"flex flex-col h-screen bg-adobe-bg text-adobe-text font-sans antialiased",children:[v.jsx(C_,{}),v.jsxs("main",{className:"flex-grow flex flex-col overflow-hidden",children:[v.jsx(z0,{}),v.jsx(U0,{})]}),r(),v.jsx(Y0,{})]})};class Z0 extends I.Component{constructor(){super(...arguments);k(this,"state",{hasError:!1})}static getDerivedStateFromError(n){return{hasError:!0}}componentDidCatch(n,r){cn.error("Uncaught error:",n,r),this.setState({error:n})}render(){var n;return this.state.hasError?v.jsxs("div",{className:"p-4 text-red-500 bg-red-100 border border-red-500 rounded-md",children:[v.jsx("h1",{className:"font-bold",children:"Something went wrong."}),v.jsx("p",{children:"An unexpected error occurred. Please try reloading the panel."}),v.jsx("details",{className:"mt-2 text-sm text-gray-700",children:(n=this.state.error)==null?void 0:n.toString()})]}):this.props.children}}g_();Pl.createRoot(document.getElementById("root")).render(v.jsx(Oc.StrictMode,{children:v.jsx(Z0,{children:v.jsx(J0,{})})}));
