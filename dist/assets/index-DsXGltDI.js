var qc=Object.defineProperty;var bc=(e,t,n)=>t in e?qc(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var yr=(e,t,n)=>bc(e,typeof t!="symbol"?t+"":t,n);import{g as ed}from"./shiki-DU-mLqct.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))r(l);new MutationObserver(l=>{for(const o of l)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(l){const o={};return l.integrity&&(o.integrity=l.integrity),l.referrerPolicy&&(o.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?o.credentials="include":l.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(l){if(l.ep)return;l.ep=!0;const o=n(l);fetch(l.href,o)}})();function xu(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Su={exports:{}},wl={},ku={exports:{}},z={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sr=Symbol.for("react.element"),td=Symbol.for("react.portal"),nd=Symbol.for("react.fragment"),rd=Symbol.for("react.strict_mode"),ld=Symbol.for("react.profiler"),od=Symbol.for("react.provider"),sd=Symbol.for("react.context"),id=Symbol.for("react.forward_ref"),ud=Symbol.for("react.suspense"),ad=Symbol.for("react.memo"),cd=Symbol.for("react.lazy"),oi=Symbol.iterator;function dd(e){return e===null||typeof e!="object"?null:(e=oi&&e[oi]||e["@@iterator"],typeof e=="function"?e:null)}var Eu={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Cu=Object.assign,Nu={};function hn(e,t,n){this.props=e,this.context=t,this.refs=Nu,this.updater=n||Eu}hn.prototype.isReactComponent={};hn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};hn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function _u(){}_u.prototype=hn.prototype;function as(e,t,n){this.props=e,this.context=t,this.refs=Nu,this.updater=n||Eu}var cs=as.prototype=new _u;cs.constructor=as;Cu(cs,hn.prototype);cs.isPureReactComponent=!0;var si=Array.isArray,ju=Object.prototype.hasOwnProperty,ds={current:null},Pu={key:!0,ref:!0,__self:!0,__source:!0};function Tu(e,t,n){var r,l={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)ju.call(t,r)&&!Pu.hasOwnProperty(r)&&(l[r]=t[r]);var i=arguments.length-2;if(i===1)l.children=n;else if(1<i){for(var u=Array(i),c=0;c<i;c++)u[c]=arguments[c+2];l.children=u}if(e&&e.defaultProps)for(r in i=e.defaultProps,i)l[r]===void 0&&(l[r]=i[r]);return{$$typeof:sr,type:e,key:o,ref:s,props:l,_owner:ds.current}}function fd(e,t){return{$$typeof:sr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function fs(e){return typeof e=="object"&&e!==null&&e.$$typeof===sr}function pd(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var ii=/\/+/g;function Hl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?pd(""+e.key):t.toString(36)}function Dr(e,t,n,r,l){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case sr:case td:s=!0}}if(s)return s=e,l=l(s),e=r===""?"."+Hl(s,0):r,si(l)?(n="",e!=null&&(n=e.replace(ii,"$&/")+"/"),Dr(l,t,n,"",function(c){return c})):l!=null&&(fs(l)&&(l=fd(l,n+(!l.key||s&&s.key===l.key?"":(""+l.key).replace(ii,"$&/")+"/")+e)),t.push(l)),1;if(s=0,r=r===""?".":r+":",si(e))for(var i=0;i<e.length;i++){o=e[i];var u=r+Hl(o,i);s+=Dr(o,t,n,u,l)}else if(u=dd(e),typeof u=="function")for(e=u.call(e),i=0;!(o=e.next()).done;)o=o.value,u=r+Hl(o,i++),s+=Dr(o,t,n,u,l);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function vr(e,t,n){if(e==null)return e;var r=[],l=0;return Dr(e,r,"","",function(o){return t.call(n,o,l++)}),r}function md(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var de={current:null},Ar={transition:null},hd={ReactCurrentDispatcher:de,ReactCurrentBatchConfig:Ar,ReactCurrentOwner:ds};function Lu(){throw Error("act(...) is not supported in production builds of React.")}z.Children={map:vr,forEach:function(e,t,n){vr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return vr(e,function(){t++}),t},toArray:function(e){return vr(e,function(t){return t})||[]},only:function(e){if(!fs(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};z.Component=hn;z.Fragment=nd;z.Profiler=ld;z.PureComponent=as;z.StrictMode=rd;z.Suspense=ud;z.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=hd;z.act=Lu;z.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Cu({},e.props),l=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=ds.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var i=e.type.defaultProps;for(u in t)ju.call(t,u)&&!Pu.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&i!==void 0?i[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){i=Array(u);for(var c=0;c<u;c++)i[c]=arguments[c+2];r.children=i}return{$$typeof:sr,type:e.type,key:l,ref:o,props:r,_owner:s}};z.createContext=function(e){return e={$$typeof:sd,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:od,_context:e},e.Consumer=e};z.createElement=Tu;z.createFactory=function(e){var t=Tu.bind(null,e);return t.type=e,t};z.createRef=function(){return{current:null}};z.forwardRef=function(e){return{$$typeof:id,render:e}};z.isValidElement=fs;z.lazy=function(e){return{$$typeof:cd,_payload:{_status:-1,_result:e},_init:md}};z.memo=function(e,t){return{$$typeof:ad,type:e,compare:t===void 0?null:t}};z.startTransition=function(e){var t=Ar.transition;Ar.transition={};try{e()}finally{Ar.transition=t}};z.unstable_act=Lu;z.useCallback=function(e,t){return de.current.useCallback(e,t)};z.useContext=function(e){return de.current.useContext(e)};z.useDebugValue=function(){};z.useDeferredValue=function(e){return de.current.useDeferredValue(e)};z.useEffect=function(e,t){return de.current.useEffect(e,t)};z.useId=function(){return de.current.useId()};z.useImperativeHandle=function(e,t,n){return de.current.useImperativeHandle(e,t,n)};z.useInsertionEffect=function(e,t){return de.current.useInsertionEffect(e,t)};z.useLayoutEffect=function(e,t){return de.current.useLayoutEffect(e,t)};z.useMemo=function(e,t){return de.current.useMemo(e,t)};z.useReducer=function(e,t,n){return de.current.useReducer(e,t,n)};z.useRef=function(e){return de.current.useRef(e)};z.useState=function(e){return de.current.useState(e)};z.useSyncExternalStore=function(e,t,n){return de.current.useSyncExternalStore(e,t,n)};z.useTransition=function(){return de.current.useTransition()};z.version="18.3.1";ku.exports=z;var T=ku.exports;const Ru=xu(T);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yd=T,vd=Symbol.for("react.element"),gd=Symbol.for("react.fragment"),wd=Object.prototype.hasOwnProperty,xd=yd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Sd={key:!0,ref:!0,__self:!0,__source:!0};function zu(e,t,n){var r,l={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)wd.call(t,r)&&!Sd.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)l[r]===void 0&&(l[r]=t[r]);return{$$typeof:vd,type:e,key:o,ref:s,props:l,_owner:xd.current}}wl.Fragment=gd;wl.jsx=zu;wl.jsxs=zu;Su.exports=wl;var f=Su.exports,ho={},Ou={exports:{}},ke={},Mu={exports:{}},Iu={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(N,L){var R=N.length;N.push(L);e:for(;0<R;){var Q=R-1>>>1,Z=N[Q];if(0<l(Z,L))N[Q]=L,N[R]=Z,R=Q;else break e}}function n(N){return N.length===0?null:N[0]}function r(N){if(N.length===0)return null;var L=N[0],R=N.pop();if(R!==L){N[0]=R;e:for(var Q=0,Z=N.length,mr=Z>>>1;Q<mr;){var kt=2*(Q+1)-1,$l=N[kt],Et=kt+1,hr=N[Et];if(0>l($l,R))Et<Z&&0>l(hr,$l)?(N[Q]=hr,N[Et]=R,Q=Et):(N[Q]=$l,N[kt]=R,Q=kt);else if(Et<Z&&0>l(hr,R))N[Q]=hr,N[Et]=R,Q=Et;else break e}}return L}function l(N,L){var R=N.sortIndex-L.sortIndex;return R!==0?R:N.id-L.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,i=s.now();e.unstable_now=function(){return s.now()-i}}var u=[],c=[],m=1,y=null,h=3,g=!1,v=!1,w=!1,k=typeof setTimeout=="function"?setTimeout:null,d=typeof clearTimeout=="function"?clearTimeout:null,a=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function p(N){for(var L=n(c);L!==null;){if(L.callback===null)r(c);else if(L.startTime<=N)r(c),L.sortIndex=L.expirationTime,t(u,L);else break;L=n(c)}}function x(N){if(w=!1,p(N),!v)if(n(u)!==null)v=!0,Fl(E);else{var L=n(c);L!==null&&Ul(x,L.startTime-N)}}function E(N,L){v=!1,w&&(w=!1,d(P),P=-1),g=!0;var R=h;try{for(p(L),y=n(u);y!==null&&(!(y.expirationTime>L)||N&&!Le());){var Q=y.callback;if(typeof Q=="function"){y.callback=null,h=y.priorityLevel;var Z=Q(y.expirationTime<=L);L=e.unstable_now(),typeof Z=="function"?y.callback=Z:y===n(u)&&r(u),p(L)}else r(u);y=n(u)}if(y!==null)var mr=!0;else{var kt=n(c);kt!==null&&Ul(x,kt.startTime-L),mr=!1}return mr}finally{y=null,h=R,g=!1}}var j=!1,_=null,P=-1,W=5,O=-1;function Le(){return!(e.unstable_now()-O<W)}function wn(){if(_!==null){var N=e.unstable_now();O=N;var L=!0;try{L=_(!0,N)}finally{L?xn():(j=!1,_=null)}}else j=!1}var xn;if(typeof a=="function")xn=function(){a(wn)};else if(typeof MessageChannel<"u"){var li=new MessageChannel,Zc=li.port2;li.port1.onmessage=wn,xn=function(){Zc.postMessage(null)}}else xn=function(){k(wn,0)};function Fl(N){_=N,j||(j=!0,xn())}function Ul(N,L){P=k(function(){N(e.unstable_now())},L)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(N){N.callback=null},e.unstable_continueExecution=function(){v||g||(v=!0,Fl(E))},e.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):W=0<N?Math.floor(1e3/N):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(N){switch(h){case 1:case 2:case 3:var L=3;break;default:L=h}var R=h;h=L;try{return N()}finally{h=R}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(N,L){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var R=h;h=N;try{return L()}finally{h=R}},e.unstable_scheduleCallback=function(N,L,R){var Q=e.unstable_now();switch(typeof R=="object"&&R!==null?(R=R.delay,R=typeof R=="number"&&0<R?Q+R:Q):R=Q,N){case 1:var Z=-1;break;case 2:Z=250;break;case 5:Z=**********;break;case 4:Z=1e4;break;default:Z=5e3}return Z=R+Z,N={id:m++,callback:L,priorityLevel:N,startTime:R,expirationTime:Z,sortIndex:-1},R>Q?(N.sortIndex=R,t(c,N),n(u)===null&&N===n(c)&&(w?(d(P),P=-1):w=!0,Ul(x,R-Q))):(N.sortIndex=Z,t(u,N),v||g||(v=!0,Fl(E))),N},e.unstable_shouldYield=Le,e.unstable_wrapCallback=function(N){var L=h;return function(){var R=h;h=L;try{return N.apply(this,arguments)}finally{h=R}}}})(Iu);Mu.exports=Iu;var kd=Mu.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ed=T,Se=kd;function S(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Du=new Set,Bn={};function At(e,t){sn(e,t),sn(e+"Capture",t)}function sn(e,t){for(Bn[e]=t,e=0;e<t.length;e++)Du.add(t[e])}var Xe=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),yo=Object.prototype.hasOwnProperty,Cd=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ui={},ai={};function Nd(e){return yo.call(ai,e)?!0:yo.call(ui,e)?!1:Cd.test(e)?ai[e]=!0:(ui[e]=!0,!1)}function _d(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function jd(e,t,n,r){if(t===null||typeof t>"u"||_d(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function fe(e,t,n,r,l,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var re={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){re[e]=new fe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];re[t]=new fe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){re[e]=new fe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){re[e]=new fe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){re[e]=new fe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){re[e]=new fe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){re[e]=new fe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){re[e]=new fe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){re[e]=new fe(e,5,!1,e.toLowerCase(),null,!1,!1)});var ps=/[\-:]([a-z])/g;function ms(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(ps,ms);re[t]=new fe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(ps,ms);re[t]=new fe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ps,ms);re[t]=new fe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){re[e]=new fe(e,1,!1,e.toLowerCase(),null,!1,!1)});re.xlinkHref=new fe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){re[e]=new fe(e,1,!1,e.toLowerCase(),null,!0,!0)});function hs(e,t,n,r){var l=re.hasOwnProperty(t)?re[t]:null;(l!==null?l.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(jd(t,n,l,r)&&(n=null),r||l===null?Nd(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,r=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var be=Ed.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,gr=Symbol.for("react.element"),Ht=Symbol.for("react.portal"),Bt=Symbol.for("react.fragment"),ys=Symbol.for("react.strict_mode"),vo=Symbol.for("react.profiler"),Au=Symbol.for("react.provider"),Fu=Symbol.for("react.context"),vs=Symbol.for("react.forward_ref"),go=Symbol.for("react.suspense"),wo=Symbol.for("react.suspense_list"),gs=Symbol.for("react.memo"),nt=Symbol.for("react.lazy"),Uu=Symbol.for("react.offscreen"),ci=Symbol.iterator;function Sn(e){return e===null||typeof e!="object"?null:(e=ci&&e[ci]||e["@@iterator"],typeof e=="function"?e:null)}var V=Object.assign,Bl;function Tn(e){if(Bl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Bl=t&&t[1]||""}return`
`+Bl+e}var Vl=!1;function Kl(e,t){if(!e||Vl)return"";Vl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var l=c.stack.split(`
`),o=r.stack.split(`
`),s=l.length-1,i=o.length-1;1<=s&&0<=i&&l[s]!==o[i];)i--;for(;1<=s&&0<=i;s--,i--)if(l[s]!==o[i]){if(s!==1||i!==1)do if(s--,i--,0>i||l[s]!==o[i]){var u=`
`+l[s].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=s&&0<=i);break}}}finally{Vl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Tn(e):""}function Pd(e){switch(e.tag){case 5:return Tn(e.type);case 16:return Tn("Lazy");case 13:return Tn("Suspense");case 19:return Tn("SuspenseList");case 0:case 2:case 15:return e=Kl(e.type,!1),e;case 11:return e=Kl(e.type.render,!1),e;case 1:return e=Kl(e.type,!0),e;default:return""}}function xo(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Bt:return"Fragment";case Ht:return"Portal";case vo:return"Profiler";case ys:return"StrictMode";case go:return"Suspense";case wo:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Fu:return(e.displayName||"Context")+".Consumer";case Au:return(e._context.displayName||"Context")+".Provider";case vs:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case gs:return t=e.displayName||null,t!==null?t:xo(e.type)||"Memo";case nt:t=e._payload,e=e._init;try{return xo(e(t))}catch{}}return null}function Td(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return xo(t);case 8:return t===ys?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function yt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function $u(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Ld(e){var t=$u(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function wr(e){e._valueTracker||(e._valueTracker=Ld(e))}function Hu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=$u(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Xr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function So(e,t){var n=t.checked;return V({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function di(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=yt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Bu(e,t){t=t.checked,t!=null&&hs(e,"checked",t,!1)}function ko(e,t){Bu(e,t);var n=yt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Eo(e,t.type,n):t.hasOwnProperty("defaultValue")&&Eo(e,t.type,yt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function fi(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Eo(e,t,n){(t!=="number"||Xr(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Ln=Array.isArray;function bt(e,t,n,r){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&r&&(e[n].defaultSelected=!0)}else{for(n=""+yt(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,r&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function Co(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(S(91));return V({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function pi(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(S(92));if(Ln(n)){if(1<n.length)throw Error(S(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:yt(n)}}function Vu(e,t){var n=yt(t.value),r=yt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function mi(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Ku(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function No(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Ku(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var xr,Wu=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(xr=xr||document.createElement("div"),xr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=xr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Vn(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var On={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Rd=["Webkit","ms","Moz","O"];Object.keys(On).forEach(function(e){Rd.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),On[t]=On[e]})});function Qu(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||On.hasOwnProperty(e)&&On[e]?(""+t).trim():t+"px"}function Gu(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,l=Qu(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,l):e[n]=l}}var zd=V({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function _o(e,t){if(t){if(zd[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(S(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(S(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(S(61))}if(t.style!=null&&typeof t.style!="object")throw Error(S(62))}}function jo(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Po=null;function ws(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var To=null,en=null,tn=null;function hi(e){if(e=ar(e)){if(typeof To!="function")throw Error(S(280));var t=e.stateNode;t&&(t=Cl(t),To(e.stateNode,e.type,t))}}function Yu(e){en?tn?tn.push(e):tn=[e]:en=e}function Xu(){if(en){var e=en,t=tn;if(tn=en=null,hi(e),t)for(e=0;e<t.length;e++)hi(t[e])}}function Ju(e,t){return e(t)}function Zu(){}var Wl=!1;function qu(e,t,n){if(Wl)return e(t,n);Wl=!0;try{return Ju(e,t,n)}finally{Wl=!1,(en!==null||tn!==null)&&(Zu(),Xu())}}function Kn(e,t){var n=e.stateNode;if(n===null)return null;var r=Cl(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(S(231,t,typeof n));return n}var Lo=!1;if(Xe)try{var kn={};Object.defineProperty(kn,"passive",{get:function(){Lo=!0}}),window.addEventListener("test",kn,kn),window.removeEventListener("test",kn,kn)}catch{Lo=!1}function Od(e,t,n,r,l,o,s,i,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(m){this.onError(m)}}var Mn=!1,Jr=null,Zr=!1,Ro=null,Md={onError:function(e){Mn=!0,Jr=e}};function Id(e,t,n,r,l,o,s,i,u){Mn=!1,Jr=null,Od.apply(Md,arguments)}function Dd(e,t,n,r,l,o,s,i,u){if(Id.apply(this,arguments),Mn){if(Mn){var c=Jr;Mn=!1,Jr=null}else throw Error(S(198));Zr||(Zr=!0,Ro=c)}}function Ft(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function bu(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function yi(e){if(Ft(e)!==e)throw Error(S(188))}function Ad(e){var t=e.alternate;if(!t){if(t=Ft(e),t===null)throw Error(S(188));return t!==e?null:e}for(var n=e,r=t;;){var l=n.return;if(l===null)break;var o=l.alternate;if(o===null){if(r=l.return,r!==null){n=r;continue}break}if(l.child===o.child){for(o=l.child;o;){if(o===n)return yi(l),e;if(o===r)return yi(l),t;o=o.sibling}throw Error(S(188))}if(n.return!==r.return)n=l,r=o;else{for(var s=!1,i=l.child;i;){if(i===n){s=!0,n=l,r=o;break}if(i===r){s=!0,r=l,n=o;break}i=i.sibling}if(!s){for(i=o.child;i;){if(i===n){s=!0,n=o,r=l;break}if(i===r){s=!0,r=o,n=l;break}i=i.sibling}if(!s)throw Error(S(189))}}if(n.alternate!==r)throw Error(S(190))}if(n.tag!==3)throw Error(S(188));return n.stateNode.current===n?e:t}function ea(e){return e=Ad(e),e!==null?ta(e):null}function ta(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=ta(e);if(t!==null)return t;e=e.sibling}return null}var na=Se.unstable_scheduleCallback,vi=Se.unstable_cancelCallback,Fd=Se.unstable_shouldYield,Ud=Se.unstable_requestPaint,G=Se.unstable_now,$d=Se.unstable_getCurrentPriorityLevel,xs=Se.unstable_ImmediatePriority,ra=Se.unstable_UserBlockingPriority,qr=Se.unstable_NormalPriority,Hd=Se.unstable_LowPriority,la=Se.unstable_IdlePriority,xl=null,He=null;function Bd(e){if(He&&typeof He.onCommitFiberRoot=="function")try{He.onCommitFiberRoot(xl,e,void 0,(e.current.flags&128)===128)}catch{}}var Ie=Math.clz32?Math.clz32:Wd,Vd=Math.log,Kd=Math.LN2;function Wd(e){return e>>>=0,e===0?32:31-(Vd(e)/Kd|0)|0}var Sr=64,kr=4194304;function Rn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function br(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,l=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var i=s&~l;i!==0?r=Rn(i):(o&=s,o!==0&&(r=Rn(o)))}else s=n&~l,s!==0?r=Rn(s):o!==0&&(r=Rn(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&l)&&(l=r&-r,o=t&-t,l>=o||l===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ie(t),l=1<<n,r|=e[n],t&=~l;return r}function Qd(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Gd(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-Ie(o),i=1<<s,u=l[s];u===-1?(!(i&n)||i&r)&&(l[s]=Qd(i,t)):u<=t&&(e.expiredLanes|=i),o&=~i}}function zo(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function oa(){var e=Sr;return Sr<<=1,!(Sr&4194240)&&(Sr=64),e}function Ql(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ir(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ie(t),e[t]=n}function Yd(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-Ie(n),o=1<<l;t[l]=0,r[l]=-1,e[l]=-1,n&=~o}}function Ss(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ie(n),l=1<<r;l&t|e[r]&t&&(e[r]|=t),n&=~l}}var I=0;function sa(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var ia,ks,ua,aa,ca,Oo=!1,Er=[],ut=null,at=null,ct=null,Wn=new Map,Qn=new Map,lt=[],Xd="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function gi(e,t){switch(e){case"focusin":case"focusout":ut=null;break;case"dragenter":case"dragleave":at=null;break;case"mouseover":case"mouseout":ct=null;break;case"pointerover":case"pointerout":Wn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Qn.delete(t.pointerId)}}function En(e,t,n,r,l,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[l]},t!==null&&(t=ar(t),t!==null&&ks(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function Jd(e,t,n,r,l){switch(t){case"focusin":return ut=En(ut,e,t,n,r,l),!0;case"dragenter":return at=En(at,e,t,n,r,l),!0;case"mouseover":return ct=En(ct,e,t,n,r,l),!0;case"pointerover":var o=l.pointerId;return Wn.set(o,En(Wn.get(o)||null,e,t,n,r,l)),!0;case"gotpointercapture":return o=l.pointerId,Qn.set(o,En(Qn.get(o)||null,e,t,n,r,l)),!0}return!1}function da(e){var t=_t(e.target);if(t!==null){var n=Ft(t);if(n!==null){if(t=n.tag,t===13){if(t=bu(n),t!==null){e.blockedOn=t,ca(e.priority,function(){ua(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Fr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Mo(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Po=r,n.target.dispatchEvent(r),Po=null}else return t=ar(n),t!==null&&ks(t),e.blockedOn=n,!1;t.shift()}return!0}function wi(e,t,n){Fr(e)&&n.delete(t)}function Zd(){Oo=!1,ut!==null&&Fr(ut)&&(ut=null),at!==null&&Fr(at)&&(at=null),ct!==null&&Fr(ct)&&(ct=null),Wn.forEach(wi),Qn.forEach(wi)}function Cn(e,t){e.blockedOn===t&&(e.blockedOn=null,Oo||(Oo=!0,Se.unstable_scheduleCallback(Se.unstable_NormalPriority,Zd)))}function Gn(e){function t(l){return Cn(l,e)}if(0<Er.length){Cn(Er[0],e);for(var n=1;n<Er.length;n++){var r=Er[n];r.blockedOn===e&&(r.blockedOn=null)}}for(ut!==null&&Cn(ut,e),at!==null&&Cn(at,e),ct!==null&&Cn(ct,e),Wn.forEach(t),Qn.forEach(t),n=0;n<lt.length;n++)r=lt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<lt.length&&(n=lt[0],n.blockedOn===null);)da(n),n.blockedOn===null&&lt.shift()}var nn=be.ReactCurrentBatchConfig,el=!0;function qd(e,t,n,r){var l=I,o=nn.transition;nn.transition=null;try{I=1,Es(e,t,n,r)}finally{I=l,nn.transition=o}}function bd(e,t,n,r){var l=I,o=nn.transition;nn.transition=null;try{I=4,Es(e,t,n,r)}finally{I=l,nn.transition=o}}function Es(e,t,n,r){if(el){var l=Mo(e,t,n,r);if(l===null)no(e,t,r,tl,n),gi(e,r);else if(Jd(l,e,t,n,r))r.stopPropagation();else if(gi(e,r),t&4&&-1<Xd.indexOf(e)){for(;l!==null;){var o=ar(l);if(o!==null&&ia(o),o=Mo(e,t,n,r),o===null&&no(e,t,r,tl,n),o===l)break;l=o}l!==null&&r.stopPropagation()}else no(e,t,r,null,n)}}var tl=null;function Mo(e,t,n,r){if(tl=null,e=ws(r),e=_t(e),e!==null)if(t=Ft(e),t===null)e=null;else if(n=t.tag,n===13){if(e=bu(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return tl=e,null}function fa(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch($d()){case xs:return 1;case ra:return 4;case qr:case Hd:return 16;case la:return 536870912;default:return 16}default:return 16}}var st=null,Cs=null,Ur=null;function pa(){if(Ur)return Ur;var e,t=Cs,n=t.length,r,l="value"in st?st.value:st.textContent,o=l.length;for(e=0;e<n&&t[e]===l[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===l[o-r];r++);return Ur=l.slice(e,1<r?1-r:void 0)}function $r(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Cr(){return!0}function xi(){return!1}function Ee(e){function t(n,r,l,o,s){this._reactName=n,this._targetInst=l,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var i in e)e.hasOwnProperty(i)&&(n=e[i],this[i]=n?n(o):o[i]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Cr:xi,this.isPropagationStopped=xi,this}return V(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Cr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Cr)},persist:function(){},isPersistent:Cr}),t}var yn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ns=Ee(yn),ur=V({},yn,{view:0,detail:0}),ef=Ee(ur),Gl,Yl,Nn,Sl=V({},ur,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:_s,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Nn&&(Nn&&e.type==="mousemove"?(Gl=e.screenX-Nn.screenX,Yl=e.screenY-Nn.screenY):Yl=Gl=0,Nn=e),Gl)},movementY:function(e){return"movementY"in e?e.movementY:Yl}}),Si=Ee(Sl),tf=V({},Sl,{dataTransfer:0}),nf=Ee(tf),rf=V({},ur,{relatedTarget:0}),Xl=Ee(rf),lf=V({},yn,{animationName:0,elapsedTime:0,pseudoElement:0}),of=Ee(lf),sf=V({},yn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),uf=Ee(sf),af=V({},yn,{data:0}),ki=Ee(af),cf={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},df={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ff={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function pf(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=ff[e])?!!t[e]:!1}function _s(){return pf}var mf=V({},ur,{key:function(e){if(e.key){var t=cf[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=$r(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?df[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:_s,charCode:function(e){return e.type==="keypress"?$r(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?$r(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),hf=Ee(mf),yf=V({},Sl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ei=Ee(yf),vf=V({},ur,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:_s}),gf=Ee(vf),wf=V({},yn,{propertyName:0,elapsedTime:0,pseudoElement:0}),xf=Ee(wf),Sf=V({},Sl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),kf=Ee(Sf),Ef=[9,13,27,32],js=Xe&&"CompositionEvent"in window,In=null;Xe&&"documentMode"in document&&(In=document.documentMode);var Cf=Xe&&"TextEvent"in window&&!In,ma=Xe&&(!js||In&&8<In&&11>=In),Ci=" ",Ni=!1;function ha(e,t){switch(e){case"keyup":return Ef.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ya(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Vt=!1;function Nf(e,t){switch(e){case"compositionend":return ya(t);case"keypress":return t.which!==32?null:(Ni=!0,Ci);case"textInput":return e=t.data,e===Ci&&Ni?null:e;default:return null}}function _f(e,t){if(Vt)return e==="compositionend"||!js&&ha(e,t)?(e=pa(),Ur=Cs=st=null,Vt=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ma&&t.locale!=="ko"?null:t.data;default:return null}}var jf={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function _i(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!jf[e.type]:t==="textarea"}function va(e,t,n,r){Yu(r),t=nl(t,"onChange"),0<t.length&&(n=new Ns("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Dn=null,Yn=null;function Pf(e){Pa(e,0)}function kl(e){var t=Qt(e);if(Hu(t))return e}function Tf(e,t){if(e==="change")return t}var ga=!1;if(Xe){var Jl;if(Xe){var Zl="oninput"in document;if(!Zl){var ji=document.createElement("div");ji.setAttribute("oninput","return;"),Zl=typeof ji.oninput=="function"}Jl=Zl}else Jl=!1;ga=Jl&&(!document.documentMode||9<document.documentMode)}function Pi(){Dn&&(Dn.detachEvent("onpropertychange",wa),Yn=Dn=null)}function wa(e){if(e.propertyName==="value"&&kl(Yn)){var t=[];va(t,Yn,e,ws(e)),qu(Pf,t)}}function Lf(e,t,n){e==="focusin"?(Pi(),Dn=t,Yn=n,Dn.attachEvent("onpropertychange",wa)):e==="focusout"&&Pi()}function Rf(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return kl(Yn)}function zf(e,t){if(e==="click")return kl(t)}function Of(e,t){if(e==="input"||e==="change")return kl(t)}function Mf(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ae=typeof Object.is=="function"?Object.is:Mf;function Xn(e,t){if(Ae(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var l=n[r];if(!yo.call(t,l)||!Ae(e[l],t[l]))return!1}return!0}function Ti(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Li(e,t){var n=Ti(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ti(n)}}function xa(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?xa(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Sa(){for(var e=window,t=Xr();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Xr(e.document)}return t}function Ps(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function If(e){var t=Sa(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&xa(n.ownerDocument.documentElement,n)){if(r!==null&&Ps(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,o=Math.min(r.start,l);r=r.end===void 0?o:Math.min(r.end,l),!e.extend&&o>r&&(l=r,r=o,o=l),l=Li(n,o);var s=Li(n,r);l&&s&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Df=Xe&&"documentMode"in document&&11>=document.documentMode,Kt=null,Io=null,An=null,Do=!1;function Ri(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Do||Kt==null||Kt!==Xr(r)||(r=Kt,"selectionStart"in r&&Ps(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),An&&Xn(An,r)||(An=r,r=nl(Io,"onSelect"),0<r.length&&(t=new Ns("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Kt)))}function Nr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Wt={animationend:Nr("Animation","AnimationEnd"),animationiteration:Nr("Animation","AnimationIteration"),animationstart:Nr("Animation","AnimationStart"),transitionend:Nr("Transition","TransitionEnd")},ql={},ka={};Xe&&(ka=document.createElement("div").style,"AnimationEvent"in window||(delete Wt.animationend.animation,delete Wt.animationiteration.animation,delete Wt.animationstart.animation),"TransitionEvent"in window||delete Wt.transitionend.transition);function El(e){if(ql[e])return ql[e];if(!Wt[e])return e;var t=Wt[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in ka)return ql[e]=t[n];return e}var Ea=El("animationend"),Ca=El("animationiteration"),Na=El("animationstart"),_a=El("transitionend"),ja=new Map,zi="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function gt(e,t){ja.set(e,t),At(t,[e])}for(var bl=0;bl<zi.length;bl++){var eo=zi[bl],Af=eo.toLowerCase(),Ff=eo[0].toUpperCase()+eo.slice(1);gt(Af,"on"+Ff)}gt(Ea,"onAnimationEnd");gt(Ca,"onAnimationIteration");gt(Na,"onAnimationStart");gt("dblclick","onDoubleClick");gt("focusin","onFocus");gt("focusout","onBlur");gt(_a,"onTransitionEnd");sn("onMouseEnter",["mouseout","mouseover"]);sn("onMouseLeave",["mouseout","mouseover"]);sn("onPointerEnter",["pointerout","pointerover"]);sn("onPointerLeave",["pointerout","pointerover"]);At("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));At("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));At("onBeforeInput",["compositionend","keypress","textInput","paste"]);At("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));At("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));At("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Uf=new Set("cancel close invalid load scroll toggle".split(" ").concat(zn));function Oi(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Dd(r,t,void 0,e),e.currentTarget=null}function Pa(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],l=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var i=r[s],u=i.instance,c=i.currentTarget;if(i=i.listener,u!==o&&l.isPropagationStopped())break e;Oi(l,i,c),o=u}else for(s=0;s<r.length;s++){if(i=r[s],u=i.instance,c=i.currentTarget,i=i.listener,u!==o&&l.isPropagationStopped())break e;Oi(l,i,c),o=u}}}if(Zr)throw e=Ro,Zr=!1,Ro=null,e}function F(e,t){var n=t[Ho];n===void 0&&(n=t[Ho]=new Set);var r=e+"__bubble";n.has(r)||(Ta(t,e,2,!1),n.add(r))}function to(e,t,n){var r=0;t&&(r|=4),Ta(n,e,r,t)}var _r="_reactListening"+Math.random().toString(36).slice(2);function Jn(e){if(!e[_r]){e[_r]=!0,Du.forEach(function(n){n!=="selectionchange"&&(Uf.has(n)||to(n,!1,e),to(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[_r]||(t[_r]=!0,to("selectionchange",!1,t))}}function Ta(e,t,n,r){switch(fa(t)){case 1:var l=qd;break;case 4:l=bd;break;default:l=Es}n=l.bind(null,t,n,e),l=void 0,!Lo||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),r?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function no(e,t,n,r,l){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var i=r.stateNode.containerInfo;if(i===l||i.nodeType===8&&i.parentNode===l)break;if(s===4)for(s=r.return;s!==null;){var u=s.tag;if((u===3||u===4)&&(u=s.stateNode.containerInfo,u===l||u.nodeType===8&&u.parentNode===l))return;s=s.return}for(;i!==null;){if(s=_t(i),s===null)return;if(u=s.tag,u===5||u===6){r=o=s;continue e}i=i.parentNode}}r=r.return}qu(function(){var c=o,m=ws(n),y=[];e:{var h=ja.get(e);if(h!==void 0){var g=Ns,v=e;switch(e){case"keypress":if($r(n)===0)break e;case"keydown":case"keyup":g=hf;break;case"focusin":v="focus",g=Xl;break;case"focusout":v="blur",g=Xl;break;case"beforeblur":case"afterblur":g=Xl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=Si;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=nf;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=gf;break;case Ea:case Ca:case Na:g=of;break;case _a:g=xf;break;case"scroll":g=ef;break;case"wheel":g=kf;break;case"copy":case"cut":case"paste":g=uf;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=Ei}var w=(t&4)!==0,k=!w&&e==="scroll",d=w?h!==null?h+"Capture":null:h;w=[];for(var a=c,p;a!==null;){p=a;var x=p.stateNode;if(p.tag===5&&x!==null&&(p=x,d!==null&&(x=Kn(a,d),x!=null&&w.push(Zn(a,x,p)))),k)break;a=a.return}0<w.length&&(h=new g(h,v,null,n,m),y.push({event:h,listeners:w}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",h&&n!==Po&&(v=n.relatedTarget||n.fromElement)&&(_t(v)||v[Je]))break e;if((g||h)&&(h=m.window===m?m:(h=m.ownerDocument)?h.defaultView||h.parentWindow:window,g?(v=n.relatedTarget||n.toElement,g=c,v=v?_t(v):null,v!==null&&(k=Ft(v),v!==k||v.tag!==5&&v.tag!==6)&&(v=null)):(g=null,v=c),g!==v)){if(w=Si,x="onMouseLeave",d="onMouseEnter",a="mouse",(e==="pointerout"||e==="pointerover")&&(w=Ei,x="onPointerLeave",d="onPointerEnter",a="pointer"),k=g==null?h:Qt(g),p=v==null?h:Qt(v),h=new w(x,a+"leave",g,n,m),h.target=k,h.relatedTarget=p,x=null,_t(m)===c&&(w=new w(d,a+"enter",v,n,m),w.target=p,w.relatedTarget=k,x=w),k=x,g&&v)t:{for(w=g,d=v,a=0,p=w;p;p=Ut(p))a++;for(p=0,x=d;x;x=Ut(x))p++;for(;0<a-p;)w=Ut(w),a--;for(;0<p-a;)d=Ut(d),p--;for(;a--;){if(w===d||d!==null&&w===d.alternate)break t;w=Ut(w),d=Ut(d)}w=null}else w=null;g!==null&&Mi(y,h,g,w,!1),v!==null&&k!==null&&Mi(y,k,v,w,!0)}}e:{if(h=c?Qt(c):window,g=h.nodeName&&h.nodeName.toLowerCase(),g==="select"||g==="input"&&h.type==="file")var E=Tf;else if(_i(h))if(ga)E=Of;else{E=Rf;var j=Lf}else(g=h.nodeName)&&g.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(E=zf);if(E&&(E=E(e,c))){va(y,E,n,m);break e}j&&j(e,h,c),e==="focusout"&&(j=h._wrapperState)&&j.controlled&&h.type==="number"&&Eo(h,"number",h.value)}switch(j=c?Qt(c):window,e){case"focusin":(_i(j)||j.contentEditable==="true")&&(Kt=j,Io=c,An=null);break;case"focusout":An=Io=Kt=null;break;case"mousedown":Do=!0;break;case"contextmenu":case"mouseup":case"dragend":Do=!1,Ri(y,n,m);break;case"selectionchange":if(Df)break;case"keydown":case"keyup":Ri(y,n,m)}var _;if(js)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else Vt?ha(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(ma&&n.locale!=="ko"&&(Vt||P!=="onCompositionStart"?P==="onCompositionEnd"&&Vt&&(_=pa()):(st=m,Cs="value"in st?st.value:st.textContent,Vt=!0)),j=nl(c,P),0<j.length&&(P=new ki(P,e,null,n,m),y.push({event:P,listeners:j}),_?P.data=_:(_=ya(n),_!==null&&(P.data=_)))),(_=Cf?Nf(e,n):_f(e,n))&&(c=nl(c,"onBeforeInput"),0<c.length&&(m=new ki("onBeforeInput","beforeinput",null,n,m),y.push({event:m,listeners:c}),m.data=_))}Pa(y,t)})}function Zn(e,t,n){return{instance:e,listener:t,currentTarget:n}}function nl(e,t){for(var n=t+"Capture",r=[];e!==null;){var l=e,o=l.stateNode;l.tag===5&&o!==null&&(l=o,o=Kn(e,n),o!=null&&r.unshift(Zn(e,o,l)),o=Kn(e,t),o!=null&&r.push(Zn(e,o,l))),e=e.return}return r}function Ut(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Mi(e,t,n,r,l){for(var o=t._reactName,s=[];n!==null&&n!==r;){var i=n,u=i.alternate,c=i.stateNode;if(u!==null&&u===r)break;i.tag===5&&c!==null&&(i=c,l?(u=Kn(n,o),u!=null&&s.unshift(Zn(n,u,i))):l||(u=Kn(n,o),u!=null&&s.push(Zn(n,u,i)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var $f=/\r\n?/g,Hf=/\u0000|\uFFFD/g;function Ii(e){return(typeof e=="string"?e:""+e).replace($f,`
`).replace(Hf,"")}function jr(e,t,n){if(t=Ii(t),Ii(e)!==t&&n)throw Error(S(425))}function rl(){}var Ao=null,Fo=null;function Uo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var $o=typeof setTimeout=="function"?setTimeout:void 0,Bf=typeof clearTimeout=="function"?clearTimeout:void 0,Di=typeof Promise=="function"?Promise:void 0,Vf=typeof queueMicrotask=="function"?queueMicrotask:typeof Di<"u"?function(e){return Di.resolve(null).then(e).catch(Kf)}:$o;function Kf(e){setTimeout(function(){throw e})}function ro(e,t){var n=t,r=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(r===0){e.removeChild(l),Gn(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=l}while(n);Gn(t)}function dt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Ai(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var vn=Math.random().toString(36).slice(2),$e="__reactFiber$"+vn,qn="__reactProps$"+vn,Je="__reactContainer$"+vn,Ho="__reactEvents$"+vn,Wf="__reactListeners$"+vn,Qf="__reactHandles$"+vn;function _t(e){var t=e[$e];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Je]||n[$e]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Ai(e);e!==null;){if(n=e[$e])return n;e=Ai(e)}return t}e=n,n=e.parentNode}return null}function ar(e){return e=e[$e]||e[Je],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Qt(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(S(33))}function Cl(e){return e[qn]||null}var Bo=[],Gt=-1;function wt(e){return{current:e}}function U(e){0>Gt||(e.current=Bo[Gt],Bo[Gt]=null,Gt--)}function A(e,t){Gt++,Bo[Gt]=e.current,e.current=t}var vt={},ie=wt(vt),he=wt(!1),Rt=vt;function un(e,t){var n=e.type.contextTypes;if(!n)return vt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var l={},o;for(o in n)l[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function ye(e){return e=e.childContextTypes,e!=null}function ll(){U(he),U(ie)}function Fi(e,t,n){if(ie.current!==vt)throw Error(S(168));A(ie,t),A(he,n)}function La(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var l in r)if(!(l in t))throw Error(S(108,Td(e)||"Unknown",l));return V({},n,r)}function ol(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||vt,Rt=ie.current,A(ie,e),A(he,he.current),!0}function Ui(e,t,n){var r=e.stateNode;if(!r)throw Error(S(169));n?(e=La(e,t,Rt),r.__reactInternalMemoizedMergedChildContext=e,U(he),U(ie),A(ie,e)):U(he),A(he,n)}var Ke=null,Nl=!1,lo=!1;function Ra(e){Ke===null?Ke=[e]:Ke.push(e)}function Gf(e){Nl=!0,Ra(e)}function xt(){if(!lo&&Ke!==null){lo=!0;var e=0,t=I;try{var n=Ke;for(I=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Ke=null,Nl=!1}catch(l){throw Ke!==null&&(Ke=Ke.slice(e+1)),na(xs,xt),l}finally{I=t,lo=!1}}return null}var Yt=[],Xt=0,sl=null,il=0,Ce=[],Ne=0,zt=null,We=1,Qe="";function Ct(e,t){Yt[Xt++]=il,Yt[Xt++]=sl,sl=e,il=t}function za(e,t,n){Ce[Ne++]=We,Ce[Ne++]=Qe,Ce[Ne++]=zt,zt=e;var r=We;e=Qe;var l=32-Ie(r)-1;r&=~(1<<l),n+=1;var o=32-Ie(t)+l;if(30<o){var s=l-l%5;o=(r&(1<<s)-1).toString(32),r>>=s,l-=s,We=1<<32-Ie(t)+l|n<<l|r,Qe=o+e}else We=1<<o|n<<l|r,Qe=e}function Ts(e){e.return!==null&&(Ct(e,1),za(e,1,0))}function Ls(e){for(;e===sl;)sl=Yt[--Xt],Yt[Xt]=null,il=Yt[--Xt],Yt[Xt]=null;for(;e===zt;)zt=Ce[--Ne],Ce[Ne]=null,Qe=Ce[--Ne],Ce[Ne]=null,We=Ce[--Ne],Ce[Ne]=null}var xe=null,we=null,$=!1,Me=null;function Oa(e,t){var n=_e(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function $i(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,xe=e,we=dt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,xe=e,we=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=zt!==null?{id:We,overflow:Qe}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=_e(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,xe=e,we=null,!0):!1;default:return!1}}function Vo(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ko(e){if($){var t=we;if(t){var n=t;if(!$i(e,t)){if(Vo(e))throw Error(S(418));t=dt(n.nextSibling);var r=xe;t&&$i(e,t)?Oa(r,n):(e.flags=e.flags&-4097|2,$=!1,xe=e)}}else{if(Vo(e))throw Error(S(418));e.flags=e.flags&-4097|2,$=!1,xe=e}}}function Hi(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;xe=e}function Pr(e){if(e!==xe)return!1;if(!$)return Hi(e),$=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Uo(e.type,e.memoizedProps)),t&&(t=we)){if(Vo(e))throw Ma(),Error(S(418));for(;t;)Oa(e,t),t=dt(t.nextSibling)}if(Hi(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(S(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){we=dt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}we=null}}else we=xe?dt(e.stateNode.nextSibling):null;return!0}function Ma(){for(var e=we;e;)e=dt(e.nextSibling)}function an(){we=xe=null,$=!1}function Rs(e){Me===null?Me=[e]:Me.push(e)}var Yf=be.ReactCurrentBatchConfig;function _n(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(S(309));var r=n.stateNode}if(!r)throw Error(S(147,e));var l=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var i=l.refs;s===null?delete i[o]:i[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(S(284));if(!n._owner)throw Error(S(290,e))}return e}function Tr(e,t){throw e=Object.prototype.toString.call(t),Error(S(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Bi(e){var t=e._init;return t(e._payload)}function Ia(e){function t(d,a){if(e){var p=d.deletions;p===null?(d.deletions=[a],d.flags|=16):p.push(a)}}function n(d,a){if(!e)return null;for(;a!==null;)t(d,a),a=a.sibling;return null}function r(d,a){for(d=new Map;a!==null;)a.key!==null?d.set(a.key,a):d.set(a.index,a),a=a.sibling;return d}function l(d,a){return d=ht(d,a),d.index=0,d.sibling=null,d}function o(d,a,p){return d.index=p,e?(p=d.alternate,p!==null?(p=p.index,p<a?(d.flags|=2,a):p):(d.flags|=2,a)):(d.flags|=1048576,a)}function s(d){return e&&d.alternate===null&&(d.flags|=2),d}function i(d,a,p,x){return a===null||a.tag!==6?(a=fo(p,d.mode,x),a.return=d,a):(a=l(a,p),a.return=d,a)}function u(d,a,p,x){var E=p.type;return E===Bt?m(d,a,p.props.children,x,p.key):a!==null&&(a.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===nt&&Bi(E)===a.type)?(x=l(a,p.props),x.ref=_n(d,a,p),x.return=d,x):(x=Gr(p.type,p.key,p.props,null,d.mode,x),x.ref=_n(d,a,p),x.return=d,x)}function c(d,a,p,x){return a===null||a.tag!==4||a.stateNode.containerInfo!==p.containerInfo||a.stateNode.implementation!==p.implementation?(a=po(p,d.mode,x),a.return=d,a):(a=l(a,p.children||[]),a.return=d,a)}function m(d,a,p,x,E){return a===null||a.tag!==7?(a=Lt(p,d.mode,x,E),a.return=d,a):(a=l(a,p),a.return=d,a)}function y(d,a,p){if(typeof a=="string"&&a!==""||typeof a=="number")return a=fo(""+a,d.mode,p),a.return=d,a;if(typeof a=="object"&&a!==null){switch(a.$$typeof){case gr:return p=Gr(a.type,a.key,a.props,null,d.mode,p),p.ref=_n(d,null,a),p.return=d,p;case Ht:return a=po(a,d.mode,p),a.return=d,a;case nt:var x=a._init;return y(d,x(a._payload),p)}if(Ln(a)||Sn(a))return a=Lt(a,d.mode,p,null),a.return=d,a;Tr(d,a)}return null}function h(d,a,p,x){var E=a!==null?a.key:null;if(typeof p=="string"&&p!==""||typeof p=="number")return E!==null?null:i(d,a,""+p,x);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case gr:return p.key===E?u(d,a,p,x):null;case Ht:return p.key===E?c(d,a,p,x):null;case nt:return E=p._init,h(d,a,E(p._payload),x)}if(Ln(p)||Sn(p))return E!==null?null:m(d,a,p,x,null);Tr(d,p)}return null}function g(d,a,p,x,E){if(typeof x=="string"&&x!==""||typeof x=="number")return d=d.get(p)||null,i(a,d,""+x,E);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case gr:return d=d.get(x.key===null?p:x.key)||null,u(a,d,x,E);case Ht:return d=d.get(x.key===null?p:x.key)||null,c(a,d,x,E);case nt:var j=x._init;return g(d,a,p,j(x._payload),E)}if(Ln(x)||Sn(x))return d=d.get(p)||null,m(a,d,x,E,null);Tr(a,x)}return null}function v(d,a,p,x){for(var E=null,j=null,_=a,P=a=0,W=null;_!==null&&P<p.length;P++){_.index>P?(W=_,_=null):W=_.sibling;var O=h(d,_,p[P],x);if(O===null){_===null&&(_=W);break}e&&_&&O.alternate===null&&t(d,_),a=o(O,a,P),j===null?E=O:j.sibling=O,j=O,_=W}if(P===p.length)return n(d,_),$&&Ct(d,P),E;if(_===null){for(;P<p.length;P++)_=y(d,p[P],x),_!==null&&(a=o(_,a,P),j===null?E=_:j.sibling=_,j=_);return $&&Ct(d,P),E}for(_=r(d,_);P<p.length;P++)W=g(_,d,P,p[P],x),W!==null&&(e&&W.alternate!==null&&_.delete(W.key===null?P:W.key),a=o(W,a,P),j===null?E=W:j.sibling=W,j=W);return e&&_.forEach(function(Le){return t(d,Le)}),$&&Ct(d,P),E}function w(d,a,p,x){var E=Sn(p);if(typeof E!="function")throw Error(S(150));if(p=E.call(p),p==null)throw Error(S(151));for(var j=E=null,_=a,P=a=0,W=null,O=p.next();_!==null&&!O.done;P++,O=p.next()){_.index>P?(W=_,_=null):W=_.sibling;var Le=h(d,_,O.value,x);if(Le===null){_===null&&(_=W);break}e&&_&&Le.alternate===null&&t(d,_),a=o(Le,a,P),j===null?E=Le:j.sibling=Le,j=Le,_=W}if(O.done)return n(d,_),$&&Ct(d,P),E;if(_===null){for(;!O.done;P++,O=p.next())O=y(d,O.value,x),O!==null&&(a=o(O,a,P),j===null?E=O:j.sibling=O,j=O);return $&&Ct(d,P),E}for(_=r(d,_);!O.done;P++,O=p.next())O=g(_,d,P,O.value,x),O!==null&&(e&&O.alternate!==null&&_.delete(O.key===null?P:O.key),a=o(O,a,P),j===null?E=O:j.sibling=O,j=O);return e&&_.forEach(function(wn){return t(d,wn)}),$&&Ct(d,P),E}function k(d,a,p,x){if(typeof p=="object"&&p!==null&&p.type===Bt&&p.key===null&&(p=p.props.children),typeof p=="object"&&p!==null){switch(p.$$typeof){case gr:e:{for(var E=p.key,j=a;j!==null;){if(j.key===E){if(E=p.type,E===Bt){if(j.tag===7){n(d,j.sibling),a=l(j,p.props.children),a.return=d,d=a;break e}}else if(j.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===nt&&Bi(E)===j.type){n(d,j.sibling),a=l(j,p.props),a.ref=_n(d,j,p),a.return=d,d=a;break e}n(d,j);break}else t(d,j);j=j.sibling}p.type===Bt?(a=Lt(p.props.children,d.mode,x,p.key),a.return=d,d=a):(x=Gr(p.type,p.key,p.props,null,d.mode,x),x.ref=_n(d,a,p),x.return=d,d=x)}return s(d);case Ht:e:{for(j=p.key;a!==null;){if(a.key===j)if(a.tag===4&&a.stateNode.containerInfo===p.containerInfo&&a.stateNode.implementation===p.implementation){n(d,a.sibling),a=l(a,p.children||[]),a.return=d,d=a;break e}else{n(d,a);break}else t(d,a);a=a.sibling}a=po(p,d.mode,x),a.return=d,d=a}return s(d);case nt:return j=p._init,k(d,a,j(p._payload),x)}if(Ln(p))return v(d,a,p,x);if(Sn(p))return w(d,a,p,x);Tr(d,p)}return typeof p=="string"&&p!==""||typeof p=="number"?(p=""+p,a!==null&&a.tag===6?(n(d,a.sibling),a=l(a,p),a.return=d,d=a):(n(d,a),a=fo(p,d.mode,x),a.return=d,d=a),s(d)):n(d,a)}return k}var cn=Ia(!0),Da=Ia(!1),ul=wt(null),al=null,Jt=null,zs=null;function Os(){zs=Jt=al=null}function Ms(e){var t=ul.current;U(ul),e._currentValue=t}function Wo(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function rn(e,t){al=e,zs=Jt=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(me=!0),e.firstContext=null)}function Pe(e){var t=e._currentValue;if(zs!==e)if(e={context:e,memoizedValue:t,next:null},Jt===null){if(al===null)throw Error(S(308));Jt=e,al.dependencies={lanes:0,firstContext:e}}else Jt=Jt.next=e;return t}var jt=null;function Is(e){jt===null?jt=[e]:jt.push(e)}function Aa(e,t,n,r){var l=t.interleaved;return l===null?(n.next=n,Is(t)):(n.next=l.next,l.next=n),t.interleaved=n,Ze(e,r)}function Ze(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var rt=!1;function Ds(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Fa(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ge(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function ft(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,M&2){var l=r.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),r.pending=t,Ze(e,n)}return l=r.interleaved,l===null?(t.next=t,Is(r)):(t.next=l.next,l.next=t),r.interleaved=t,Ze(e,n)}function Hr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ss(e,n)}}function Vi(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var l=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?l=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?l=o=t:o=o.next=t}else l=o=t;n={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function cl(e,t,n,r){var l=e.updateQueue;rt=!1;var o=l.firstBaseUpdate,s=l.lastBaseUpdate,i=l.shared.pending;if(i!==null){l.shared.pending=null;var u=i,c=u.next;u.next=null,s===null?o=c:s.next=c,s=u;var m=e.alternate;m!==null&&(m=m.updateQueue,i=m.lastBaseUpdate,i!==s&&(i===null?m.firstBaseUpdate=c:i.next=c,m.lastBaseUpdate=u))}if(o!==null){var y=l.baseState;s=0,m=c=u=null,i=o;do{var h=i.lane,g=i.eventTime;if((r&h)===h){m!==null&&(m=m.next={eventTime:g,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var v=e,w=i;switch(h=t,g=n,w.tag){case 1:if(v=w.payload,typeof v=="function"){y=v.call(g,y,h);break e}y=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=w.payload,h=typeof v=="function"?v.call(g,y,h):v,h==null)break e;y=V({},y,h);break e;case 2:rt=!0}}i.callback!==null&&i.lane!==0&&(e.flags|=64,h=l.effects,h===null?l.effects=[i]:h.push(i))}else g={eventTime:g,lane:h,tag:i.tag,payload:i.payload,callback:i.callback,next:null},m===null?(c=m=g,u=y):m=m.next=g,s|=h;if(i=i.next,i===null){if(i=l.shared.pending,i===null)break;h=i,i=h.next,h.next=null,l.lastBaseUpdate=h,l.shared.pending=null}}while(!0);if(m===null&&(u=y),l.baseState=u,l.firstBaseUpdate=c,l.lastBaseUpdate=m,t=l.shared.interleaved,t!==null){l=t;do s|=l.lane,l=l.next;while(l!==t)}else o===null&&(l.shared.lanes=0);Mt|=s,e.lanes=s,e.memoizedState=y}}function Ki(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],l=r.callback;if(l!==null){if(r.callback=null,r=n,typeof l!="function")throw Error(S(191,l));l.call(r)}}}var cr={},Be=wt(cr),bn=wt(cr),er=wt(cr);function Pt(e){if(e===cr)throw Error(S(174));return e}function As(e,t){switch(A(er,t),A(bn,e),A(Be,cr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:No(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=No(t,e)}U(Be),A(Be,t)}function dn(){U(Be),U(bn),U(er)}function Ua(e){Pt(er.current);var t=Pt(Be.current),n=No(t,e.type);t!==n&&(A(bn,e),A(Be,n))}function Fs(e){bn.current===e&&(U(Be),U(bn))}var H=wt(0);function dl(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var oo=[];function Us(){for(var e=0;e<oo.length;e++)oo[e]._workInProgressVersionPrimary=null;oo.length=0}var Br=be.ReactCurrentDispatcher,so=be.ReactCurrentBatchConfig,Ot=0,B=null,X=null,q=null,fl=!1,Fn=!1,tr=0,Xf=0;function le(){throw Error(S(321))}function $s(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ae(e[n],t[n]))return!1;return!0}function Hs(e,t,n,r,l,o){if(Ot=o,B=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Br.current=e===null||e.memoizedState===null?bf:ep,e=n(r,l),Fn){o=0;do{if(Fn=!1,tr=0,25<=o)throw Error(S(301));o+=1,q=X=null,t.updateQueue=null,Br.current=tp,e=n(r,l)}while(Fn)}if(Br.current=pl,t=X!==null&&X.next!==null,Ot=0,q=X=B=null,fl=!1,t)throw Error(S(300));return e}function Bs(){var e=tr!==0;return tr=0,e}function Ue(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return q===null?B.memoizedState=q=e:q=q.next=e,q}function Te(){if(X===null){var e=B.alternate;e=e!==null?e.memoizedState:null}else e=X.next;var t=q===null?B.memoizedState:q.next;if(t!==null)q=t,X=e;else{if(e===null)throw Error(S(310));X=e,e={memoizedState:X.memoizedState,baseState:X.baseState,baseQueue:X.baseQueue,queue:X.queue,next:null},q===null?B.memoizedState=q=e:q=q.next=e}return q}function nr(e,t){return typeof t=="function"?t(e):t}function io(e){var t=Te(),n=t.queue;if(n===null)throw Error(S(311));n.lastRenderedReducer=e;var r=X,l=r.baseQueue,o=n.pending;if(o!==null){if(l!==null){var s=l.next;l.next=o.next,o.next=s}r.baseQueue=l=o,n.pending=null}if(l!==null){o=l.next,r=r.baseState;var i=s=null,u=null,c=o;do{var m=c.lane;if((Ot&m)===m)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var y={lane:m,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(i=u=y,s=r):u=u.next=y,B.lanes|=m,Mt|=m}c=c.next}while(c!==null&&c!==o);u===null?s=r:u.next=i,Ae(r,t.memoizedState)||(me=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){l=e;do o=l.lane,B.lanes|=o,Mt|=o,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function uo(e){var t=Te(),n=t.queue;if(n===null)throw Error(S(311));n.lastRenderedReducer=e;var r=n.dispatch,l=n.pending,o=t.memoizedState;if(l!==null){n.pending=null;var s=l=l.next;do o=e(o,s.action),s=s.next;while(s!==l);Ae(o,t.memoizedState)||(me=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function $a(){}function Ha(e,t){var n=B,r=Te(),l=t(),o=!Ae(r.memoizedState,l);if(o&&(r.memoizedState=l,me=!0),r=r.queue,Vs(Ka.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||q!==null&&q.memoizedState.tag&1){if(n.flags|=2048,rr(9,Va.bind(null,n,r,l,t),void 0,null),b===null)throw Error(S(349));Ot&30||Ba(n,t,l)}return l}function Ba(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=B.updateQueue,t===null?(t={lastEffect:null,stores:null},B.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Va(e,t,n,r){t.value=n,t.getSnapshot=r,Wa(t)&&Qa(e)}function Ka(e,t,n){return n(function(){Wa(t)&&Qa(e)})}function Wa(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ae(e,n)}catch{return!0}}function Qa(e){var t=Ze(e,1);t!==null&&De(t,e,1,-1)}function Wi(e){var t=Ue();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:nr,lastRenderedState:e},t.queue=e,e=e.dispatch=qf.bind(null,B,e),[t.memoizedState,e]}function rr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=B.updateQueue,t===null?(t={lastEffect:null,stores:null},B.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Ga(){return Te().memoizedState}function Vr(e,t,n,r){var l=Ue();B.flags|=e,l.memoizedState=rr(1|t,n,void 0,r===void 0?null:r)}function _l(e,t,n,r){var l=Te();r=r===void 0?null:r;var o=void 0;if(X!==null){var s=X.memoizedState;if(o=s.destroy,r!==null&&$s(r,s.deps)){l.memoizedState=rr(t,n,o,r);return}}B.flags|=e,l.memoizedState=rr(1|t,n,o,r)}function Qi(e,t){return Vr(8390656,8,e,t)}function Vs(e,t){return _l(2048,8,e,t)}function Ya(e,t){return _l(4,2,e,t)}function Xa(e,t){return _l(4,4,e,t)}function Ja(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Za(e,t,n){return n=n!=null?n.concat([e]):null,_l(4,4,Ja.bind(null,t,e),n)}function Ks(){}function qa(e,t){var n=Te();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&$s(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function ba(e,t){var n=Te();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&$s(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function ec(e,t,n){return Ot&21?(Ae(n,t)||(n=oa(),B.lanes|=n,Mt|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,me=!0),e.memoizedState=n)}function Jf(e,t){var n=I;I=n!==0&&4>n?n:4,e(!0);var r=so.transition;so.transition={};try{e(!1),t()}finally{I=n,so.transition=r}}function tc(){return Te().memoizedState}function Zf(e,t,n){var r=mt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},nc(e))rc(t,n);else if(n=Aa(e,t,n,r),n!==null){var l=ce();De(n,e,r,l),lc(n,t,r)}}function qf(e,t,n){var r=mt(e),l={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(nc(e))rc(t,l);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,i=o(s,n);if(l.hasEagerState=!0,l.eagerState=i,Ae(i,s)){var u=t.interleaved;u===null?(l.next=l,Is(t)):(l.next=u.next,u.next=l),t.interleaved=l;return}}catch{}finally{}n=Aa(e,t,l,r),n!==null&&(l=ce(),De(n,e,r,l),lc(n,t,r))}}function nc(e){var t=e.alternate;return e===B||t!==null&&t===B}function rc(e,t){Fn=fl=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function lc(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ss(e,n)}}var pl={readContext:Pe,useCallback:le,useContext:le,useEffect:le,useImperativeHandle:le,useInsertionEffect:le,useLayoutEffect:le,useMemo:le,useReducer:le,useRef:le,useState:le,useDebugValue:le,useDeferredValue:le,useTransition:le,useMutableSource:le,useSyncExternalStore:le,useId:le,unstable_isNewReconciler:!1},bf={readContext:Pe,useCallback:function(e,t){return Ue().memoizedState=[e,t===void 0?null:t],e},useContext:Pe,useEffect:Qi,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Vr(4194308,4,Ja.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Vr(4194308,4,e,t)},useInsertionEffect:function(e,t){return Vr(4,2,e,t)},useMemo:function(e,t){var n=Ue();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ue();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Zf.bind(null,B,e),[r.memoizedState,e]},useRef:function(e){var t=Ue();return e={current:e},t.memoizedState=e},useState:Wi,useDebugValue:Ks,useDeferredValue:function(e){return Ue().memoizedState=e},useTransition:function(){var e=Wi(!1),t=e[0];return e=Jf.bind(null,e[1]),Ue().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=B,l=Ue();if($){if(n===void 0)throw Error(S(407));n=n()}else{if(n=t(),b===null)throw Error(S(349));Ot&30||Ba(r,t,n)}l.memoizedState=n;var o={value:n,getSnapshot:t};return l.queue=o,Qi(Ka.bind(null,r,o,e),[e]),r.flags|=2048,rr(9,Va.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=Ue(),t=b.identifierPrefix;if($){var n=Qe,r=We;n=(r&~(1<<32-Ie(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=tr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Xf++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ep={readContext:Pe,useCallback:qa,useContext:Pe,useEffect:Vs,useImperativeHandle:Za,useInsertionEffect:Ya,useLayoutEffect:Xa,useMemo:ba,useReducer:io,useRef:Ga,useState:function(){return io(nr)},useDebugValue:Ks,useDeferredValue:function(e){var t=Te();return ec(t,X.memoizedState,e)},useTransition:function(){var e=io(nr)[0],t=Te().memoizedState;return[e,t]},useMutableSource:$a,useSyncExternalStore:Ha,useId:tc,unstable_isNewReconciler:!1},tp={readContext:Pe,useCallback:qa,useContext:Pe,useEffect:Vs,useImperativeHandle:Za,useInsertionEffect:Ya,useLayoutEffect:Xa,useMemo:ba,useReducer:uo,useRef:Ga,useState:function(){return uo(nr)},useDebugValue:Ks,useDeferredValue:function(e){var t=Te();return X===null?t.memoizedState=e:ec(t,X.memoizedState,e)},useTransition:function(){var e=uo(nr)[0],t=Te().memoizedState;return[e,t]},useMutableSource:$a,useSyncExternalStore:Ha,useId:tc,unstable_isNewReconciler:!1};function ze(e,t){if(e&&e.defaultProps){t=V({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Qo(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:V({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var jl={isMounted:function(e){return(e=e._reactInternals)?Ft(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ce(),l=mt(e),o=Ge(r,l);o.payload=t,n!=null&&(o.callback=n),t=ft(e,o,l),t!==null&&(De(t,e,l,r),Hr(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ce(),l=mt(e),o=Ge(r,l);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=ft(e,o,l),t!==null&&(De(t,e,l,r),Hr(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ce(),r=mt(e),l=Ge(n,r);l.tag=2,t!=null&&(l.callback=t),t=ft(e,l,r),t!==null&&(De(t,e,r,n),Hr(t,e,r))}};function Gi(e,t,n,r,l,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!Xn(n,r)||!Xn(l,o):!0}function oc(e,t,n){var r=!1,l=vt,o=t.contextType;return typeof o=="object"&&o!==null?o=Pe(o):(l=ye(t)?Rt:ie.current,r=t.contextTypes,o=(r=r!=null)?un(e,l):vt),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=jl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=o),t}function Yi(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&jl.enqueueReplaceState(t,t.state,null)}function Go(e,t,n,r){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},Ds(e);var o=t.contextType;typeof o=="object"&&o!==null?l.context=Pe(o):(o=ye(t)?Rt:ie.current,l.context=un(e,o)),l.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Qo(e,t,o,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&jl.enqueueReplaceState(l,l.state,null),cl(e,n,l,r),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function fn(e,t){try{var n="",r=t;do n+=Pd(r),r=r.return;while(r);var l=n}catch(o){l=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:l,digest:null}}function ao(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Yo(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var np=typeof WeakMap=="function"?WeakMap:Map;function sc(e,t,n){n=Ge(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){hl||(hl=!0,ls=r),Yo(e,t)},n}function ic(e,t,n){n=Ge(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var l=t.value;n.payload=function(){return r(l)},n.callback=function(){Yo(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){Yo(e,t),typeof r!="function"&&(pt===null?pt=new Set([this]):pt.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function Xi(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new np;var l=new Set;r.set(t,l)}else l=r.get(t),l===void 0&&(l=new Set,r.set(t,l));l.has(n)||(l.add(n),e=yp.bind(null,e,t,n),t.then(e,e))}function Ji(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Zi(e,t,n,r,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Ge(-1,1),t.tag=2,ft(n,t,1))),n.lanes|=1),e)}var rp=be.ReactCurrentOwner,me=!1;function ue(e,t,n,r){t.child=e===null?Da(t,null,n,r):cn(t,e.child,n,r)}function qi(e,t,n,r,l){n=n.render;var o=t.ref;return rn(t,l),r=Hs(e,t,n,r,o,l),n=Bs(),e!==null&&!me?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,qe(e,t,l)):($&&n&&Ts(t),t.flags|=1,ue(e,t,r,l),t.child)}function bi(e,t,n,r,l){if(e===null){var o=n.type;return typeof o=="function"&&!qs(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,uc(e,t,o,r,l)):(e=Gr(n.type,null,r,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&l)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:Xn,n(s,r)&&e.ref===t.ref)return qe(e,t,l)}return t.flags|=1,e=ht(o,r),e.ref=t.ref,e.return=t,t.child=e}function uc(e,t,n,r,l){if(e!==null){var o=e.memoizedProps;if(Xn(o,r)&&e.ref===t.ref)if(me=!1,t.pendingProps=r=o,(e.lanes&l)!==0)e.flags&131072&&(me=!0);else return t.lanes=e.lanes,qe(e,t,l)}return Xo(e,t,n,r,l)}function ac(e,t,n){var r=t.pendingProps,l=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},A(qt,ge),ge|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,A(qt,ge),ge|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,A(qt,ge),ge|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,A(qt,ge),ge|=r;return ue(e,t,l,n),t.child}function cc(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Xo(e,t,n,r,l){var o=ye(n)?Rt:ie.current;return o=un(t,o),rn(t,l),n=Hs(e,t,n,r,o,l),r=Bs(),e!==null&&!me?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,qe(e,t,l)):($&&r&&Ts(t),t.flags|=1,ue(e,t,n,l),t.child)}function eu(e,t,n,r,l){if(ye(n)){var o=!0;ol(t)}else o=!1;if(rn(t,l),t.stateNode===null)Kr(e,t),oc(t,n,r),Go(t,n,r,l),r=!0;else if(e===null){var s=t.stateNode,i=t.memoizedProps;s.props=i;var u=s.context,c=n.contextType;typeof c=="object"&&c!==null?c=Pe(c):(c=ye(n)?Rt:ie.current,c=un(t,c));var m=n.getDerivedStateFromProps,y=typeof m=="function"||typeof s.getSnapshotBeforeUpdate=="function";y||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(i!==r||u!==c)&&Yi(t,s,r,c),rt=!1;var h=t.memoizedState;s.state=h,cl(t,r,s,l),u=t.memoizedState,i!==r||h!==u||he.current||rt?(typeof m=="function"&&(Qo(t,n,m,r),u=t.memoizedState),(i=rt||Gi(t,n,i,r,h,u,c))?(y||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),s.props=r,s.state=u,s.context=c,r=i):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Fa(e,t),i=t.memoizedProps,c=t.type===t.elementType?i:ze(t.type,i),s.props=c,y=t.pendingProps,h=s.context,u=n.contextType,typeof u=="object"&&u!==null?u=Pe(u):(u=ye(n)?Rt:ie.current,u=un(t,u));var g=n.getDerivedStateFromProps;(m=typeof g=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(i!==y||h!==u)&&Yi(t,s,r,u),rt=!1,h=t.memoizedState,s.state=h,cl(t,r,s,l);var v=t.memoizedState;i!==y||h!==v||he.current||rt?(typeof g=="function"&&(Qo(t,n,g,r),v=t.memoizedState),(c=rt||Gi(t,n,c,r,h,v,u)||!1)?(m||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,v,u),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,v,u)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||i===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),s.props=r,s.state=v,s.context=u,r=c):(typeof s.componentDidUpdate!="function"||i===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return Jo(e,t,n,r,o,l)}function Jo(e,t,n,r,l,o){cc(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return l&&Ui(t,n,!1),qe(e,t,o);r=t.stateNode,rp.current=t;var i=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=cn(t,e.child,null,o),t.child=cn(t,null,i,o)):ue(e,t,i,o),t.memoizedState=r.state,l&&Ui(t,n,!0),t.child}function dc(e){var t=e.stateNode;t.pendingContext?Fi(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Fi(e,t.context,!1),As(e,t.containerInfo)}function tu(e,t,n,r,l){return an(),Rs(l),t.flags|=256,ue(e,t,n,r),t.child}var Zo={dehydrated:null,treeContext:null,retryLane:0};function qo(e){return{baseLanes:e,cachePool:null,transitions:null}}function fc(e,t,n){var r=t.pendingProps,l=H.current,o=!1,s=(t.flags&128)!==0,i;if((i=s)||(i=e!==null&&e.memoizedState===null?!1:(l&2)!==0),i?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),A(H,l&1),e===null)return Ko(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=Ll(s,r,0,null),e=Lt(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=qo(n),t.memoizedState=Zo,e):Ws(t,s));if(l=e.memoizedState,l!==null&&(i=l.dehydrated,i!==null))return lp(e,t,s,r,i,l,n);if(o){o=r.fallback,s=t.mode,l=e.child,i=l.sibling;var u={mode:"hidden",children:r.children};return!(s&1)&&t.child!==l?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=ht(l,u),r.subtreeFlags=l.subtreeFlags&14680064),i!==null?o=ht(i,o):(o=Lt(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?qo(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=Zo,r}return o=e.child,e=o.sibling,r=ht(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Ws(e,t){return t=Ll({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Lr(e,t,n,r){return r!==null&&Rs(r),cn(t,e.child,null,n),e=Ws(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function lp(e,t,n,r,l,o,s){if(n)return t.flags&256?(t.flags&=-257,r=ao(Error(S(422))),Lr(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,l=t.mode,r=Ll({mode:"visible",children:r.children},l,0,null),o=Lt(o,l,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&cn(t,e.child,null,s),t.child.memoizedState=qo(s),t.memoizedState=Zo,o);if(!(t.mode&1))return Lr(e,t,s,null);if(l.data==="$!"){if(r=l.nextSibling&&l.nextSibling.dataset,r)var i=r.dgst;return r=i,o=Error(S(419)),r=ao(o,r,void 0),Lr(e,t,s,r)}if(i=(s&e.childLanes)!==0,me||i){if(r=b,r!==null){switch(s&-s){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(r.suspendedLanes|s)?0:l,l!==0&&l!==o.retryLane&&(o.retryLane=l,Ze(e,l),De(r,e,l,-1))}return Zs(),r=ao(Error(S(421))),Lr(e,t,s,r)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=vp.bind(null,e),l._reactRetry=t,null):(e=o.treeContext,we=dt(l.nextSibling),xe=t,$=!0,Me=null,e!==null&&(Ce[Ne++]=We,Ce[Ne++]=Qe,Ce[Ne++]=zt,We=e.id,Qe=e.overflow,zt=t),t=Ws(t,r.children),t.flags|=4096,t)}function nu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Wo(e.return,t,n)}function co(e,t,n,r,l){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:l}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=l)}function pc(e,t,n){var r=t.pendingProps,l=r.revealOrder,o=r.tail;if(ue(e,t,r.children,n),r=H.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&nu(e,n,t);else if(e.tag===19)nu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(A(H,r),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&dl(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),co(t,!1,l,n,o);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&dl(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}co(t,!0,n,null,o);break;case"together":co(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Kr(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function qe(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Mt|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(S(153));if(t.child!==null){for(e=t.child,n=ht(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=ht(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function op(e,t,n){switch(t.tag){case 3:dc(t),an();break;case 5:Ua(t);break;case 1:ye(t.type)&&ol(t);break;case 4:As(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,l=t.memoizedProps.value;A(ul,r._currentValue),r._currentValue=l;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(A(H,H.current&1),t.flags|=128,null):n&t.child.childLanes?fc(e,t,n):(A(H,H.current&1),e=qe(e,t,n),e!==null?e.sibling:null);A(H,H.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return pc(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),A(H,H.current),r)break;return null;case 22:case 23:return t.lanes=0,ac(e,t,n)}return qe(e,t,n)}var mc,bo,hc,yc;mc=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};bo=function(){};hc=function(e,t,n,r){var l=e.memoizedProps;if(l!==r){e=t.stateNode,Pt(Be.current);var o=null;switch(n){case"input":l=So(e,l),r=So(e,r),o=[];break;case"select":l=V({},l,{value:void 0}),r=V({},r,{value:void 0}),o=[];break;case"textarea":l=Co(e,l),r=Co(e,r),o=[];break;default:typeof l.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=rl)}_o(n,r);var s;n=null;for(c in l)if(!r.hasOwnProperty(c)&&l.hasOwnProperty(c)&&l[c]!=null)if(c==="style"){var i=l[c];for(s in i)i.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Bn.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var u=r[c];if(i=l!=null?l[c]:void 0,r.hasOwnProperty(c)&&u!==i&&(u!=null||i!=null))if(c==="style")if(i){for(s in i)!i.hasOwnProperty(s)||u&&u.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in u)u.hasOwnProperty(s)&&i[s]!==u[s]&&(n||(n={}),n[s]=u[s])}else n||(o||(o=[]),o.push(c,n)),n=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,i=i?i.__html:void 0,u!=null&&i!==u&&(o=o||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(o=o||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(Bn.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&F("scroll",e),o||i===u||(o=[])):(o=o||[]).push(c,u))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}};yc=function(e,t,n,r){n!==r&&(t.flags|=4)};function jn(e,t){if(!$)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function oe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags&14680064,r|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function sp(e,t,n){var r=t.pendingProps;switch(Ls(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return oe(t),null;case 1:return ye(t.type)&&ll(),oe(t),null;case 3:return r=t.stateNode,dn(),U(he),U(ie),Us(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Pr(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Me!==null&&(is(Me),Me=null))),bo(e,t),oe(t),null;case 5:Fs(t);var l=Pt(er.current);if(n=t.type,e!==null&&t.stateNode!=null)hc(e,t,n,r,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(S(166));return oe(t),null}if(e=Pt(Be.current),Pr(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[$e]=t,r[qn]=o,e=(t.mode&1)!==0,n){case"dialog":F("cancel",r),F("close",r);break;case"iframe":case"object":case"embed":F("load",r);break;case"video":case"audio":for(l=0;l<zn.length;l++)F(zn[l],r);break;case"source":F("error",r);break;case"img":case"image":case"link":F("error",r),F("load",r);break;case"details":F("toggle",r);break;case"input":di(r,o),F("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},F("invalid",r);break;case"textarea":pi(r,o),F("invalid",r)}_o(n,o),l=null;for(var s in o)if(o.hasOwnProperty(s)){var i=o[s];s==="children"?typeof i=="string"?r.textContent!==i&&(o.suppressHydrationWarning!==!0&&jr(r.textContent,i,e),l=["children",i]):typeof i=="number"&&r.textContent!==""+i&&(o.suppressHydrationWarning!==!0&&jr(r.textContent,i,e),l=["children",""+i]):Bn.hasOwnProperty(s)&&i!=null&&s==="onScroll"&&F("scroll",r)}switch(n){case"input":wr(r),fi(r,o,!0);break;case"textarea":wr(r),mi(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=rl)}r=l,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Ku(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[$e]=t,e[qn]=r,mc(e,t,!1,!1),t.stateNode=e;e:{switch(s=jo(n,r),n){case"dialog":F("cancel",e),F("close",e),l=r;break;case"iframe":case"object":case"embed":F("load",e),l=r;break;case"video":case"audio":for(l=0;l<zn.length;l++)F(zn[l],e);l=r;break;case"source":F("error",e),l=r;break;case"img":case"image":case"link":F("error",e),F("load",e),l=r;break;case"details":F("toggle",e),l=r;break;case"input":di(e,r),l=So(e,r),F("invalid",e);break;case"option":l=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},l=V({},r,{value:void 0}),F("invalid",e);break;case"textarea":pi(e,r),l=Co(e,r),F("invalid",e);break;default:l=r}_o(n,l),i=l;for(o in i)if(i.hasOwnProperty(o)){var u=i[o];o==="style"?Gu(e,u):o==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&Wu(e,u)):o==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&Vn(e,u):typeof u=="number"&&Vn(e,""+u):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Bn.hasOwnProperty(o)?u!=null&&o==="onScroll"&&F("scroll",e):u!=null&&hs(e,o,u,s))}switch(n){case"input":wr(e),fi(e,r,!1);break;case"textarea":wr(e),mi(e);break;case"option":r.value!=null&&e.setAttribute("value",""+yt(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?bt(e,!!r.multiple,o,!1):r.defaultValue!=null&&bt(e,!!r.multiple,r.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=rl)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return oe(t),null;case 6:if(e&&t.stateNode!=null)yc(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(S(166));if(n=Pt(er.current),Pt(Be.current),Pr(t)){if(r=t.stateNode,n=t.memoizedProps,r[$e]=t,(o=r.nodeValue!==n)&&(e=xe,e!==null))switch(e.tag){case 3:jr(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&jr(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[$e]=t,t.stateNode=r}return oe(t),null;case 13:if(U(H),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if($&&we!==null&&t.mode&1&&!(t.flags&128))Ma(),an(),t.flags|=98560,o=!1;else if(o=Pr(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(S(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(S(317));o[$e]=t}else an(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;oe(t),o=!1}else Me!==null&&(is(Me),Me=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||H.current&1?J===0&&(J=3):Zs())),t.updateQueue!==null&&(t.flags|=4),oe(t),null);case 4:return dn(),bo(e,t),e===null&&Jn(t.stateNode.containerInfo),oe(t),null;case 10:return Ms(t.type._context),oe(t),null;case 17:return ye(t.type)&&ll(),oe(t),null;case 19:if(U(H),o=t.memoizedState,o===null)return oe(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)jn(o,!1);else{if(J!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=dl(e),s!==null){for(t.flags|=128,jn(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return A(H,H.current&1|2),t.child}e=e.sibling}o.tail!==null&&G()>pn&&(t.flags|=128,r=!0,jn(o,!1),t.lanes=4194304)}else{if(!r)if(e=dl(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),jn(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!$)return oe(t),null}else 2*G()-o.renderingStartTime>pn&&n!==1073741824&&(t.flags|=128,r=!0,jn(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=G(),t.sibling=null,n=H.current,A(H,r?n&1|2:n&1),t):(oe(t),null);case 22:case 23:return Js(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?ge&1073741824&&(oe(t),t.subtreeFlags&6&&(t.flags|=8192)):oe(t),null;case 24:return null;case 25:return null}throw Error(S(156,t.tag))}function ip(e,t){switch(Ls(t),t.tag){case 1:return ye(t.type)&&ll(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return dn(),U(he),U(ie),Us(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Fs(t),null;case 13:if(U(H),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(S(340));an()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return U(H),null;case 4:return dn(),null;case 10:return Ms(t.type._context),null;case 22:case 23:return Js(),null;case 24:return null;default:return null}}var Rr=!1,se=!1,up=typeof WeakSet=="function"?WeakSet:Set,C=null;function Zt(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){K(e,t,r)}else n.current=null}function es(e,t,n){try{n()}catch(r){K(e,t,r)}}var ru=!1;function ap(e,t){if(Ao=el,e=Sa(),Ps(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var l=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var s=0,i=-1,u=-1,c=0,m=0,y=e,h=null;t:for(;;){for(var g;y!==n||l!==0&&y.nodeType!==3||(i=s+l),y!==o||r!==0&&y.nodeType!==3||(u=s+r),y.nodeType===3&&(s+=y.nodeValue.length),(g=y.firstChild)!==null;)h=y,y=g;for(;;){if(y===e)break t;if(h===n&&++c===l&&(i=s),h===o&&++m===r&&(u=s),(g=y.nextSibling)!==null)break;y=h,h=y.parentNode}y=g}n=i===-1||u===-1?null:{start:i,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(Fo={focusedElem:e,selectionRange:n},el=!1,C=t;C!==null;)if(t=C,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,C=e;else for(;C!==null;){t=C;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var w=v.memoizedProps,k=v.memoizedState,d=t.stateNode,a=d.getSnapshotBeforeUpdate(t.elementType===t.type?w:ze(t.type,w),k);d.__reactInternalSnapshotBeforeUpdate=a}break;case 3:var p=t.stateNode.containerInfo;p.nodeType===1?p.textContent="":p.nodeType===9&&p.documentElement&&p.removeChild(p.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(S(163))}}catch(x){K(t,t.return,x)}if(e=t.sibling,e!==null){e.return=t.return,C=e;break}C=t.return}return v=ru,ru=!1,v}function Un(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var l=r=r.next;do{if((l.tag&e)===e){var o=l.destroy;l.destroy=void 0,o!==void 0&&es(t,n,o)}l=l.next}while(l!==r)}}function Pl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ts(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function vc(e){var t=e.alternate;t!==null&&(e.alternate=null,vc(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[$e],delete t[qn],delete t[Ho],delete t[Wf],delete t[Qf])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function gc(e){return e.tag===5||e.tag===3||e.tag===4}function lu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||gc(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ns(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=rl));else if(r!==4&&(e=e.child,e!==null))for(ns(e,t,n),e=e.sibling;e!==null;)ns(e,t,n),e=e.sibling}function rs(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(rs(e,t,n),e=e.sibling;e!==null;)rs(e,t,n),e=e.sibling}var te=null,Oe=!1;function tt(e,t,n){for(n=n.child;n!==null;)wc(e,t,n),n=n.sibling}function wc(e,t,n){if(He&&typeof He.onCommitFiberUnmount=="function")try{He.onCommitFiberUnmount(xl,n)}catch{}switch(n.tag){case 5:se||Zt(n,t);case 6:var r=te,l=Oe;te=null,tt(e,t,n),te=r,Oe=l,te!==null&&(Oe?(e=te,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):te.removeChild(n.stateNode));break;case 18:te!==null&&(Oe?(e=te,n=n.stateNode,e.nodeType===8?ro(e.parentNode,n):e.nodeType===1&&ro(e,n),Gn(e)):ro(te,n.stateNode));break;case 4:r=te,l=Oe,te=n.stateNode.containerInfo,Oe=!0,tt(e,t,n),te=r,Oe=l;break;case 0:case 11:case 14:case 15:if(!se&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){l=r=r.next;do{var o=l,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&es(n,t,s),l=l.next}while(l!==r)}tt(e,t,n);break;case 1:if(!se&&(Zt(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(i){K(n,t,i)}tt(e,t,n);break;case 21:tt(e,t,n);break;case 22:n.mode&1?(se=(r=se)||n.memoizedState!==null,tt(e,t,n),se=r):tt(e,t,n);break;default:tt(e,t,n)}}function ou(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new up),t.forEach(function(r){var l=gp.bind(null,e,r);n.has(r)||(n.add(r),r.then(l,l))})}}function Re(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var l=n[r];try{var o=e,s=t,i=s;e:for(;i!==null;){switch(i.tag){case 5:te=i.stateNode,Oe=!1;break e;case 3:te=i.stateNode.containerInfo,Oe=!0;break e;case 4:te=i.stateNode.containerInfo,Oe=!0;break e}i=i.return}if(te===null)throw Error(S(160));wc(o,s,l),te=null,Oe=!1;var u=l.alternate;u!==null&&(u.return=null),l.return=null}catch(c){K(l,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)xc(t,e),t=t.sibling}function xc(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Re(t,e),Fe(e),r&4){try{Un(3,e,e.return),Pl(3,e)}catch(w){K(e,e.return,w)}try{Un(5,e,e.return)}catch(w){K(e,e.return,w)}}break;case 1:Re(t,e),Fe(e),r&512&&n!==null&&Zt(n,n.return);break;case 5:if(Re(t,e),Fe(e),r&512&&n!==null&&Zt(n,n.return),e.flags&32){var l=e.stateNode;try{Vn(l,"")}catch(w){K(e,e.return,w)}}if(r&4&&(l=e.stateNode,l!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,i=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{i==="input"&&o.type==="radio"&&o.name!=null&&Bu(l,o),jo(i,s);var c=jo(i,o);for(s=0;s<u.length;s+=2){var m=u[s],y=u[s+1];m==="style"?Gu(l,y):m==="dangerouslySetInnerHTML"?Wu(l,y):m==="children"?Vn(l,y):hs(l,m,y,c)}switch(i){case"input":ko(l,o);break;case"textarea":Vu(l,o);break;case"select":var h=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!o.multiple;var g=o.value;g!=null?bt(l,!!o.multiple,g,!1):h!==!!o.multiple&&(o.defaultValue!=null?bt(l,!!o.multiple,o.defaultValue,!0):bt(l,!!o.multiple,o.multiple?[]:"",!1))}l[qn]=o}catch(w){K(e,e.return,w)}}break;case 6:if(Re(t,e),Fe(e),r&4){if(e.stateNode===null)throw Error(S(162));l=e.stateNode,o=e.memoizedProps;try{l.nodeValue=o}catch(w){K(e,e.return,w)}}break;case 3:if(Re(t,e),Fe(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Gn(t.containerInfo)}catch(w){K(e,e.return,w)}break;case 4:Re(t,e),Fe(e);break;case 13:Re(t,e),Fe(e),l=e.child,l.flags&8192&&(o=l.memoizedState!==null,l.stateNode.isHidden=o,!o||l.alternate!==null&&l.alternate.memoizedState!==null||(Ys=G())),r&4&&ou(e);break;case 22:if(m=n!==null&&n.memoizedState!==null,e.mode&1?(se=(c=se)||m,Re(t,e),se=c):Re(t,e),Fe(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!m&&e.mode&1)for(C=e,m=e.child;m!==null;){for(y=C=m;C!==null;){switch(h=C,g=h.child,h.tag){case 0:case 11:case 14:case 15:Un(4,h,h.return);break;case 1:Zt(h,h.return);var v=h.stateNode;if(typeof v.componentWillUnmount=="function"){r=h,n=h.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(w){K(r,n,w)}}break;case 5:Zt(h,h.return);break;case 22:if(h.memoizedState!==null){iu(y);continue}}g!==null?(g.return=h,C=g):iu(y)}m=m.sibling}e:for(m=null,y=e;;){if(y.tag===5){if(m===null){m=y;try{l=y.stateNode,c?(o=l.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(i=y.stateNode,u=y.memoizedProps.style,s=u!=null&&u.hasOwnProperty("display")?u.display:null,i.style.display=Qu("display",s))}catch(w){K(e,e.return,w)}}}else if(y.tag===6){if(m===null)try{y.stateNode.nodeValue=c?"":y.memoizedProps}catch(w){K(e,e.return,w)}}else if((y.tag!==22&&y.tag!==23||y.memoizedState===null||y===e)&&y.child!==null){y.child.return=y,y=y.child;continue}if(y===e)break e;for(;y.sibling===null;){if(y.return===null||y.return===e)break e;m===y&&(m=null),y=y.return}m===y&&(m=null),y.sibling.return=y.return,y=y.sibling}}break;case 19:Re(t,e),Fe(e),r&4&&ou(e);break;case 21:break;default:Re(t,e),Fe(e)}}function Fe(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(gc(n)){var r=n;break e}n=n.return}throw Error(S(160))}switch(r.tag){case 5:var l=r.stateNode;r.flags&32&&(Vn(l,""),r.flags&=-33);var o=lu(e);rs(e,o,l);break;case 3:case 4:var s=r.stateNode.containerInfo,i=lu(e);ns(e,i,s);break;default:throw Error(S(161))}}catch(u){K(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function cp(e,t,n){C=e,Sc(e)}function Sc(e,t,n){for(var r=(e.mode&1)!==0;C!==null;){var l=C,o=l.child;if(l.tag===22&&r){var s=l.memoizedState!==null||Rr;if(!s){var i=l.alternate,u=i!==null&&i.memoizedState!==null||se;i=Rr;var c=se;if(Rr=s,(se=u)&&!c)for(C=l;C!==null;)s=C,u=s.child,s.tag===22&&s.memoizedState!==null?uu(l):u!==null?(u.return=s,C=u):uu(l);for(;o!==null;)C=o,Sc(o),o=o.sibling;C=l,Rr=i,se=c}su(e)}else l.subtreeFlags&8772&&o!==null?(o.return=l,C=o):su(e)}}function su(e){for(;C!==null;){var t=C;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:se||Pl(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!se)if(n===null)r.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:ze(t.type,n.memoizedProps);r.componentDidUpdate(l,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&Ki(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Ki(t,s,n)}break;case 5:var i=t.stateNode;if(n===null&&t.flags&4){n=i;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var m=c.memoizedState;if(m!==null){var y=m.dehydrated;y!==null&&Gn(y)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(S(163))}se||t.flags&512&&ts(t)}catch(h){K(t,t.return,h)}}if(t===e){C=null;break}if(n=t.sibling,n!==null){n.return=t.return,C=n;break}C=t.return}}function iu(e){for(;C!==null;){var t=C;if(t===e){C=null;break}var n=t.sibling;if(n!==null){n.return=t.return,C=n;break}C=t.return}}function uu(e){for(;C!==null;){var t=C;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Pl(4,t)}catch(u){K(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var l=t.return;try{r.componentDidMount()}catch(u){K(t,l,u)}}var o=t.return;try{ts(t)}catch(u){K(t,o,u)}break;case 5:var s=t.return;try{ts(t)}catch(u){K(t,s,u)}}}catch(u){K(t,t.return,u)}if(t===e){C=null;break}var i=t.sibling;if(i!==null){i.return=t.return,C=i;break}C=t.return}}var dp=Math.ceil,ml=be.ReactCurrentDispatcher,Qs=be.ReactCurrentOwner,je=be.ReactCurrentBatchConfig,M=0,b=null,Y=null,ne=0,ge=0,qt=wt(0),J=0,lr=null,Mt=0,Tl=0,Gs=0,$n=null,pe=null,Ys=0,pn=1/0,Ve=null,hl=!1,ls=null,pt=null,zr=!1,it=null,yl=0,Hn=0,os=null,Wr=-1,Qr=0;function ce(){return M&6?G():Wr!==-1?Wr:Wr=G()}function mt(e){return e.mode&1?M&2&&ne!==0?ne&-ne:Yf.transition!==null?(Qr===0&&(Qr=oa()),Qr):(e=I,e!==0||(e=window.event,e=e===void 0?16:fa(e.type)),e):1}function De(e,t,n,r){if(50<Hn)throw Hn=0,os=null,Error(S(185));ir(e,n,r),(!(M&2)||e!==b)&&(e===b&&(!(M&2)&&(Tl|=n),J===4&&ot(e,ne)),ve(e,r),n===1&&M===0&&!(t.mode&1)&&(pn=G()+500,Nl&&xt()))}function ve(e,t){var n=e.callbackNode;Gd(e,t);var r=br(e,e===b?ne:0);if(r===0)n!==null&&vi(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&vi(n),t===1)e.tag===0?Gf(au.bind(null,e)):Ra(au.bind(null,e)),Vf(function(){!(M&6)&&xt()}),n=null;else{switch(sa(r)){case 1:n=xs;break;case 4:n=ra;break;case 16:n=qr;break;case 536870912:n=la;break;default:n=qr}n=Tc(n,kc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function kc(e,t){if(Wr=-1,Qr=0,M&6)throw Error(S(327));var n=e.callbackNode;if(ln()&&e.callbackNode!==n)return null;var r=br(e,e===b?ne:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=vl(e,r);else{t=r;var l=M;M|=2;var o=Cc();(b!==e||ne!==t)&&(Ve=null,pn=G()+500,Tt(e,t));do try{mp();break}catch(i){Ec(e,i)}while(!0);Os(),ml.current=o,M=l,Y!==null?t=0:(b=null,ne=0,t=J)}if(t!==0){if(t===2&&(l=zo(e),l!==0&&(r=l,t=ss(e,l))),t===1)throw n=lr,Tt(e,0),ot(e,r),ve(e,G()),n;if(t===6)ot(e,r);else{if(l=e.current.alternate,!(r&30)&&!fp(l)&&(t=vl(e,r),t===2&&(o=zo(e),o!==0&&(r=o,t=ss(e,o))),t===1))throw n=lr,Tt(e,0),ot(e,r),ve(e,G()),n;switch(e.finishedWork=l,e.finishedLanes=r,t){case 0:case 1:throw Error(S(345));case 2:Nt(e,pe,Ve);break;case 3:if(ot(e,r),(r&130023424)===r&&(t=Ys+500-G(),10<t)){if(br(e,0)!==0)break;if(l=e.suspendedLanes,(l&r)!==r){ce(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=$o(Nt.bind(null,e,pe,Ve),t);break}Nt(e,pe,Ve);break;case 4:if(ot(e,r),(r&4194240)===r)break;for(t=e.eventTimes,l=-1;0<r;){var s=31-Ie(r);o=1<<s,s=t[s],s>l&&(l=s),r&=~o}if(r=l,r=G()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*dp(r/1960))-r,10<r){e.timeoutHandle=$o(Nt.bind(null,e,pe,Ve),r);break}Nt(e,pe,Ve);break;case 5:Nt(e,pe,Ve);break;default:throw Error(S(329))}}}return ve(e,G()),e.callbackNode===n?kc.bind(null,e):null}function ss(e,t){var n=$n;return e.current.memoizedState.isDehydrated&&(Tt(e,t).flags|=256),e=vl(e,t),e!==2&&(t=pe,pe=n,t!==null&&is(t)),e}function is(e){pe===null?pe=e:pe.push.apply(pe,e)}function fp(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var l=n[r],o=l.getSnapshot;l=l.value;try{if(!Ae(o(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ot(e,t){for(t&=~Gs,t&=~Tl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ie(t),r=1<<n;e[n]=-1,t&=~r}}function au(e){if(M&6)throw Error(S(327));ln();var t=br(e,0);if(!(t&1))return ve(e,G()),null;var n=vl(e,t);if(e.tag!==0&&n===2){var r=zo(e);r!==0&&(t=r,n=ss(e,r))}if(n===1)throw n=lr,Tt(e,0),ot(e,t),ve(e,G()),n;if(n===6)throw Error(S(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Nt(e,pe,Ve),ve(e,G()),null}function Xs(e,t){var n=M;M|=1;try{return e(t)}finally{M=n,M===0&&(pn=G()+500,Nl&&xt())}}function It(e){it!==null&&it.tag===0&&!(M&6)&&ln();var t=M;M|=1;var n=je.transition,r=I;try{if(je.transition=null,I=1,e)return e()}finally{I=r,je.transition=n,M=t,!(M&6)&&xt()}}function Js(){ge=qt.current,U(qt)}function Tt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Bf(n)),Y!==null)for(n=Y.return;n!==null;){var r=n;switch(Ls(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ll();break;case 3:dn(),U(he),U(ie),Us();break;case 5:Fs(r);break;case 4:dn();break;case 13:U(H);break;case 19:U(H);break;case 10:Ms(r.type._context);break;case 22:case 23:Js()}n=n.return}if(b=e,Y=e=ht(e.current,null),ne=ge=t,J=0,lr=null,Gs=Tl=Mt=0,pe=$n=null,jt!==null){for(t=0;t<jt.length;t++)if(n=jt[t],r=n.interleaved,r!==null){n.interleaved=null;var l=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=l,r.next=s}n.pending=r}jt=null}return e}function Ec(e,t){do{var n=Y;try{if(Os(),Br.current=pl,fl){for(var r=B.memoizedState;r!==null;){var l=r.queue;l!==null&&(l.pending=null),r=r.next}fl=!1}if(Ot=0,q=X=B=null,Fn=!1,tr=0,Qs.current=null,n===null||n.return===null){J=1,lr=t,Y=null;break}e:{var o=e,s=n.return,i=n,u=t;if(t=ne,i.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,m=i,y=m.tag;if(!(m.mode&1)&&(y===0||y===11||y===15)){var h=m.alternate;h?(m.updateQueue=h.updateQueue,m.memoizedState=h.memoizedState,m.lanes=h.lanes):(m.updateQueue=null,m.memoizedState=null)}var g=Ji(s);if(g!==null){g.flags&=-257,Zi(g,s,i,o,t),g.mode&1&&Xi(o,c,t),t=g,u=c;var v=t.updateQueue;if(v===null){var w=new Set;w.add(u),t.updateQueue=w}else v.add(u);break e}else{if(!(t&1)){Xi(o,c,t),Zs();break e}u=Error(S(426))}}else if($&&i.mode&1){var k=Ji(s);if(k!==null){!(k.flags&65536)&&(k.flags|=256),Zi(k,s,i,o,t),Rs(fn(u,i));break e}}o=u=fn(u,i),J!==4&&(J=2),$n===null?$n=[o]:$n.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var d=sc(o,u,t);Vi(o,d);break e;case 1:i=u;var a=o.type,p=o.stateNode;if(!(o.flags&128)&&(typeof a.getDerivedStateFromError=="function"||p!==null&&typeof p.componentDidCatch=="function"&&(pt===null||!pt.has(p)))){o.flags|=65536,t&=-t,o.lanes|=t;var x=ic(o,i,t);Vi(o,x);break e}}o=o.return}while(o!==null)}_c(n)}catch(E){t=E,Y===n&&n!==null&&(Y=n=n.return);continue}break}while(!0)}function Cc(){var e=ml.current;return ml.current=pl,e===null?pl:e}function Zs(){(J===0||J===3||J===2)&&(J=4),b===null||!(Mt&268435455)&&!(Tl&268435455)||ot(b,ne)}function vl(e,t){var n=M;M|=2;var r=Cc();(b!==e||ne!==t)&&(Ve=null,Tt(e,t));do try{pp();break}catch(l){Ec(e,l)}while(!0);if(Os(),M=n,ml.current=r,Y!==null)throw Error(S(261));return b=null,ne=0,J}function pp(){for(;Y!==null;)Nc(Y)}function mp(){for(;Y!==null&&!Fd();)Nc(Y)}function Nc(e){var t=Pc(e.alternate,e,ge);e.memoizedProps=e.pendingProps,t===null?_c(e):Y=t,Qs.current=null}function _c(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=ip(n,t),n!==null){n.flags&=32767,Y=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{J=6,Y=null;return}}else if(n=sp(n,t,ge),n!==null){Y=n;return}if(t=t.sibling,t!==null){Y=t;return}Y=t=e}while(t!==null);J===0&&(J=5)}function Nt(e,t,n){var r=I,l=je.transition;try{je.transition=null,I=1,hp(e,t,n,r)}finally{je.transition=l,I=r}return null}function hp(e,t,n,r){do ln();while(it!==null);if(M&6)throw Error(S(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(S(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Yd(e,o),e===b&&(Y=b=null,ne=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||zr||(zr=!0,Tc(qr,function(){return ln(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=je.transition,je.transition=null;var s=I;I=1;var i=M;M|=4,Qs.current=null,ap(e,n),xc(n,e),If(Fo),el=!!Ao,Fo=Ao=null,e.current=n,cp(n),Ud(),M=i,I=s,je.transition=o}else e.current=n;if(zr&&(zr=!1,it=e,yl=l),o=e.pendingLanes,o===0&&(pt=null),Bd(n.stateNode),ve(e,G()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],r(l.value,{componentStack:l.stack,digest:l.digest});if(hl)throw hl=!1,e=ls,ls=null,e;return yl&1&&e.tag!==0&&ln(),o=e.pendingLanes,o&1?e===os?Hn++:(Hn=0,os=e):Hn=0,xt(),null}function ln(){if(it!==null){var e=sa(yl),t=je.transition,n=I;try{if(je.transition=null,I=16>e?16:e,it===null)var r=!1;else{if(e=it,it=null,yl=0,M&6)throw Error(S(331));var l=M;for(M|=4,C=e.current;C!==null;){var o=C,s=o.child;if(C.flags&16){var i=o.deletions;if(i!==null){for(var u=0;u<i.length;u++){var c=i[u];for(C=c;C!==null;){var m=C;switch(m.tag){case 0:case 11:case 15:Un(8,m,o)}var y=m.child;if(y!==null)y.return=m,C=y;else for(;C!==null;){m=C;var h=m.sibling,g=m.return;if(vc(m),m===c){C=null;break}if(h!==null){h.return=g,C=h;break}C=g}}}var v=o.alternate;if(v!==null){var w=v.child;if(w!==null){v.child=null;do{var k=w.sibling;w.sibling=null,w=k}while(w!==null)}}C=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,C=s;else e:for(;C!==null;){if(o=C,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Un(9,o,o.return)}var d=o.sibling;if(d!==null){d.return=o.return,C=d;break e}C=o.return}}var a=e.current;for(C=a;C!==null;){s=C;var p=s.child;if(s.subtreeFlags&2064&&p!==null)p.return=s,C=p;else e:for(s=a;C!==null;){if(i=C,i.flags&2048)try{switch(i.tag){case 0:case 11:case 15:Pl(9,i)}}catch(E){K(i,i.return,E)}if(i===s){C=null;break e}var x=i.sibling;if(x!==null){x.return=i.return,C=x;break e}C=i.return}}if(M=l,xt(),He&&typeof He.onPostCommitFiberRoot=="function")try{He.onPostCommitFiberRoot(xl,e)}catch{}r=!0}return r}finally{I=n,je.transition=t}}return!1}function cu(e,t,n){t=fn(n,t),t=sc(e,t,1),e=ft(e,t,1),t=ce(),e!==null&&(ir(e,1,t),ve(e,t))}function K(e,t,n){if(e.tag===3)cu(e,e,n);else for(;t!==null;){if(t.tag===3){cu(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(pt===null||!pt.has(r))){e=fn(n,e),e=ic(t,e,1),t=ft(t,e,1),e=ce(),t!==null&&(ir(t,1,e),ve(t,e));break}}t=t.return}}function yp(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ce(),e.pingedLanes|=e.suspendedLanes&n,b===e&&(ne&n)===n&&(J===4||J===3&&(ne&130023424)===ne&&500>G()-Ys?Tt(e,0):Gs|=n),ve(e,t)}function jc(e,t){t===0&&(e.mode&1?(t=kr,kr<<=1,!(kr&130023424)&&(kr=4194304)):t=1);var n=ce();e=Ze(e,t),e!==null&&(ir(e,t,n),ve(e,n))}function vp(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),jc(e,n)}function gp(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(S(314))}r!==null&&r.delete(t),jc(e,n)}var Pc;Pc=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||he.current)me=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return me=!1,op(e,t,n);me=!!(e.flags&131072)}else me=!1,$&&t.flags&1048576&&za(t,il,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Kr(e,t),e=t.pendingProps;var l=un(t,ie.current);rn(t,n),l=Hs(null,t,r,e,l,n);var o=Bs();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,ye(r)?(o=!0,ol(t)):o=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,Ds(t),l.updater=jl,t.stateNode=l,l._reactInternals=t,Go(t,r,e,n),t=Jo(null,t,r,!0,o,n)):(t.tag=0,$&&o&&Ts(t),ue(null,t,l,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Kr(e,t),e=t.pendingProps,l=r._init,r=l(r._payload),t.type=r,l=t.tag=xp(r),e=ze(r,e),l){case 0:t=Xo(null,t,r,e,n);break e;case 1:t=eu(null,t,r,e,n);break e;case 11:t=qi(null,t,r,e,n);break e;case 14:t=bi(null,t,r,ze(r.type,e),n);break e}throw Error(S(306,r,""))}return t;case 0:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ze(r,l),Xo(e,t,r,l,n);case 1:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ze(r,l),eu(e,t,r,l,n);case 3:e:{if(dc(t),e===null)throw Error(S(387));r=t.pendingProps,o=t.memoizedState,l=o.element,Fa(e,t),cl(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){l=fn(Error(S(423)),t),t=tu(e,t,r,n,l);break e}else if(r!==l){l=fn(Error(S(424)),t),t=tu(e,t,r,n,l);break e}else for(we=dt(t.stateNode.containerInfo.firstChild),xe=t,$=!0,Me=null,n=Da(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(an(),r===l){t=qe(e,t,n);break e}ue(e,t,r,n)}t=t.child}return t;case 5:return Ua(t),e===null&&Ko(t),r=t.type,l=t.pendingProps,o=e!==null?e.memoizedProps:null,s=l.children,Uo(r,l)?s=null:o!==null&&Uo(r,o)&&(t.flags|=32),cc(e,t),ue(e,t,s,n),t.child;case 6:return e===null&&Ko(t),null;case 13:return fc(e,t,n);case 4:return As(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=cn(t,null,r,n):ue(e,t,r,n),t.child;case 11:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ze(r,l),qi(e,t,r,l,n);case 7:return ue(e,t,t.pendingProps,n),t.child;case 8:return ue(e,t,t.pendingProps.children,n),t.child;case 12:return ue(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,l=t.pendingProps,o=t.memoizedProps,s=l.value,A(ul,r._currentValue),r._currentValue=s,o!==null)if(Ae(o.value,s)){if(o.children===l.children&&!he.current){t=qe(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var i=o.dependencies;if(i!==null){s=o.child;for(var u=i.firstContext;u!==null;){if(u.context===r){if(o.tag===1){u=Ge(-1,n&-n),u.tag=2;var c=o.updateQueue;if(c!==null){c=c.shared;var m=c.pending;m===null?u.next=u:(u.next=m.next,m.next=u),c.pending=u}}o.lanes|=n,u=o.alternate,u!==null&&(u.lanes|=n),Wo(o.return,n,t),i.lanes|=n;break}u=u.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(S(341));s.lanes|=n,i=s.alternate,i!==null&&(i.lanes|=n),Wo(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}ue(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,r=t.pendingProps.children,rn(t,n),l=Pe(l),r=r(l),t.flags|=1,ue(e,t,r,n),t.child;case 14:return r=t.type,l=ze(r,t.pendingProps),l=ze(r.type,l),bi(e,t,r,l,n);case 15:return uc(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ze(r,l),Kr(e,t),t.tag=1,ye(r)?(e=!0,ol(t)):e=!1,rn(t,n),oc(t,r,l),Go(t,r,l,n),Jo(null,t,r,!0,e,n);case 19:return pc(e,t,n);case 22:return ac(e,t,n)}throw Error(S(156,t.tag))};function Tc(e,t){return na(e,t)}function wp(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function _e(e,t,n,r){return new wp(e,t,n,r)}function qs(e){return e=e.prototype,!(!e||!e.isReactComponent)}function xp(e){if(typeof e=="function")return qs(e)?1:0;if(e!=null){if(e=e.$$typeof,e===vs)return 11;if(e===gs)return 14}return 2}function ht(e,t){var n=e.alternate;return n===null?(n=_e(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Gr(e,t,n,r,l,o){var s=2;if(r=e,typeof e=="function")qs(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case Bt:return Lt(n.children,l,o,t);case ys:s=8,l|=8;break;case vo:return e=_e(12,n,t,l|2),e.elementType=vo,e.lanes=o,e;case go:return e=_e(13,n,t,l),e.elementType=go,e.lanes=o,e;case wo:return e=_e(19,n,t,l),e.elementType=wo,e.lanes=o,e;case Uu:return Ll(n,l,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Au:s=10;break e;case Fu:s=9;break e;case vs:s=11;break e;case gs:s=14;break e;case nt:s=16,r=null;break e}throw Error(S(130,e==null?e:typeof e,""))}return t=_e(s,n,t,l),t.elementType=e,t.type=r,t.lanes=o,t}function Lt(e,t,n,r){return e=_e(7,e,r,t),e.lanes=n,e}function Ll(e,t,n,r){return e=_e(22,e,r,t),e.elementType=Uu,e.lanes=n,e.stateNode={isHidden:!1},e}function fo(e,t,n){return e=_e(6,e,null,t),e.lanes=n,e}function po(e,t,n){return t=_e(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Sp(e,t,n,r,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ql(0),this.expirationTimes=Ql(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ql(0),this.identifierPrefix=r,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function bs(e,t,n,r,l,o,s,i,u){return e=new Sp(e,t,n,i,u),t===1?(t=1,o===!0&&(t|=8)):t=0,o=_e(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ds(o),e}function kp(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Ht,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Lc(e){if(!e)return vt;e=e._reactInternals;e:{if(Ft(e)!==e||e.tag!==1)throw Error(S(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(ye(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(S(171))}if(e.tag===1){var n=e.type;if(ye(n))return La(e,n,t)}return t}function Rc(e,t,n,r,l,o,s,i,u){return e=bs(n,r,!0,e,l,o,s,i,u),e.context=Lc(null),n=e.current,r=ce(),l=mt(n),o=Ge(r,l),o.callback=t??null,ft(n,o,l),e.current.lanes=l,ir(e,l,r),ve(e,r),e}function Rl(e,t,n,r){var l=t.current,o=ce(),s=mt(l);return n=Lc(n),t.context===null?t.context=n:t.pendingContext=n,t=Ge(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=ft(l,t,s),e!==null&&(De(e,l,s,o),Hr(e,l,s)),s}function gl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function du(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ei(e,t){du(e,t),(e=e.alternate)&&du(e,t)}function Ep(){return null}var zc=typeof reportError=="function"?reportError:function(e){console.error(e)};function ti(e){this._internalRoot=e}zl.prototype.render=ti.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(S(409));Rl(e,t,null,null)};zl.prototype.unmount=ti.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;It(function(){Rl(null,e,null,null)}),t[Je]=null}};function zl(e){this._internalRoot=e}zl.prototype.unstable_scheduleHydration=function(e){if(e){var t=aa();e={blockedOn:null,target:e,priority:t};for(var n=0;n<lt.length&&t!==0&&t<lt[n].priority;n++);lt.splice(n,0,e),n===0&&da(e)}};function ni(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ol(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function fu(){}function Cp(e,t,n,r,l){if(l){if(typeof r=="function"){var o=r;r=function(){var c=gl(s);o.call(c)}}var s=Rc(t,r,e,0,null,!1,!1,"",fu);return e._reactRootContainer=s,e[Je]=s.current,Jn(e.nodeType===8?e.parentNode:e),It(),s}for(;l=e.lastChild;)e.removeChild(l);if(typeof r=="function"){var i=r;r=function(){var c=gl(u);i.call(c)}}var u=bs(e,0,!1,null,null,!1,!1,"",fu);return e._reactRootContainer=u,e[Je]=u.current,Jn(e.nodeType===8?e.parentNode:e),It(function(){Rl(t,u,n,r)}),u}function Ml(e,t,n,r,l){var o=n._reactRootContainer;if(o){var s=o;if(typeof l=="function"){var i=l;l=function(){var u=gl(s);i.call(u)}}Rl(t,s,e,l)}else s=Cp(n,t,e,l,r);return gl(s)}ia=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Rn(t.pendingLanes);n!==0&&(Ss(t,n|1),ve(t,G()),!(M&6)&&(pn=G()+500,xt()))}break;case 13:It(function(){var r=Ze(e,1);if(r!==null){var l=ce();De(r,e,1,l)}}),ei(e,1)}};ks=function(e){if(e.tag===13){var t=Ze(e,134217728);if(t!==null){var n=ce();De(t,e,134217728,n)}ei(e,134217728)}};ua=function(e){if(e.tag===13){var t=mt(e),n=Ze(e,t);if(n!==null){var r=ce();De(n,e,t,r)}ei(e,t)}};aa=function(){return I};ca=function(e,t){var n=I;try{return I=e,t()}finally{I=n}};To=function(e,t,n){switch(t){case"input":if(ko(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var l=Cl(r);if(!l)throw Error(S(90));Hu(r),ko(r,l)}}}break;case"textarea":Vu(e,n);break;case"select":t=n.value,t!=null&&bt(e,!!n.multiple,t,!1)}};Ju=Xs;Zu=It;var Np={usingClientEntryPoint:!1,Events:[ar,Qt,Cl,Yu,Xu,Xs]},Pn={findFiberByHostInstance:_t,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},_p={bundleType:Pn.bundleType,version:Pn.version,rendererPackageName:Pn.rendererPackageName,rendererConfig:Pn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:be.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=ea(e),e===null?null:e.stateNode},findFiberByHostInstance:Pn.findFiberByHostInstance||Ep,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Or=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Or.isDisabled&&Or.supportsFiber)try{xl=Or.inject(_p),He=Or}catch{}}ke.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Np;ke.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ni(t))throw Error(S(200));return kp(e,t,null,n)};ke.createRoot=function(e,t){if(!ni(e))throw Error(S(299));var n=!1,r="",l=zc;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=bs(e,1,!1,null,null,n,!1,r,l),e[Je]=t.current,Jn(e.nodeType===8?e.parentNode:e),new ti(t)};ke.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(S(188)):(e=Object.keys(e).join(","),Error(S(268,e)));return e=ea(t),e=e===null?null:e.stateNode,e};ke.flushSync=function(e){return It(e)};ke.hydrate=function(e,t,n){if(!Ol(t))throw Error(S(200));return Ml(null,e,t,!0,n)};ke.hydrateRoot=function(e,t,n){if(!ni(e))throw Error(S(405));var r=n!=null&&n.hydratedSources||null,l=!1,o="",s=zc;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=Rc(t,null,e,1,n??null,l,!1,o,s),e[Je]=t.current,Jn(e),r)for(e=0;e<r.length;e++)n=r[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new zl(t)};ke.render=function(e,t,n){if(!Ol(t))throw Error(S(200));return Ml(null,e,t,!1,n)};ke.unmountComponentAtNode=function(e){if(!Ol(e))throw Error(S(40));return e._reactRootContainer?(It(function(){Ml(null,null,e,!1,function(){e._reactRootContainer=null,e[Je]=null})}),!0):!1};ke.unstable_batchedUpdates=Xs;ke.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ol(n))throw Error(S(200));if(e==null||e._reactInternals===void 0)throw Error(S(38));return Ml(e,t,n,!1,r)};ke.version="18.3.1-next-f1338f8080-20240426";function Oc(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Oc)}catch(e){console.error(e)}}Oc(),Ou.exports=ke;var jp=Ou.exports,pu=jp;ho.createRoot=pu.createRoot,ho.hydrateRoot=pu.hydrateRoot;/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pp=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Mc=(...e)=>e.filter((t,n,r)=>!!t&&r.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Tp={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lp=T.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:l="",children:o,iconNode:s,...i},u)=>T.createElement("svg",{ref:u,...Tp,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Mc("lucide",l),...i},[...s.map(([c,m])=>T.createElement(c,m)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D=(e,t)=>{const n=T.forwardRef(({className:r,...l},o)=>T.createElement(Lp,{ref:o,iconNode:t,className:Mc(`lucide-${Pp(e)}`,r),...l}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rp=D("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ic=D("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zp=D("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Op=D("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ri=D("CircleCheckBig",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dc=D("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mp=D("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ip=D("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dp=D("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ap=D("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fp=D("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Up=D("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $t=D("KeyRound",[["path",{d:"M2 18v3c0 .6.4 1 1 1h4v-3h3v-3h2l1.4-1.4a6.5 6.5 0 1 0-4-4Z",key:"167ctg"}],["circle",{cx:"16.5",cy:"7.5",r:".5",fill:"currentColor",key:"w0ekpg"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dr=D("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $p=D("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hp=D("Paperclip",[["path",{d:"m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48",key:"1u3ebp"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bp=D("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ac=D("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fc=D("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vp=D("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kp=D("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wp=D("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qp=D("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gp=D("Terminal",[["polyline",{points:"4 17 10 11 4 5",key:"akl6gq"}],["line",{x1:"12",x2:"20",y1:"19",y2:"19",key:"q2wloq"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uc=D("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $c=D("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yp=D("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.395.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hc=D("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Xp={},mu=e=>{let t;const n=new Set,r=(m,y)=>{const h=typeof m=="function"?m(t):m;if(!Object.is(h,t)){const g=t;t=y??(typeof h!="object"||h===null)?h:Object.assign({},t,h),n.forEach(v=>v(t,g))}},l=()=>t,u={setState:r,getState:l,getInitialState:()=>c,subscribe:m=>(n.add(m),()=>n.delete(m)),destroy:()=>{(Xp?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},c=t=e(r,l,u);return u},Jp=e=>e?mu(e):mu;var Bc={exports:{}},Vc={},Kc={exports:{}},Wc={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mn=T;function Zp(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var qp=typeof Object.is=="function"?Object.is:Zp,bp=mn.useState,em=mn.useEffect,tm=mn.useLayoutEffect,nm=mn.useDebugValue;function rm(e,t){var n=t(),r=bp({inst:{value:n,getSnapshot:t}}),l=r[0].inst,o=r[1];return tm(function(){l.value=n,l.getSnapshot=t,mo(l)&&o({inst:l})},[e,n,t]),em(function(){return mo(l)&&o({inst:l}),e(function(){mo(l)&&o({inst:l})})},[e]),nm(n),n}function mo(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!qp(e,n)}catch{return!0}}function lm(e,t){return t()}var om=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?lm:rm;Wc.useSyncExternalStore=mn.useSyncExternalStore!==void 0?mn.useSyncExternalStore:om;Kc.exports=Wc;var sm=Kc.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Il=T,im=sm;function um(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var am=typeof Object.is=="function"?Object.is:um,cm=im.useSyncExternalStore,dm=Il.useRef,fm=Il.useEffect,pm=Il.useMemo,mm=Il.useDebugValue;Vc.useSyncExternalStoreWithSelector=function(e,t,n,r,l){var o=dm(null);if(o.current===null){var s={hasValue:!1,value:null};o.current=s}else s=o.current;o=pm(function(){function u(g){if(!c){if(c=!0,m=g,g=r(g),l!==void 0&&s.hasValue){var v=s.value;if(l(v,g))return y=v}return y=g}if(v=y,am(m,g))return v;var w=r(g);return l!==void 0&&l(v,w)?(m=g,v):(m=g,y=w)}var c=!1,m,y,h=n===void 0?null:n;return[function(){return u(t())},h===null?void 0:function(){return u(h())}]},[t,n,r,l]);var i=cm(e,o[0],o[1]);return fm(function(){s.hasValue=!0,s.value=i},[i]),mm(i),i};Bc.exports=Vc;var hm=Bc.exports;const ym=xu(hm),Qc={},{useDebugValue:vm}=Ru,{useSyncExternalStoreWithSelector:gm}=ym;let hu=!1;const wm=e=>e;function xm(e,t=wm,n){(Qc?"production":void 0)!=="production"&&n&&!hu&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),hu=!0);const r=gm(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return vm(r),r}const yu=e=>{(Qc?"production":void 0)!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof e=="function"?Jp(e):e,n=(r,l)=>xm(t,r,l);return Object.assign(n,t),n},Dl=e=>e?yu(e):yu,fr=Dl(e=>({activeModal:null,modalData:null,openModal:(t,n=null)=>e({activeModal:t,modalData:n}),closeModal:()=>e({activeModal:null,modalData:null})})),Sm={};function km(e,t){let n;try{n=e()}catch{return}return{getItem:l=>{var o;const s=u=>u===null?null:JSON.parse(u,void 0),i=(o=n.getItem(l))!=null?o:null;return i instanceof Promise?i.then(s):s(i)},setItem:(l,o)=>n.setItem(l,JSON.stringify(o,void 0)),removeItem:l=>n.removeItem(l)}}const or=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then(r){return or(r)(n)},catch(r){return this}}}catch(n){return{then(r){return this},catch(r){return or(r)(n)}}}},Em=(e,t)=>(n,r,l)=>{let o={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:k=>k,version:0,merge:(k,d)=>({...d,...k}),...t},s=!1;const i=new Set,u=new Set;let c;try{c=o.getStorage()}catch{}if(!c)return e((...k)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),n(...k)},r,l);const m=or(o.serialize),y=()=>{const k=o.partialize({...r()});let d;const a=m({state:k,version:o.version}).then(p=>c.setItem(o.name,p)).catch(p=>{d=p});if(d)throw d;return a},h=l.setState;l.setState=(k,d)=>{h(k,d),y()};const g=e((...k)=>{n(...k),y()},r,l);let v;const w=()=>{var k;if(!c)return;s=!1,i.forEach(a=>a(r()));const d=((k=o.onRehydrateStorage)==null?void 0:k.call(o,r()))||void 0;return or(c.getItem.bind(c))(o.name).then(a=>{if(a)return o.deserialize(a)}).then(a=>{if(a)if(typeof a.version=="number"&&a.version!==o.version){if(o.migrate)return o.migrate(a.state,a.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return a.state}).then(a=>{var p;return v=o.merge(a,(p=r())!=null?p:g),n(v,!0),y()}).then(()=>{d==null||d(v,void 0),s=!0,u.forEach(a=>a(v))}).catch(a=>{d==null||d(void 0,a)})};return l.persist={setOptions:k=>{o={...o,...k},k.getStorage&&(c=k.getStorage())},clearStorage:()=>{c==null||c.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>w(),hasHydrated:()=>s,onHydrate:k=>(i.add(k),()=>{i.delete(k)}),onFinishHydration:k=>(u.add(k),()=>{u.delete(k)})},w(),v||g},Cm=(e,t)=>(n,r,l)=>{let o={storage:km(()=>localStorage),partialize:w=>w,version:0,merge:(w,k)=>({...k,...w}),...t},s=!1;const i=new Set,u=new Set;let c=o.storage;if(!c)return e((...w)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),n(...w)},r,l);const m=()=>{const w=o.partialize({...r()});return c.setItem(o.name,{state:w,version:o.version})},y=l.setState;l.setState=(w,k)=>{y(w,k),m()};const h=e((...w)=>{n(...w),m()},r,l);l.getInitialState=()=>h;let g;const v=()=>{var w,k;if(!c)return;s=!1,i.forEach(a=>{var p;return a((p=r())!=null?p:h)});const d=((k=o.onRehydrateStorage)==null?void 0:k.call(o,(w=r())!=null?w:h))||void 0;return or(c.getItem.bind(c))(o.name).then(a=>{if(a)if(typeof a.version=="number"&&a.version!==o.version){if(o.migrate)return[!0,o.migrate(a.state,a.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,a.state];return[!1,void 0]}).then(a=>{var p;const[x,E]=a;if(g=o.merge(E,(p=r())!=null?p:h),n(g,!0),x)return m()}).then(()=>{d==null||d(g,void 0),g=r(),s=!0,u.forEach(a=>a(g))}).catch(a=>{d==null||d(void 0,a)})};return l.persist={setOptions:w=>{o={...o,...w},w.storage&&(c=w.storage)},clearStorage:()=>{c==null||c.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>v(),hasHydrated:()=>s,onHydrate:w=>(i.add(w),()=>{i.delete(w)}),onFinishHydration:w=>(u.add(w),()=>{u.delete(w)})},o.skipHydration||v(),g||h},Nm=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?((Sm?"production":void 0)!=="production"&&console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),Em(e,t)):Cm(e,t),Gc=Nm;let Mr;const _m=new Uint8Array(16);function jm(){if(!Mr&&(Mr=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!Mr))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Mr(_m)}const ee=[];for(let e=0;e<256;++e)ee.push((e+256).toString(16).slice(1));function Pm(e,t=0){return ee[e[t+0]]+ee[e[t+1]]+ee[e[t+2]]+ee[e[t+3]]+"-"+ee[e[t+4]]+ee[e[t+5]]+"-"+ee[e[t+6]]+ee[e[t+7]]+"-"+ee[e[t+8]]+ee[e[t+9]]+"-"+ee[e[t+10]]+ee[e[t+11]]+ee[e[t+12]]+ee[e[t+13]]+ee[e[t+14]]+ee[e[t+15]]}const Tm=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),vu={randomUUID:Tm};function Yc(e,t,n){if(vu.randomUUID&&!e)return vu.randomUUID();e=e||{};const r=e.random||(e.rng||jm)();return r[6]=r[6]&15|64,r[8]=r[8]&63|128,Pm(r)}const St=Dl(e=>({toasts:[],addToast:t=>{const n={...t,id:Date.now()};e(r=>({toasts:[...r.toasts,n]})),setTimeout(()=>{e(r=>({toasts:r.toasts.filter(l=>l.id!==n.id)}))},t.duration||5e3)},removeToast:t=>{e(n=>({toasts:n.toasts.filter(r=>r.id!==t)}))}})),ae={THEME_BRIGHTNESS_THRESHOLD:8421504,DEFAULT_OLLAMA_URL:"http://localhost:11434",OPENROUTER_REFERRER_URL:"https://sahai.com/cep",OPENROUTER_APP_TITLE:"SahAI CEP Extension",HEALTH_CHECK_INTERVAL_MS:3e4,MODEL_CACHE_DURATION_MS:36e5,DARK_THEME:{BG_COLOR:"#323232",TEXT_COLOR:"#F0F0F0",SECONDARY_BG_COLOR:"#3C3C3C",BORDER_COLOR:"#4A4A4A",SCROLLBAR_THUMB_COLOR:"#555555",SCROLLBAR_TRACK_COLOR:"#323232"},LIGHT_THEME:{BG_COLOR:"#F5F5F5",TEXT_COLOR:"#1a1a1a",SECONDARY_BG_COLOR:"#EAEAEA",BORDER_COLOR:"#D3D3D3",SCROLLBAR_THUMB_COLOR:"#C1C1C1",SCROLLBAR_TRACK_COLOR:"#F5F5F5"},ENDPOINTS:{OPENAI:"https://api.openai.com/v1",GROQ:"https://api.groq.com/openai/v1",DEEPSEEK:"https://api.deepseek.com",ANTHROPIC:"https://api.anthropic.com/v1",GOOGLE_GEMINI:"https://generativelanguage.googleapis.com/v1beta/models",OPENROUTER:"https://openrouter.ai/api/v1"},STORAGE_KEYS:{SETTINGS:"sahai-settings-storage"}},Dt={log:(...e)=>{},warn:(...e)=>{},error:(...e)=>{console.error(...e)},debug:(...e)=>{},info:(...e)=>{}},Xc={openai:{models:[]},anthropic:{models:[]},google:{models:[]},groq:{models:[]},deepseek:{models:[]},openrouter:{models:[]},ollama:{baseURL:"http://localhost:11434",models:[]}},et=Dl()(Gc((e,t)=>({providers:Xc,selectedProvider:"openai",selectedModel:"",theme:"auto",adobeTheme:null,modelCache:new Map,setProviderApiKey:(n,r)=>{us(n,r),t().refreshProviderModels(n)},setOllamaBaseUrl:n=>{e(r=>({providers:{...r.providers,ollama:{...r.providers.ollama,baseURL:n}}}))},setSelectedProvider:n=>{e({selectedProvider:n,selectedModel:""}),t().providers[n].models.length===0&&t().refreshProviderModels(n)},setSelectedModel:n=>e({selectedModel:n}),setTheme:(n,r)=>e({theme:n,...r&&{adobeTheme:r}}),applyTheme:()=>{const{theme:n,adobeTheme:r}=t(),l=document.documentElement;if(n==="auto"&&r||n!=="auto"){const s=n==="dark"||r&&parseInt(r.backgroundColor.substring(1),16)<ae.THEME_BRIGHTNESS_THRESHOLD?ae.DARK_THEME:ae.LIGHT_THEME;l.style.setProperty("--adobe-bg-color",s.BG_COLOR),l.style.setProperty("--adobe-text-color",s.TEXT_COLOR),l.style.setProperty("--adobe-secondary-bg-color",s.SECONDARY_BG_COLOR),l.style.setProperty("--adobe-border-color",s.BORDER_COLOR),l.style.setProperty("--adobe-scrollbar-thumb-color",s.SCROLLBAR_THUMB_COLOR),l.style.setProperty("--adobe-scrollbar-track-color",s.SCROLLBAR_TRACK_COLOR)}},refreshProviderModels:async n=>{const{modelCache:r}=t(),l=r.get(n);if(l&&Date.now()-l.timestamp<ae.MODEL_CACHE_DURATION_MS){e(o=>({providers:{...o.providers,[n]:{...o.providers[n],models:l.models}}}));return}try{Dt.log(`Fetching fresh models for ${n}`);const s=await pr(n).getModels();e(i=>({providers:{...i.providers,[n]:{...i.providers[n],models:s}},modelCache:r.set(n,{models:s,timestamp:Date.now()})})),!t().selectedModel&&s.length>0&&t().setSelectedModel(s[0].id)}catch(o){Dt.error(`Failed to fetch models for ${n}:`,o),St.getState().addToast({message:`Could not fetch models for ${n}. Check API key and connection.`,type:"error"}),e(s=>({providers:{...s.providers,[n]:{...s.providers[n],models:[]}}}))}}}),{name:ae.STORAGE_KEYS.SETTINGS,partialize:e=>({selectedProvider:e.selectedProvider,selectedModel:e.selectedModel,theme:e.theme,providers:{ollama:e.providers.ollama}})}));Object.keys(Xc).forEach(e=>{Ye(e)});let on;const Lm=()=>{typeof CSInterface<"u"&&(on=new CSInterface,Jc())},Rm=e=>new Promise((t,n)=>{if(!on){n("Not in a CEP environment.");return}on.evalScript(e,r=>{try{t(JSON.parse(r))}catch{t(r)}})}),Jc=()=>{if(!on)return;const e=on.getThemeInformation(),t={baseFontFamily:e.baseFontFamily,baseFontSize:e.baseFontSize,baseFontColor:`#${e.baseFontColor.color.hex}`,backgroundColor:`#${e.panelBackgroundColor.color.hex}`},n=zm(t.backgroundColor);et.getState().setTheme(n,t),on.addEventListener("com.adobe.csxs.events.ThemeColorChanged",()=>Jc())},zm=e=>{const t=e.substring(1),n=parseInt(t.substring(0,2),16),r=parseInt(t.substring(2,4),16),l=parseInt(t.substring(4,6),16);return(n*299+r*587+l*114)/1e3>125?"light":"dark"},us=(e,t)=>{try{const n=btoa(t);localStorage.setItem(`sahai_api_key_${e}`,n)}catch(n){console.error("Failed to store credential:",n)}},Ye=e=>{try{const t=localStorage.getItem(`sahai_api_key_${e}`);return t?atob(t):null}catch(t){return console.error("Failed to retrieve credential:",t),null}};class Yr{constructor(t,n){this.providerId=t,this.baseUrl=n}async getModels(){if(!Ye(this.providerId))return[];const n=await this.makeRequest("/models");return(n.data||n).map(l=>({id:l.id,name:l.id,description:`Owned by ${l.owned_by||"unknown"}`,contextLength:l.context_window||l.context_length})).sort((l,o)=>l.name.localeCompare(o.name))}async*chat(t,n){if(!Ye(this.providerId))throw new Error(`API key for ${this.providerId} not found.`);const l=JSON.stringify({model:n,messages:t.map(({role:s,content:i})=>({role:s,content:i})),stream:!0}),o=await this.makeRequest("/chat/completions",{method:"POST",body:l},!0);yield*this.processStream(o)}async makeRequest(t,n={},r=!1){var i;const l=Ye(this.providerId),o=new Headers(n.headers||{});o.set("Content-Type","application/json"),o.set("Authorization",`Bearer ${l}`);const s=await fetch(`${this.baseUrl}${t}`,{...n,headers:o});if(!s.ok){const u=await s.json().catch(()=>({}));throw new Error(`API Error (${s.status}): ${((i=u.error)==null?void 0:i.message)||s.statusText}`)}return r?s:s.json()}async*processStream(t){var l,o,s;const n=(l=t.body)==null?void 0:l.getReader();if(!n)throw new Error("Failed to read stream.");const r=new TextDecoder;for(;;){const{done:i,value:u}=await n.read();if(i)break;const m=r.decode(u).split(`

`);for(const y of m)if(y.startsWith("data: ")){const h=y.substring(6);if(h.trim()==="[DONE]")return;try{const v=(s=(o=JSON.parse(h).choices[0])==null?void 0:o.delta)==null?void 0:s.content;v&&(yield v)}catch{Dt.error("Error parsing stream data chunk:",h)}}}}}class Om{constructor(){yr(this,"BASE_URL",ae.ENDPOINTS.ANTHROPIC)}async getModels(){return Promise.resolve([{id:"claude-3-opus-20240229",name:"Claude 3 Opus",contextLength:2e5},{id:"claude-3-sonnet-20240229",name:"Claude 3 Sonnet",contextLength:2e5},{id:"claude-3-haiku-20240307",name:"Claude 3 Haiku",contextLength:2e5},{id:"claude-2.1",name:"Claude 2.1",contextLength:2e5},{id:"claude-2.0",name:"Claude 2.0",contextLength:1e5}])}async*chat(t,n){var m,y,h;const r=Ye("anthropic");if(!r)throw new Error("API key for Anthropic not found.");const l=(m=t.find(g=>g.role==="system"))==null?void 0:m.content,o=t.filter(g=>g.role!=="system"),s=JSON.stringify({model:n,messages:o.map(({role:g,content:v})=>({role:g,content:v})),...l&&{system:l},stream:!0,max_tokens:4096}),i=await fetch(`${this.BASE_URL}/messages`,{method:"POST",headers:{"Content-Type":"application/json","x-api-key":r,"anthropic-version":"2023-06-01"},body:s});if(!i.ok){const g=await i.json().catch(()=>({}));throw new Error(`API Error (${i.status}): ${((y=g.error)==null?void 0:y.message)||i.statusText}`)}const u=(h=i.body)==null?void 0:h.getReader();if(!u)throw new Error("Failed to read stream.");const c=new TextDecoder;for(;;){const{done:g,value:v}=await u.read();if(g)break;const k=c.decode(v).split(`
`);for(const d of k)if(d.startsWith("data: "))try{const a=JSON.parse(d.substring(6));a.type==="content_block_delta"&&a.delta.type==="text_delta"&&(yield a.delta.text)}catch{Dt.error("Error parsing Anthropic stream chunk:",d)}}}}class Mm{constructor(){yr(this,"BASE_URL",ae.ENDPOINTS.GOOGLE_GEMINI)}async getModels(){const t=Ye("google");return t?(await(await fetch(`${this.BASE_URL}?key=${t}`)).json()).models.filter(l=>l.supportedGenerationMethods.includes("generateContent")).map(l=>({id:l.name,name:l.displayName,description:l.description,contextLength:l.inputTokenLimit})).sort((l,o)=>l.name.localeCompare(o.name)):[]}async*chat(t,n){var m,y,h,g,v,w,k;const r=Ye("google");if(!r)throw new Error("API key for Google Gemini not found.");const l=t.map(d=>({role:d.role==="assistant"?"model":"user",parts:[{text:d.content}]})),o=JSON.stringify({contents:l}),s=n.split("/").pop(),i=await fetch(`${this.BASE_URL}/${s}:streamGenerateContent?key=${r}&alt=sse`,{method:"POST",headers:{"Content-Type":"application/json"},body:o});if(!i.ok){const d=await i.json().catch(()=>({}));throw new Error(`API Error (${i.status}): ${((m=d.error)==null?void 0:m.message)||i.statusText}`)}const u=(y=i.body)==null?void 0:y.getReader();if(!u)throw new Error("Failed to read stream.");const c=new TextDecoder;for(;;){const{done:d,value:a}=await u.read();if(d)break;const x=c.decode(a).split(`
`);for(const E of x)if(E.startsWith("data: "))try{const _=(k=(w=(v=(g=(h=JSON.parse(E.substring(6)).candidates)==null?void 0:h[0])==null?void 0:g.content)==null?void 0:v.parts)==null?void 0:w[0])==null?void 0:k.text;_&&(yield _)}catch{}}}}class Im extends Yr{constructor(){super("openrouter",ae.ENDPOINTS.OPENROUTER)}async makeRequest(t,n={},r=!1){var i;const l=Ye(this.providerId),o=new Headers(n.headers||{});o.set("Content-Type","application/json"),o.set("Authorization",`Bearer ${l}`),o.set("HTTP-Referer",ae.OPENROUTER_REFERRER_URL),o.set("X-Title",ae.OPENROUTER_APP_TITLE);const s=await fetch(`${this.baseUrl}${t}`,{...n,headers:o});if(!s.ok){const u=await s.json().catch(()=>({}));throw new Error(`API Error (${s.status}): ${((i=u.error)==null?void 0:i.message)||s.statusText}`)}return r?s:s.json()}}class Dm{constructor(t){this.baseUrl=t}async getModels(){try{const t=await fetch(`${this.baseUrl}/api/tags`);return t.ok?(await t.json()).models.map(r=>({id:r.name,name:r.name,description:`Size: ${(r.size/1e9).toFixed(2)} GB`})).sort((r,l)=>r.name.localeCompare(l.name)):[]}catch(t){return Dt.error("Failed to connect to Ollama:",t),[]}}async*chat(t,n){var i,u;const r=JSON.stringify({model:n,messages:t.map(({role:c,content:m})=>({role:c,content:m})),stream:!0}),l=await fetch(`${this.baseUrl}/api/chat`,{method:"POST",headers:{"Content-Type":"application/json"},body:r});if(!l.ok){const c=await l.json().catch(()=>({}));throw new Error(`Ollama Error (${l.status}): ${c.error||l.statusText}`)}const o=(i=l.body)==null?void 0:i.getReader();if(!o)throw new Error("Failed to read stream.");const s=new TextDecoder;for(;;){const{done:c,value:m}=await o.read();if(c)break;const h=s.decode(m).split(`
`);for(const g of h)if(g.trim())try{const v=JSON.parse(g);if((u=v.message)!=null&&u.content&&(yield v.message.content),v.done)return}catch{Dt.error("Error parsing Ollama stream chunk:",g)}}}}function pr(e,t){const n=et.getState().providers[e];switch(e){case"openai":return new Yr(e,ae.ENDPOINTS.OPENAI);case"groq":return new Yr(e,ae.ENDPOINTS.GROQ);case"deepseek":return new Yr(e,ae.ENDPOINTS.DEEPSEEK);case"anthropic":return new Om;case"google":return new Mm;case"openrouter":return new Im;case"ollama":const r=(t==null?void 0:t.baseURL)||(n==null?void 0:n.baseURL)||ae.DEFAULT_OLLAMA_URL;return new Dm(r);default:const l=e;throw new Error(`Provider ${l} not implemented.`)}}const gu=()=>({id:Yc(),title:"New Chat",messages:[],createdAt:new Date().toISOString()}),gn=Dl()(Gc((e,t)=>({conversations:{},currentConversationId:null,isLoading:!1,addMessage:(n,r)=>{const l={...n,id:Yc(),timestamp:new Date().toISOString(),status:"sent"};return e(o=>{const s=o.conversations[r];return{conversations:{...o.conversations,[r]:{...s,messages:[...s.messages,l]}}}}),l.id},updateMessageStatus:(n,r)=>{e(l=>{const{currentConversationId:o,conversations:s}=l;if(!o||!s[o])return{};const i=s[o].messages,u=i.findIndex(m=>m.id===n);if(u===-1)return{};const c=[...i];return c[u]={...c[u],status:r},{conversations:{...l.conversations,[o]:{...s[o],messages:c}}}})},appendTokenToMessage:(n,r)=>{e(l=>{const{currentConversationId:o,conversations:s}=l;if(!o||!s[o])return{};const i=s[o].messages,u=i.findIndex(y=>y.id===n);if(u===-1)return{};const c=[...i],m=c[u];return c[u]={...m,content:m.content+r},{conversations:{...l.conversations,[o]:{...s[o],messages:c}}}})},sendChatMessage:async(n,r,l)=>{let{currentConversationId:o}=t();if(!o){const i=gu();e(u=>({conversations:{...u.conversations,[i.id]:i},currentConversationId:i.id})),o=i.id}t().addMessage({role:"user",content:n},o);const s=t().addMessage({role:"assistant",content:""},o);t().updateMessageStatus(s,"streaming"),e({isLoading:!0});try{const i=pr(r),u=t().conversations[o].messages.slice(0,-1),c=i.chat(u,l);for await(const m of c)t().appendTokenToMessage(s,m);t().updateMessageStatus(s,"sent")}catch(i){t().updateMessageStatus(s,"error"),t().appendTokenToMessage(s,`**Error:** ${i.message}`),St.getState().addToast({message:i.message,type:"error"})}finally{e({isLoading:!1})}},retryLastUserMessage:async()=>{const{currentConversationId:n,conversations:r}=t();if(!n||!r[n])return;const l=r[n].messages,o=l.map(m=>m.role).lastIndexOf("assistant");if(o===-1||l[o].status!=="error")return;const s=l[o-1];if((s==null?void 0:s.role)!=="user")return;const i=l.slice(0,o);e(m=>({conversations:{...m.conversations,[n]:{...r[n],messages:i}}}));const{selectedProvider:u,selectedModel:c}=et.getState();t().sendChatMessage(s.content,u,c)},startNewConversation:()=>{const n=gu();e(r=>({conversations:{...r.conversations,[n.id]:n},currentConversationId:n.id}))},setCurrentConversationId:n=>e({currentConversationId:n}),clearAllConversations:()=>e({conversations:{},currentConversationId:null})}),{name:"sahai-chat-storage"})),Am=()=>{const e=et(c=>c.selectedProvider),t=fr(c=>c.openModal),[n,r]=T.useState("idle"),[l,o]=T.useState(null),s=T.useCallback(async()=>{if(!e){r("idle");return}r("loading");const c=Date.now();try{await pr(e).getModels(),o(Date.now()-c),r("ok")}catch{r("error"),o(null)}},[e]);T.useEffect(()=>{s();const c=setInterval(s,ae.HEALTH_CHECK_INTERVAL_MS);return()=>clearInterval(c)},[s]);const i={idle:"bg-gray-400",loading:"bg-yellow-500 animate-pulse",ok:"bg-green-500",error:"bg-red-500"},u={idle:"Status: Idle",loading:"Status: Checking connection...",ok:`Status: Connected | Latency: ${l??"N/A"}ms. Click for details.`,error:"Status: Connection failed. Click for details."};return f.jsx("div",{className:"flex items-center gap-2 cursor-pointer",title:u[n],onClick:()=>t("status"),children:n==="loading"?f.jsx(dr,{size:14,className:"animate-spin text-yellow-500"}):f.jsx("div",{className:`w-3 h-3 rounded-full ${i[n]}`})})},Fm=({options:e,value:t,onChange:n,placeholder:r="Select a model...",disabled:l=!1})=>{const[o,s]=T.useState(!1),[i,u]=T.useState(""),[c,m]=T.useState(-1),y=T.useRef(null),h=T.useRef(null),g=T.useMemo(()=>e.find(d=>d.id===t),[e,t]),v=T.useMemo(()=>e.filter(d=>d.name.toLowerCase().includes(i.toLowerCase())||d.id.toLowerCase().includes(i.toLowerCase())),[e,i]);T.useEffect(()=>{const d=a=>{y.current&&!y.current.contains(a.target)&&s(!1)};return document.addEventListener("mousedown",d),()=>document.removeEventListener("mousedown",d)},[]),T.useEffect(()=>{const d=a=>{if(o)switch(a.key){case"ArrowDown":a.preventDefault(),m(p=>Math.min(p+1,v.length-1));break;case"ArrowUp":a.preventDefault(),m(p=>Math.max(p-1,0));break;case"Enter":a.preventDefault(),c>=0&&v[c]&&w(v[c]);break;case"Escape":s(!1);break}};return window.addEventListener("keydown",d),()=>window.removeEventListener("keydown",d)},[o,c,v]);const w=d=>{n(d.id),u(""),s(!1)},k=()=>{l||(s(!0),setTimeout(()=>{var d;return(d=h.current)==null?void 0:d.focus()},0))};return f.jsxs("div",{ref:y,className:"relative w-full",children:[f.jsxs("button",{onClick:k,disabled:l,className:"flex items-center justify-between w-full px-2 py-1 text-left bg-adobe-secondary rounded border border-transparent hover:border-adobe disabled:opacity-50",children:[f.jsx("span",{className:"truncate text-xs",children:(g==null?void 0:g.name)||r}),f.jsx(Ic,{size:14,className:"text-gray-400"})]}),o&&f.jsxs("div",{className:"absolute z-10 w-full mt-1 bg-adobe-bg border border-adobe rounded-md shadow-lg max-h-60 overflow-y-auto",children:[f.jsx("div",{className:"p-2 border-b border-adobe sticky top-0 bg-adobe-bg",children:f.jsxs("div",{className:"relative",children:[f.jsx(Vp,{size:14,className:"absolute left-2 top-1/2 -translate-y-1/2 text-gray-400"}),f.jsx("input",{ref:h,type:"text",value:i,onChange:d=>u(d.target.value),placeholder:"Search models...",className:"w-full pl-7 pr-2 py-1 bg-adobe-secondary border border-adobe rounded text-xs focus:outline-none"})]})}),f.jsx("ul",{className:"py-1",children:v.length>0?v.map((d,a)=>f.jsx("li",{onClick:()=>w(d),onMouseEnter:()=>m(a),className:`px-3 py-1.5 text-xs cursor-pointer ${a===c?"bg-blue-600 text-white":"hover:bg-adobe-secondary"} ${t===d.id?"font-bold":""}`,children:d.name},d.id)):f.jsx("li",{className:"px-3 py-1.5 text-xs text-gray-500",children:"No results found"})})]})]})},Um=()=>{var i;const e=fr(u=>u.openModal),t=gn(u=>u.startNewConversation),{selectedProvider:n,selectedModel:r,setSelectedModel:l,providers:o}=et(),s=((i=o[n])==null?void 0:i.models)||[];return f.jsxs("header",{className:"flex items-center justify-between p-2 border-b border-adobe bg-adobe-secondary flex-shrink-0 gap-2",children:[f.jsxs("div",{className:"flex items-center gap-2 flex-shrink min-w-0",children:[f.jsx(Am,{}),f.jsxs("div",{className:"flex flex-col gap-1 min-w-0",children:[f.jsx("p",{className:"text-xs font-bold truncate capitalize",children:n}),f.jsx(Fm,{options:s,value:r,onChange:l,placeholder:s.length===0?"No models found":"Select a model",disabled:s.length===0})]})]}),f.jsxs("div",{className:"flex items-center gap-1 flex-shrink-0",children:[f.jsx("button",{onClick:t,className:"p-1.5 rounded hover:bg-adobe-bg","aria-label":"New Chat",children:f.jsx(Bp,{size:16})}),f.jsx("button",{onClick:()=>e("history"),className:"p-1.5 rounded hover:bg-adobe-bg","aria-label":"Chat History",children:f.jsx(Fp,{size:16})}),f.jsx("button",{onClick:()=>e("settings"),className:"p-1.5 rounded hover:bg-adobe-bg","aria-label":"Settings",children:f.jsx(Qp,{size:16})})]})]})};let Ir=null;const $m=["javascript","typescript","tsx","html","css","json","xml","markdown","yaml","scss","less","python","swift","rust","go","java","php","ruby","shell","shellscript"];async function Hm(){return Ir||(Ir=await ed({themes:["github-dark"],langs:$m}),Ir)}async function Bm(e,t){const n=await Hm(),r=n.getLoadedLanguages().includes(t)?t:"txt";return n.codeToHtml(e,{lang:r,theme:"github-dark"})}const Vm=({code:e,language:t})=>{const[n,r]=T.useState(""),[l,o]=T.useState(!1),s=St(c=>c.addToast);T.useEffect(()=>{Bm(e,t).then(r)},[e,t]);const i=()=>{navigator.clipboard.writeText(e),s({message:"Code copied to clipboard",type:"success"})},u=async()=>{try{const c=await Rm(`runCode(${JSON.stringify(e)})`);s({message:`Execution result: ${c.result||c.error}`,type:c.success?"info":"error"})}catch(c){s({message:`Execution failed: ${c.message}`,type:"error"})}};return f.jsxs("div",{className:"bg-[#282c34] rounded-md my-2 text-sm overflow-hidden",children:[f.jsxs("div",{className:"flex justify-between items-center px-3 py-1 bg-gray-700 text-gray-300",children:[f.jsx("span",{className:"font-mono",children:t}),f.jsxs("div",{className:"flex gap-1",children:[f.jsx("button",{onClick:()=>o(!l),className:"p-1 hover:bg-gray-600 rounded",children:l?f.jsx(Ic,{size:14}):f.jsx(zp,{size:14})}),f.jsx("button",{onClick:i,className:"p-1 hover:bg-gray-600 rounded",children:f.jsx(Mp,{size:14})}),f.jsx("button",{className:"p-1 hover:bg-gray-600 rounded",children:f.jsx(Ip,{size:14})}),["javascript","typescript","extendscript","jsx"].includes(t)&&f.jsx("button",{onClick:u,className:"p-1 hover:bg-gray-600 rounded",children:f.jsx(Gp,{size:14})})]})]}),!l&&f.jsx("div",{className:"p-3 overflow-x-auto",dangerouslySetInnerHTML:{__html:n}})]})},Km=({message:e})=>{const t=e.role==="user",{retryLastUserMessage:n}=gn(),r=e.content.split(/(```[\w\s]*\n[\s\S]*?\n```)/g);return f.jsxs("div",{className:`flex items-start gap-3 ${t?"justify-end":"self-start"}`,children:[!t&&f.jsx("div",{className:"flex-shrink-0 w-8 h-8 rounded-full bg-adobe-secondary flex items-center justify-center",children:f.jsx(Rp,{size:18})}),f.jsxs("div",{className:`max-w-[85%] rounded-lg p-3 ${t?"bg-blue-600 text-white":e.status==="error"?"bg-red-100 text-red-800":"bg-adobe-secondary"}`,children:[r.map((l,o)=>{if(l.startsWith("```")){const s=l.match(/```(\w*)\n([\s\S]*?)\n```/);if(s){const i=s[1]||"plaintext",u=s[2]||"";return f.jsx(Vm,{code:u,language:i},o)}}return f.jsx("p",{className:"whitespace-pre-wrap",dangerouslySetInnerHTML:{__html:l.replace(/\*\*(.*?)\*\*/g,"<b>$1</b>")}},o)}),e.status==="error"&&!t&&f.jsx("div",{className:"mt-2 pt-2 border-t border-red-300 flex justify-end",children:f.jsxs("button",{onClick:n,className:"flex items-center gap-2 text-xs font-semibold text-red-700 hover:text-red-900",children:[f.jsx(Ac,{size:12}),"Try again"]})})]}),t&&f.jsx("div",{className:"flex-shrink-0 w-8 h-8 rounded-full bg-adobe-secondary flex items-center justify-center",children:f.jsx(Yp,{size:18})})]})},Wm=()=>f.jsxs("div",{className:"flex items-center gap-2 self-start",children:[f.jsx("div",{className:"flex-shrink-0 w-8 h-8 rounded-full bg-adobe-secondary flex items-center justify-center",children:f.jsx(dr,{size:18,className:"animate-spin"})}),f.jsx("div",{className:"bg-adobe-secondary rounded-lg p-3",children:f.jsxs("div",{className:"flex items-center gap-1.5",children:[f.jsx("span",{className:"h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-75"}),f.jsx("span",{className:"h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-150"}),f.jsx("span",{className:"h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-300"})]})})]}),Qm=()=>{var o;const{conversations:e,currentConversationId:t,isLoading:n}=gn(),r=t?(o=e[t])==null?void 0:o.messages:[],l=T.useRef(null);return T.useEffect(()=>{l.current&&(l.current.scrollTop=l.current.scrollHeight)},[r]),f.jsx("div",{ref:l,className:"flex-grow p-4 overflow-y-auto",children:f.jsxs("div",{className:"flex flex-col gap-4",children:[r&&r.map(s=>f.jsx(Km,{message:s},s.id)),n&&r&&r.length>0&&r[r.length-1].content===""&&f.jsx(Wm,{})]})})},Gm=()=>{const[e,t]=T.useState(""),[n,r]=T.useState(!1),{sendChatMessage:l,isLoading:o}=gn(),{selectedProvider:s,selectedModel:i}=et(),u=St(v=>v.addToast),c=T.useRef(null),m=T.useRef(null),y=()=>{if(e.trim()&&!o){if(!i){u({message:"Please select a model first.",type:"warning"});return}l(e.trim(),s,i),t(""),setTimeout(()=>{var v;return(v=c.current)==null?void 0:v.focus()},0)}},h=v=>{v.key==="Enter"&&!v.shiftKey&&(v.preventDefault(),y())},g=()=>{var w;if(n){(w=m.current)==null||w.stop(),r(!1);return}const v=window.SpeechRecognition||window.webkitSpeechRecognition;if(!v){u({message:"Voice recognition is not supported in this browser.",type:"error"});return}m.current=new v,m.current.continuous=!0,m.current.interimResults=!0,m.current.lang="en-US",m.current.onstart=()=>r(!0),m.current.onend=()=>r(!1),m.current.onerror=k=>{u({message:`Voice recognition error: ${k.error}`,type:"error"}),r(!1)},m.current.onresult=k=>{let d="";for(let a=k.resultIndex;a<k.results.length;++a)k.results[a].isFinal?t(p=>p+k.results[a][0].transcript+" "):d+=k.results[a][0].transcript},m.current.start()};return T.useEffect(()=>{if(c.current){c.current.style.height="auto";const v=c.current.scrollHeight;c.current.style.height=`${v}px`}},[e]),f.jsx("div",{className:"p-2 border-t border-adobe bg-adobe-bg flex-shrink-0",children:f.jsxs("div",{className:"flex items-start gap-2 p-2 rounded-md bg-adobe-secondary",children:[f.jsx("button",{className:"p-1.5 rounded","aria-label":"Attach File",children:f.jsx(Hp,{size:18})}),f.jsx("button",{onClick:g,className:`p-1.5 rounded ${n?"bg-red-500 text-white":""}`,"aria-label":"Voice Input",children:f.jsx($p,{size:18})}),f.jsx("textarea",{ref:c,value:e,onChange:v=>t(v.target.value),onKeyDown:h,placeholder:n?"Listening...":"Type a message or use voice input...",className:"flex-grow bg-transparent focus:outline-none resize-none max-h-48 overflow-y-auto",rows:1,maxLength:4e3,disabled:o}),f.jsx("button",{onClick:y,disabled:o||!e.trim(),className:"p-1.5 rounded bg-blue-600 text-white disabled:bg-gray-500 self-end",children:o?f.jsx(dr,{size:18,className:"animate-spin"}):f.jsx(Kp,{size:18})})]})})},Al=({title:e,children:t})=>{const n=fr(r=>r.closeModal);return T.useEffect(()=>{const r=l=>{l.key==="Escape"&&n()};return window.addEventListener("keydown",r),()=>window.removeEventListener("keydown",r)},[n]),f.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex justify-center items-center z-50",onClick:n,children:f.jsxs("div",{className:"bg-adobe-bg rounded-lg shadow-xl w-full max-w-md m-4 flex flex-col",onClick:r=>r.stopPropagation(),children:[f.jsxs("header",{className:"flex justify-between items-center p-4 border-b border-adobe",children:[f.jsx("h2",{className:"text-lg font-semibold",children:e}),f.jsx("button",{onClick:n,className:"p-1 rounded-full hover:bg-adobe-secondary",children:f.jsx(Hc,{size:20})})]}),f.jsx("div",{className:"p-4 overflow-y-auto",children:t})]})})},Ym=()=>{const{theme:e,setTheme:t}=et(),{clearAllConversations:n}=gn(),r=St(o=>o.addToast),l=()=>{window.confirm("Are you sure you want to delete all chat history? This action cannot be undone.")&&(n(),r({message:"All chat history has been cleared.",type:"success"}))};return f.jsx(Al,{title:"Settings",children:f.jsxs("div",{className:"flex flex-col gap-6",children:[f.jsxs("div",{children:[f.jsx("label",{htmlFor:"theme-select",className:"block text-sm font-medium mb-1",children:"Theme"}),f.jsxs("select",{id:"theme-select",value:e,onChange:o=>t(o.target.value),className:"w-full p-2 rounded bg-adobe-secondary border border-adobe focus:outline-none focus:ring-2 focus:ring-blue-500",children:[f.jsx("option",{value:"auto",children:"Auto (Sync with Adobe)"}),f.jsx("option",{value:"light",children:"Light"}),f.jsx("option",{value:"dark",children:"Dark"})]})]}),f.jsxs("div",{className:"border-t border-adobe pt-4",children:[f.jsx("h4",{className:"text-md font-semibold mb-2",children:"Data Management"}),f.jsxs("button",{onClick:l,className:"w-full flex items-center justify-center gap-2 px-3 py-2 rounded bg-red-600 hover:bg-red-700 text-white text-sm",children:[f.jsx(Uc,{size:16}),"Clear All Chat History"]}),f.jsx("p",{className:"text-xs text-gray-500 mt-2",children:"This will permanently delete all your conversations and messages stored within the extension."})]})]})})},wu=[{id:"openai",name:"OpenAI",icon:f.jsx($t,{size:18})},{id:"anthropic",name:"Anthropic",icon:f.jsx($t,{size:18})},{id:"google",name:"Google Gemini",icon:f.jsx($t,{size:18})},{id:"groq",name:"Groq",icon:f.jsx($t,{size:18})},{id:"deepseek",name:"DeepSeek",icon:f.jsx($t,{size:18})},{id:"openrouter",name:"OpenRouter",icon:f.jsx($t,{size:18})},{id:"ollama",name:"Ollama",icon:f.jsx(Wp,{size:18})}],Xm=({providerId:e,providerName:t})=>{const[n,r]=T.useState(""),[l,o]=T.useState(!1),[s,i]=T.useState(!1),u=St(y=>y.addToast);T.useEffect(()=>{const y=Ye(e);o(!!y),r("")},[e]);const c=()=>{if(!n.trim()){u({message:"API key cannot be empty.",type:"warning"});return}us(e,n.trim()),o(!0),r(""),i(!1),u({message:`${t} API key saved successfully.`,type:"success"})},m=()=>{us(e,""),o(!1),u({message:`${t} API key cleared.`,type:"info"})};return f.jsxs("div",{className:"flex flex-col gap-4",children:[f.jsxs("h3",{className:"text-lg font-semibold",children:[t," Configuration"]}),f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium mb-1",children:"API Key"}),f.jsxs("div",{className:"flex items-center gap-2",children:[f.jsx("input",{type:s?"text":"password",value:n,onChange:y=>r(y.target.value),placeholder:l?"•••••••••••••••• (Key is set)":"Enter your API key",className:"w-full p-2 rounded bg-adobe-secondary border border-adobe focus:outline-none focus:ring-2 focus:ring-blue-500"}),f.jsx("button",{onClick:()=>i(!s),className:"p-2 hover:bg-adobe-secondary rounded",children:s?f.jsx(Dp,{size:18}):f.jsx(Ap,{size:18})})]}),f.jsxs("p",{className:`text-xs mt-1 ${l?"text-green-500":"text-yellow-500"}`,children:["Status: ",l?"API Key is configured":"API Key not set"]})]}),f.jsxs("div",{className:"flex justify-end gap-2",children:[l&&f.jsxs("button",{onClick:m,className:"flex items-center gap-2 px-3 py-2 rounded bg-red-600 hover:bg-red-700 text-white text-sm",children:[f.jsx(Uc,{size:16}),"Clear Key"]}),f.jsxs("button",{onClick:c,disabled:!n.trim(),className:"flex items-center gap-2 px-3 py-2 rounded bg-blue-600 hover:bg-blue-700 text-white text-sm disabled:bg-gray-500",children:[f.jsx(Fc,{size:16}),"Save Key"]})]})]})},Jm=()=>{const{providers:e,setOllamaBaseUrl:t}=et(),[n,r]=T.useState(e.ollama.baseURL||"http://localhost:11434"),[l,o]=T.useState("idle"),s=St(m=>m.addToast),i=()=>{t(n),s({message:"Ollama Base URL updated.",type:"success"})},u=async()=>{o("testing");try{const y=await pr("ollama",{baseURL:n}).getModels();y.length>0?(o("success"),s({message:`Connection successful! Found ${y.length} models.`,type:"success"})):(o("failed"),s({message:"Connection successful, but no models found.",type:"warning"}))}catch{o("failed"),s({message:"Failed to connect to Ollama. Check the URL and ensure Ollama is running.",type:"error"})}},c={idle:null,testing:f.jsx(dr,{size:18,className:"animate-spin text-yellow-500"}),success:f.jsx(ri,{size:18,className:"text-green-500"}),failed:f.jsx(Dc,{size:18,className:"text-red-500"})}[l];return f.jsxs("div",{className:"flex flex-col gap-4",children:[f.jsx("h3",{className:"text-lg font-semibold",children:"Ollama (Local) Configuration"}),f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium mb-1",children:"Ollama Server URL"}),f.jsxs("div",{className:"flex items-center gap-2",children:[f.jsx("input",{type:"text",value:n,onChange:m=>r(m.target.value),placeholder:"e.g., http://localhost:11434",className:"w-full p-2 rounded bg-adobe-secondary border border-adobe focus:outline-none focus:ring-2 focus:ring-blue-500"}),c]})]}),f.jsxs("div",{className:"flex justify-end gap-2",children:[f.jsx("button",{onClick:u,disabled:l==="testing",className:"flex items-center gap-2 px-3 py-2 rounded bg-gray-600 hover:bg-gray-700 text-white text-sm disabled:opacity-50",children:"Test Connection"}),f.jsxs("button",{onClick:i,className:"flex items-center gap-2 px-3 py-2 rounded bg-blue-600 hover:bg-blue-700 text-white text-sm",children:[f.jsx(Fc,{size:16}),"Save URL"]})]})]})},Zm=()=>{const[e,t]=T.useState("openai"),n=()=>{const r=wu.find(l=>l.id===e);return r?r.id==="ollama"?f.jsx(Jm,{}):f.jsx(Xm,{providerId:r.id,providerName:r.name}):null};return f.jsx(Al,{title:"Provider Configuration",children:f.jsxs("div",{className:"flex min-h-[300px]",children:[f.jsx("nav",{className:"w-1/3 border-r border-adobe pr-4",children:f.jsx("ul",{className:"flex flex-col gap-1",children:wu.map(r=>f.jsx("li",{children:f.jsxs("button",{onClick:()=>t(r.id),className:`w-full flex items-center gap-3 text-left p-2 rounded text-sm ${e===r.id?"bg-blue-600 text-white":"hover:bg-adobe-secondary"}`,children:[r.icon,r.name]})},r.id))})}),f.jsx("section",{className:"w-2/3 pl-4",children:n()})]})})},qm=()=>{const{conversations:e,setCurrentConversationId:t}=gn(),{closeModal:n}=fr(),r=Object.values(e),l=o=>{t(o),n()};return f.jsx(Al,{title:"Chat History",children:r.length===0?f.jsx("p",{children:"No chat history yet."}):f.jsx("ul",{className:"flex flex-col gap-2",children:r.map(o=>f.jsxs("li",{onClick:()=>l(o.id),className:"p-2 rounded hover:bg-adobe-secondary cursor-pointer",children:[f.jsx("p",{className:"font-semibold",children:o.title}),f.jsx("p",{className:"text-xs opacity-70",children:new Date(o.createdAt).toLocaleString()})]},o.id))})})},bm=["openai","anthropic","google","groq","deepseek","openrouter","ollama"],eh=({providerId:e})=>{const[t,n]=T.useState({status:"loading"}),r=async()=>{n({status:"loading"});const o=Date.now();try{const i=await pr(e).getModels();i.length>0?n({status:"success",latency:Date.now()-o,modelsCount:i.length}):n({status:"error",error:"Connected, but no models found."})}catch(s){const i=/API key/i.test(s.message);n({status:i?"unconfigured":"error",error:i?"API key not set or invalid.":"Connection failed."})}};T.useEffect(()=>{r()},[e]);const l={loading:f.jsx(dr,{size:16,className:"animate-spin text-yellow-500"}),success:f.jsx(ri,{size:16,className:"text-green-500"}),error:f.jsx(Dc,{size:16,className:"text-red-500"}),unconfigured:f.jsx($c,{size:16,className:"text-orange-500"})};return f.jsxs("tr",{className:"border-b border-adobe",children:[f.jsx("td",{className:"p-2 font-semibold capitalize",children:e}),f.jsx("td",{className:"p-2 text-center",children:l[t.status]}),f.jsx("td",{className:"p-2 text-xs",children:t.latency?`${t.latency} ms`:"N/A"}),f.jsx("td",{className:"p-2 text-xs",children:t.modelsCount??"N/A"}),f.jsx("td",{className:"p-2 text-xs text-gray-400",children:t.error??"OK"}),f.jsx("td",{className:"p-2 text-center",children:f.jsx("button",{onClick:r,title:"Re-check status",children:f.jsx(Ac,{size:14,className:"hover:text-blue-500"})})})]})},th=()=>f.jsx(Al,{title:"System Connection Status",children:f.jsxs("div",{className:"max-h-[60vh] overflow-y-auto",children:[f.jsxs("table",{className:"w-full text-sm text-left",children:[f.jsx("thead",{className:"bg-adobe-secondary sticky top-0",children:f.jsxs("tr",{className:"border-b-2 border-adobe",children:[f.jsx("th",{className:"p-2",children:"Provider"}),f.jsx("th",{className:"p-2 text-center",children:"Status"}),f.jsx("th",{className:"p-2",children:"Latency"}),f.jsx("th",{className:"p-2",children:"Models"}),f.jsx("th",{className:"p-2",children:"Details"}),f.jsx("th",{className:"p-2 text-center",children:"Check"})]})}),f.jsx("tbody",{children:bm.map(e=>f.jsx(eh,{providerId:e},e))})]}),f.jsx("p",{className:"text-xs text-gray-500 mt-4",children:"This panel shows the real-time status of each AI provider. Latency is measured by fetching the model list."})]})}),nh={success:f.jsx(ri,{className:"text-green-500"}),error:f.jsx(Op,{className:"text-red-500"}),warning:f.jsx($c,{className:"text-yellow-500"}),info:f.jsx(Up,{className:"text-blue-500"})},rh={success:"bg-green-100 border-green-400",error:"bg-red-100 border-red-400",warning:"bg-yellow-100 border-yellow-400",info:"bg-blue-100 border-blue-400"},lh=()=>{const{toasts:e,removeToast:t}=St();return e.length?f.jsx("div",{className:"fixed top-4 right-4 z-50 flex flex-col gap-2",children:e.map(n=>f.jsxs("div",{className:`flex items-center gap-3 p-3 rounded-md shadow-lg border ${rh[n.type]}`,children:[nh[n.type],f.jsx("p",{className:"text-sm text-gray-800",children:n.message}),f.jsx("button",{onClick:()=>t(n.id),children:f.jsx(Hc,{size:16,className:"text-gray-500"})})]},n.id))}):null},oh=()=>{const{theme:e,applyTheme:t}=et(),n=fr(l=>l.activeModal);T.useEffect(()=>{t()},[e,t]);const r=()=>{switch(n){case"settings":return f.jsx(Ym,{});case"provider":return f.jsx(Zm,{});case"history":return f.jsx(qm,{});case"status":return f.jsx(th,{});default:return null}};return f.jsxs("div",{className:"flex flex-col h-screen bg-adobe-bg text-adobe-text font-sans antialiased",children:[f.jsx(Um,{}),f.jsxs("main",{className:"flex-grow flex flex-col overflow-hidden",children:[f.jsx(Qm,{}),f.jsx(Gm,{})]}),r(),f.jsx(lh,{})]})};class sh extends T.Component{constructor(){super(...arguments);yr(this,"state",{hasError:!1})}static getDerivedStateFromError(n){return{hasError:!0}}componentDidCatch(n,r){Dt.error("Uncaught error:",n,r),this.setState({error:n})}render(){var n;return this.state.hasError?f.jsxs("div",{className:"p-4 text-red-500 bg-red-100 border border-red-500 rounded-md",children:[f.jsx("h1",{className:"font-bold",children:"Something went wrong."}),f.jsx("p",{children:"An unexpected error occurred. Please try reloading the panel."}),f.jsx("details",{className:"mt-2 text-sm text-gray-700",children:(n=this.state.error)==null?void 0:n.toString()})]}):this.props.children}}Lm();ho.createRoot(document.getElementById("root")).render(f.jsx(Ru.StrictMode,{children:f.jsx(sh,{children:f.jsx(oh,{})})}));
