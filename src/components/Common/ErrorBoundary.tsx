import { Component, ErrorInfo, ReactNode } from 'react';
import { logger } from '../../utils/logger';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
  };

  public static getDerivedStateFromError(_: Error): State {
    return { hasError: true };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    logger.error("Uncaught error:", error, errorInfo);
    this.setState({ error });
  }

  public render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 text-red-500 bg-red-100 border border-red-500 rounded-md">
          <h1 className="font-bold">Something went wrong.</h1>
          <p>An unexpected error occurred. Please try reloading the panel.</p>
          <details className="mt-2 text-sm text-gray-700">
              {this.state.error?.toString()}
          </details>
        </div>
      );
    }

    return this.props.children;
  }
}
