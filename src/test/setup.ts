/**
 * Test setup file for Vitest
 * 
 * This file is automatically loaded before each test file and provides
 * global configuration and utilities for testing.
 */

import { afterEach, vi } from 'vitest';
import { cleanup } from '@testing-library/react';
import '@testing-library/jest-dom/vitest';

// Runs a cleanup after each test case (e.g. clearing jsdom)
afterEach(() => {
  cleanup();
});

// Mock the CSInterface global for CEP functionality
(globalThis as any).CSInterface = class MockCSInterface {
  getThemeInformation() {
    return {
      baseFontFamily: 'Arial',
      baseFontSize: '12px',
      baseFontColor: { color: { hex: '000000' } },
      panelBackgroundColor: { color: { hex: 'F5F5F5' } },
    };
  }

  addEventListener() {
    // Mock implementation
  }

  evalScript() {
    // Mock implementation
  }
};

// Mock localStorage for secure credential storage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
(globalThis as any).localStorage = localStorageMock;
