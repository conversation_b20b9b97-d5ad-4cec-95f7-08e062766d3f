/**
 * Environment-aware logging utility for the SahAI CEP Extension.
 * 
 * This logger automatically disables console output in production builds
 * to prevent information leakage while maintaining error logging for debugging.
 * 
 * Usage:
 * ```typescript
 * import { logger } from '../utils/logger';
 * 
 * logger.log('Debug information');
 * logger.warn('Warning message');
 * logger.error('Error occurred');
 * ```
 */

// Use Vite's environment variable to check for production mode
const isProduction = (import.meta as any).env?.PROD || false;

/**
 * Environment-aware logger that respects production/development modes.
 */
export const logger = {
  /**
   * Log general information (disabled in production)
   */
  log: (...args: any[]) => {
    if (!isProduction) {
      console.log(...args);
    }
  },

  /**
   * Log warning messages (disabled in production)
   */
  warn: (...args: any[]) => {
    if (!isProduction) {
      console.warn(...args);
    }
  },

  /**
   * Log error messages (always enabled for debugging via CEP logs)
   */
  error: (...args: any[]) => {
    // We keep errors in production for debugging via CEP logs
    console.error(...args);
  },

  /**
   * Log debug information (disabled in production)
   */
  debug: (...args: any[]) => {
    if (!isProduction) {
      console.debug(...args);
    }
  },

  /**
   * Log informational messages (disabled in production)
   */
  info: (...args: any[]) => {
    if (!isProduction) {
      console.info(...args);
    }
  },
} as const;

/**
 * Type definition for logger methods
 */
export type Logger = typeof logger;
