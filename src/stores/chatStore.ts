import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { v4 as uuidv4 } from 'uuid';
import { Conversation, Message, ProviderID } from '../types';
import { getProviderApi } from '../services/providerFactory';
import { useToastStore } from './toastStore';
import { useSettingsStore } from './settingsStore';

interface ChatState {
  conversations: Record<string, Conversation>;
  currentConversationId: string | null;
  isLoading: boolean;
  addMessage: (message: Omit<Message, 'id' | 'timestamp'>, conversationId: string) => string;
  updateMessageStatus: (messageId: string, status: Message['status']) => void;
  appendTokenToMessage: (messageId: string, token: string) => void;
  sendChatMessage: (content: string, providerId: ProviderID, modelId: string) => Promise<void>;
  retryLastUserMessage: () => Promise<void>;
  startNewConversation: () => void;
  setCurrentConversationId: (id: string) => void;
  clearAllConversations: () => void;
}

const createNewConversation = (): Conversation => ({
  id: uuidv4(),
  title: 'New Chat',
  messages: [],
  createdAt: new Date().toISOString(),
});

export const useChatStore = create<ChatState>()(
  persist(
    (set, get) => ({
      conversations: {},
      currentConversationId: null,
      isLoading: false,

      addMessage: (messageContent, conversationId) => {
        const newMessage: Message = {
          ...messageContent,
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          status: 'sent',
        };
        set((state) => {
          const conversation = state.conversations[conversationId];
          return {
            conversations: {
              ...state.conversations,
              [conversationId]: {
                ...conversation,
                messages: [...conversation.messages, newMessage],
              },
            },
          };
        });
        return newMessage.id;
      },

      updateMessageStatus: (messageId, status) => {
        set(state => {
            const { currentConversationId, conversations } = state;
            if (!currentConversationId || !conversations[currentConversationId]) return {};
            const messages = conversations[currentConversationId].messages;
            const msgIndex = messages.findIndex(m => m.id === messageId);
            if (msgIndex === -1) return {};

            const updatedMessages = [...messages];
            updatedMessages[msgIndex] = { ...updatedMessages[msgIndex], status };

            return {
                conversations: {
                    ...state.conversations,
                    [currentConversationId]: {
                        ...conversations[currentConversationId],
                        messages: updatedMessages
                    }
                }
            }
        });
      },

      appendTokenToMessage: (messageId, token) => {
          set(state => {
              const { currentConversationId, conversations } = state;
              if (!currentConversationId || !conversations[currentConversationId]) return {};

              const messages = conversations[currentConversationId].messages;
              const msgIndex = messages.findIndex(m => m.id === messageId);
              if (msgIndex === -1) return {};

              const updatedMessages = [...messages];
              const targetMessage = updatedMessages[msgIndex];

              updatedMessages[msgIndex] = { ...targetMessage, content: targetMessage.content + token };

              return {
                  conversations: {
                      ...state.conversations,
                      [currentConversationId]: {
                          ...conversations[currentConversationId],
                          messages: updatedMessages
                      }
                  }
              }
          })
      },

      sendChatMessage: async (content: string, providerId: ProviderID, modelId: string) => {
        let { currentConversationId } = get();

        // If there's no active conversation, create one
        if (!currentConversationId) {
            const newConvo = createNewConversation();
            set(state => ({
                conversations: { ...state.conversations, [newConvo.id]: newConvo },
                currentConversationId: newConvo.id
            }));
            currentConversationId = newConvo.id;
        }

        // Add user message
        get().addMessage({ role: 'user', content }, currentConversationId!);
        const assistantMsgId = get().addMessage({ role: 'assistant', content: '' }, currentConversationId!);
        get().updateMessageStatus(assistantMsgId, 'streaming');
        set({ isLoading: true });

        try {
          // No longer needs to import useSettingsStore here
          const api = getProviderApi(providerId);
          const messages = get().conversations[currentConversationId!].messages.slice(0, -1);

          const stream = api.chat(messages, modelId);
          for await (const chunk of stream) {
            get().appendTokenToMessage(assistantMsgId, chunk);
          }
          get().updateMessageStatus(assistantMsgId, 'sent');
        } catch (error: any) {
            get().updateMessageStatus(assistantMsgId, 'error');
            get().appendTokenToMessage(assistantMsgId, `**Error:** ${error.message}`);
            useToastStore.getState().addToast({ message: error.message, type: 'error' });
        } finally {
            set({ isLoading: false });
        }
      },

      retryLastUserMessage: async () => {
          const { currentConversationId, conversations } = get();
          if (!currentConversationId || !conversations[currentConversationId]) return;

          const messages = conversations[currentConversationId].messages;
          const lastAssistantMsgIndex = messages.map(m => m.role).lastIndexOf('assistant');
          if (lastAssistantMsgIndex === -1 || messages[lastAssistantMsgIndex].status !== 'error') return;

          const userMessageToRetry = messages[lastAssistantMsgIndex -1];
          if (userMessageToRetry?.role !== 'user') return;

          // Remove the failed assistant message
          const updatedMessages = messages.slice(0, lastAssistantMsgIndex);
          set(state => ({
              conversations: {
                  ...state.conversations,
                  [currentConversationId]: { ...conversations[currentConversationId], messages: updatedMessages }
              }
          }));

          // Get current provider and model for retry
          const { selectedProvider, selectedModel } = useSettingsStore.getState();

          // Resend with current provider and model
          get().sendChatMessage(userMessageToRetry.content, selectedProvider as ProviderID, selectedModel);
      },

      startNewConversation: () => {
        const newConvo = createNewConversation();
        set(state => ({
            conversations: { ...state.conversations, [newConvo.id]: newConvo },
            currentConversationId: newConvo.id
        }));
      },

      setCurrentConversationId: (id) => set({ currentConversationId: id }),

      clearAllConversations: () => set({ conversations: {}, currentConversationId: null }),

    }),
    {
      name: 'sahai-chat-storage',
    }
  )
);
